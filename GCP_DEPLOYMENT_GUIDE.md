# GCP Deployment Guide for TrackMyClass

This guide covers deploying the TrackMyClass application to Google Cloud Platform (GCP).

## Prerequisites

1. **Google Cloud SDK**: Install and configure the gcloud CLI
2. **Docker**: For building container images
3. **GCP Project**: Create a GCP project and enable required APIs

## Required GCP APIs

Enable the following APIs in your GCP project:
```bash
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable sqladmin.googleapis.com
gcloud services enable appengine.googleapis.com
```

## Deployment Options

### Option 1: Google Cloud Run (Recommended)

Cloud Run is ideal for containerized applications with automatic scaling.

1. **Configure the deployment script**:
   - Edit `deploy-to-gcp.sh`
   - Update `PROJECT_ID`, `SERVICE_NAME`, and `REGION`

2. **Set up database**:
   - Create a Cloud SQL MySQL instance
   - Update database connection details in the script

3. **Deploy**:
   ```bash
   chmod +x deploy-to-gcp.sh
   ./deploy-to-gcp.sh
   ```

### Option 2: Google App Engine

App Engine provides a fully managed platform.

1. **Configure app.yaml**:
   - Update environment variables
   - Configure database connection for Cloud SQL

2. **Deploy**:
   ```bash
   gcloud app deploy
   ```

### Option 3: Google Cloud Build (CI/CD)

For automated deployments from source control.

1. **Connect your repository** to Cloud Build
2. **Configure cloudbuild.yaml** with your project settings
3. **Set up triggers** for automatic deployment

## Database Setup

### Cloud SQL MySQL

1. **Create instance**:
   ```bash
   gcloud sql instances create trackmyclass-db \
       --database-version=MYSQL_8_0 \
       --tier=db-f1-micro \
       --region=us-central1
   ```

2. **Create database**:
   ```bash
   gcloud sql databases create trackmyclass \
       --instance=trackmyclass-db
   ```

3. **Create user**:
   ```bash
   gcloud sql users create appuser \
       --instance=trackmyclass-db \
       --password=your-secure-password
   ```

## Authentication Setup

### Google Identity Platform

1. **Enable Identity Platform** in GCP Console
2. **Configure OAuth providers** (Google, etc.)
3. **Update application properties** with OAuth configuration
4. **Set environment variables** in your deployment

Example OAuth configuration:
```properties
spring.security.oauth2.client.registration.google.client-id=your-client-id
spring.security.oauth2.client.registration.google.client-secret=your-client-secret
spring.security.oauth2.client.registration.google.scope=openid,profile,email
```

## Environment Variables

Configure these in your GCP service:

- `SPRING_PROFILES_ACTIVE=prod`
- `DB_HOST=your-cloud-sql-connection-name`
- `DB_PORT=3306`
- `DB_NAME=trackmyclass`
- `DB_USERNAME=your-db-username`
- `DB_PASSWORD=your-db-password`
- `OAUTH_CLIENT_ID=your-oauth-client-id`
- `OAUTH_CLIENT_SECRET=your-oauth-client-secret`
- `OAUTH_ISSUER_URI=your-oauth-issuer-uri`

## File Storage

The application is configured to use database storage by default. For production, consider:

1. **Google Cloud Storage**: Implement a GCS storage provider
2. **Persistent Disks**: For App Engine file storage

## Monitoring and Logging

GCP provides built-in monitoring:

- **Cloud Logging**: Application logs
- **Cloud Monitoring**: Performance metrics
- **Error Reporting**: Exception tracking

## Security Considerations

1. **Use IAM roles** for service authentication
2. **Enable HTTPS** (automatic with Cloud Run/App Engine)
3. **Configure firewall rules** if needed
4. **Use Secret Manager** for sensitive configuration

## Cost Optimization

1. **Use appropriate instance sizes**
2. **Configure auto-scaling** based on traffic
3. **Monitor usage** with Cloud Billing
4. **Use preemptible instances** for development

## Troubleshooting

1. **Check logs**: `gcloud logs read`
2. **Verify environment variables**
3. **Test database connectivity**
4. **Check OAuth configuration**

For more detailed information, refer to the official GCP documentation.