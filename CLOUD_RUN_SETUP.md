# Cloud Run Deployment with H2 Database

## Quick Deploy

1. **Enable Cloud Build API**:
```bash
gcloud services enable cloudbuild.googleapis.com run.googleapis.com
```

2. **Deploy**:
```bash
chmod +x deploy-to-gcp.sh
./deploy-to-gcp.sh YOUR_PROJECT_ID
```

## Database

Uses H2 file database stored in `/tmp/proddb` - no external database setup required.

## Add OAuth (Optional)

After deployment, add OAuth configuration:
```bash
gcloud run services update trackmyclass \
    --region=us-central1 \
    --set-env-vars="OAUTH_CLIENT_ID=your-client-id,OAUTH_CLIENT_SECRET=your-secret"
```

## Check Status

```bash
gcloud run services describe trackmyclass --region=us-central1
```