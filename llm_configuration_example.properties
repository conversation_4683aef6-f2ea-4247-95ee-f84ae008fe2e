# LLM Configuration Example
# Copy these properties to your application.properties or application.yml

# Primary LLM Provider (OPENAI, GROQ, ANTHROPIC, GOOGLE, HUGGINGFACE, OLLAMA)
llm.primary.provider=GROQ

# ==================== OpenAI Configuration ====================
llm.openai.enabled=false
llm.openai.api.key=${OPENAI_API_KEY:}
llm.openai.model=gpt-3.5-turbo
# Optional: Organization ID for OpenAI
# llm.openai.organization.id=${OPENAI_ORG_ID:}

# ==================== Groq Configuration ====================
llm.groq.enabled=true
llm.groq.api.key=${GROQ_API_KEY:}
llm.groq.model=mixtral-8x7b-32768
# Alternative Groq models:
# llm.groq.model=llama2-70b-4096
# llm.groq.model=gemma-7b-it
# llm.groq.model=llama3-8b-8192
# llm.groq.model=llama3-70b-8192

# ==================== Anthropic Claude Configuration ====================
llm.anthropic.enabled=false
llm.anthropic.api.key=${ANTHROPIC_API_KEY:}
llm.anthropic.model=claude-3-sonnet-20240229
llm.anthropic.version=2023-06-01
# Alternative Anthropic models:
# llm.anthropic.model=claude-3-opus-20240229
# llm.anthropic.model=claude-3-haiku-20240307
# llm.anthropic.model=claude-2.1

# ==================== Google Gemini Configuration ====================
llm.google.enabled=false
llm.google.api.key=${GOOGLE_API_KEY:}
llm.google.model=gemini-pro
# llm.google.project.id=${GOOGLE_PROJECT_ID:}

# ==================== Hugging Face Configuration ====================
llm.huggingface.enabled=false
llm.huggingface.api.key=${HUGGINGFACE_API_KEY:}
llm.huggingface.model=microsoft/DialoGPT-medium

# ==================== Ollama Configuration (Local with DeepSeek) ====================
llm.ollama.enabled=false
llm.ollama.api.url=http://localhost:11434/api/generate
llm.ollama.model=deepseek-coder:6.7b
# Alternative DeepSeek models:
# llm.ollama.model=deepseek-coder:1.3b
# llm.ollama.model=deepseek-coder:33b
# llm.ollama.model=deepseek-llm:7b
# llm.ollama.model=deepseek-llm:67b
# Other Ollama models:
# llm.ollama.model=llama3:8b
# llm.ollama.model=mistral:7b
# llm.ollama.model=codellama:7b

# ==================== Environment Variables ====================
# Set these environment variables in your system:
# export GROQ_API_KEY="gsk_your_groq_api_key_here"
# export OPENAI_API_KEY="sk-your_openai_api_key_here"
# export ANTHROPIC_API_KEY="sk-ant-your_anthropic_api_key_here"
# export GOOGLE_API_KEY="your_google_api_key_here"
# export HUGGINGFACE_API_KEY="hf_your_huggingface_api_key_here"

# ==================== Usage Instructions ====================
# 1. Enable your preferred provider by setting llm.{provider}.enabled=true
# 2. Set the API key for your chosen provider
# 3. Choose the model you want to use
# 4. Set the primary provider to your preferred choice
# 5. The system will automatically fallback to other enabled providers if the primary fails

# ==================== Provider Switching ====================
# To switch from OpenAI to Groq:
# 1. Set llm.groq.enabled=true
# 2. Set llm.openai.enabled=false
# 3. Set llm.primary.provider=GROQ
# 4. Ensure GROQ_API_KEY environment variable is set

# To temporarily disable all AI and use fallback questions:
# Set all providers to enabled=false

# ==================== Ollama Setup Instructions ====================
# 1. Install Ollama: https://ollama.ai/
# 2. Pull DeepSeek models:
#    ollama pull deepseek-coder:6.7b
#    ollama pull deepseek-coder:1.3b
#    ollama pull deepseek-llm:7b
# 3. Start Ollama service:
#    ollama serve
# 4. Test model:
#    ollama run deepseek-coder:6.7b "Hello, can you help with coding questions?"
# 5. Enable in configuration:
#    llm.ollama.enabled=true
#    llm.primary.provider=OLLAMA

# ==================== DeepSeek Model Recommendations ====================
# deepseek-coder:6.7b    - Best balance of speed and quality for coding questions
# deepseek-coder:1.3b    - Fastest, good for simple questions
# deepseek-coder:33b     - Highest quality, slower (requires more RAM)
# deepseek-llm:7b        - General purpose, good for all subjects
# deepseek-llm:67b       - Highest quality general model (requires significant RAM)

# ==================== API Key Security ====================
# Never commit API keys to version control
# Use environment variables or secure configuration management
# Rotate API keys regularly
# Monitor API usage and costs
