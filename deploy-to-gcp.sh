#!/bin/bash

# Cloud Run deployment script
# Usage: ./deploy-to-gcp.sh [PROJECT_ID]

PROJECT_ID=${1:-$(gcloud config get-value project)}
SERVICE_NAME="trackmyclass"
REGION="us-central1"
IMAGE_NAME="gcr.io/${PROJECT_ID}/${SERVICE_NAME}"

if [ -z "$PROJECT_ID" ]; then
    echo "Error: PROJECT_ID not set. Use: ./deploy-to-gcp.sh YOUR_PROJECT_ID"
    exit 1
fi

echo "Deploying to project: $PROJECT_ID"

# Build and push image (Dockerfile handles Maven build)
echo "Building and pushing Docker image..."
gcloud builds submit --tag ${IMAGE_NAME}

# Deploy to Cloud Run
echo "Deploying to Cloud Run..."
gcloud run deploy ${SERVICE_NAME} \
    --image ${IMAGE_NAME} \
    --region ${REGION} \
    --allow-unauthenticated \
    --port 8080 \
    --memory 1Gi \
    --cpu 1 \
    --max-instances 10 \
    --set-env-vars="SPRING_PROFILES_ACTIVE=prod"

echo "Deployment completed!"
gcloud run services describe ${SERVICE_NAME} --region=${REGION} --format="value(status.url)"