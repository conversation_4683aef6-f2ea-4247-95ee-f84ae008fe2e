# DeepSeek with Ollama Setup Guide

This guide will help you set up DeepSeek models locally using Ollama for the assignment management system.

## 🚀 Quick Start

### 1. Install Ollama

#### Windows
```bash
# Download and install from https://ollama.ai/
# Or use winget
winget install Ollama.Ollama
```

#### macOS
```bash
# Download from https://ollama.ai/
# Or use Homebrew
brew install ollama
```

#### Linux
```bash
curl -fsSL https://ollama.ai/install.sh | sh
```

### 2. Pull DeepSeek Models

```bash
# Recommended: Balanced performance and quality
ollama pull deepseek-coder:6.7b

# Lightweight: Faster inference, good for simple questions
ollama pull deepseek-coder:1.3b

# General purpose: Good for all subjects
ollama pull deepseek-llm:7b

# High quality: Best results (requires more RAM)
ollama pull deepseek-coder:33b
ollama pull deepseek-llm:67b
```

### 3. Start Ollama Service

```bash
# Start Ollama server
ollama serve

# Test the model
ollama run deepseek-coder:6.7b "Generate a simple math question for grade 5"
```

### 4. Configure Application

Add to your `application.properties`:

```properties
# Enable Ollama with DeepSeek
llm.ollama.enabled=true
llm.ollama.api.url=http://localhost:11434/api/generate
llm.ollama.model=deepseek-coder:6.7b

# Set as primary provider
llm.primary.provider=OLLAMA

# Disable other providers if desired
llm.openai.enabled=false
llm.groq.enabled=false
llm.anthropic.enabled=false
```

## 📊 DeepSeek Model Comparison

| Model | Size | RAM Required | Speed | Quality | Best For |
|-------|------|-------------|-------|---------|----------|
| deepseek-coder:1.3b | 1.3B | 2GB | ⚡⚡⚡ | ⭐⭐⭐ | Simple coding questions |
| deepseek-coder:6.7b | 6.7B | 8GB | ⚡⚡ | ⭐⭐⭐⭐ | **Recommended** - Balanced |
| deepseek-coder:33b | 33B | 32GB | ⚡ | ⭐⭐⭐⭐⭐ | Complex coding problems |
| deepseek-llm:7b | 7B | 8GB | ⚡⚡ | ⭐⭐⭐⭐ | General subjects |
| deepseek-llm:67b | 67B | 64GB | ⚡ | ⭐⭐⭐⭐⭐ | Highest quality |

## 🔧 Advanced Configuration

### Custom Ollama Configuration

```properties
# Custom Ollama settings
llm.ollama.enabled=true
llm.ollama.api.url=http://localhost:11434/api/generate
llm.ollama.model=deepseek-coder:6.7b

# Performance tuning (optional)
llm.ollama.max.tokens=4000
llm.ollama.temperature=0.3
llm.ollama.timeout.seconds=60
```

### Remote Ollama Instance

```properties
# Connect to remote Ollama server
llm.ollama.api.url=http://your-server:11434/api/generate
llm.ollama.model=deepseek-coder:6.7b
```

### Docker Setup

```yaml
# docker-compose.yml
version: '3.8'
services:
  ollama:
    image: ollama/ollama:latest
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_HOST=0.0.0.0
    command: serve

volumes:
  ollama_data:
```

```bash
# Start with Docker
docker-compose up -d

# Pull models
docker exec -it ollama_container ollama pull deepseek-coder:6.7b
```

## 🧪 Testing DeepSeek Integration

### 1. Test Ollama Connection

```bash
curl http://localhost:11434/api/generate -d '{
  "model": "deepseek-coder:6.7b",
  "prompt": "Generate a simple math question",
  "stream": false
}'
```

### 2. Test via Application API

```http
POST /api/assignment/v1/llm/providers/OLLAMA/test
Authorization: Bearer <teacher-jwt-token>
```

### 3. Generate Sample Assignment

```http
POST /api/assignment/v1/assignments
Content-Type: application/json
Authorization: Bearer <teacher-jwt-token>

{
  "classroomId": "classroom-123",
  "title": "DeepSeek Generated Quiz",
  "subject": "Computer Science",
  "topic": "Python Programming",
  "type": "AI_GENERATED",
  "aiRequest": {
    "topic": "Python loops and conditionals",
    "difficultyLevel": "MEDIUM",
    "numberOfQuestions": 5,
    "questionTypes": ["MULTIPLE_CHOICE", "SHORT_ANSWER"],
    "gradeLevel": "High School"
  }
}
```

## 🎯 DeepSeek Advantages

### 1. **Privacy & Security**
- ✅ Runs locally - no data sent to external APIs
- ✅ Complete control over your data
- ✅ No API key management needed
- ✅ No usage limits or costs

### 2. **Performance**
- ✅ Fast inference with proper hardware
- ✅ No network latency
- ✅ Consistent availability
- ✅ Optimized for coding questions

### 3. **Cost Effective**
- ✅ No per-token charges
- ✅ One-time setup cost
- ✅ Unlimited usage
- ✅ Scales with your hardware

### 4. **Customization**
- ✅ Fine-tune models for your needs
- ✅ Custom prompting strategies
- ✅ Domain-specific optimization
- ✅ Version control over models

## 🔍 Troubleshooting

### Common Issues

#### 1. Ollama Not Starting
```bash
# Check if port is available
netstat -an | grep 11434

# Kill existing processes
pkill ollama

# Restart service
ollama serve
```

#### 2. Model Not Found
```bash
# List available models
ollama list

# Pull missing model
ollama pull deepseek-coder:6.7b
```

#### 3. Out of Memory
```bash
# Check system resources
free -h

# Use smaller model
ollama pull deepseek-coder:1.3b
```

#### 4. Slow Performance
- Use GPU acceleration if available
- Increase system RAM
- Use smaller models for faster inference
- Optimize Ollama settings

### Performance Optimization

#### 1. GPU Acceleration
```bash
# Install CUDA drivers (NVIDIA)
# Ollama will automatically use GPU if available

# Check GPU usage
nvidia-smi
```

#### 2. Memory Management
```bash
# Set memory limits
export OLLAMA_MAX_LOADED_MODELS=1
export OLLAMA_MAX_QUEUE=512
```

#### 3. Model Management
```bash
# Remove unused models
ollama rm old-model:version

# List model sizes
ollama list
```

## 📈 Monitoring & Maintenance

### 1. Monitor Performance
```bash
# Check Ollama logs
ollama logs

# Monitor system resources
htop
```

### 2. Update Models
```bash
# Update to latest version
ollama pull deepseek-coder:6.7b

# Check for updates
ollama list
```

### 3. Backup Configuration
```bash
# Backup Ollama data
cp -r ~/.ollama ~/ollama-backup
```

## 🔄 Switching Between Providers

### From Cloud to Local (DeepSeek)
```properties
# Disable cloud providers
llm.openai.enabled=false
llm.groq.enabled=false
llm.anthropic.enabled=false

# Enable Ollama
llm.ollama.enabled=true
llm.primary.provider=OLLAMA
```

### Hybrid Setup (Fallback)
```properties
# Primary: Local DeepSeek
llm.primary.provider=OLLAMA
llm.ollama.enabled=true

# Fallback: Cloud provider
llm.groq.enabled=true
```

## 🎓 Best Practices

1. **Start with `deepseek-coder:6.7b`** for balanced performance
2. **Monitor system resources** during usage
3. **Use appropriate models** for different question types
4. **Set up monitoring** for production environments
5. **Regular model updates** for improvements
6. **Backup configurations** before changes

## 📞 Support

- **Ollama Documentation**: https://ollama.ai/docs
- **DeepSeek Models**: https://ollama.ai/library/deepseek-coder
- **GitHub Issues**: Report integration issues
- **Community**: Join Ollama Discord/Forums

---

**Ready to use DeepSeek locally? Follow the quick start guide above!** 🚀
