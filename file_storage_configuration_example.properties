# ==================== File Storage Configuration ====================

# Storage Type: DATABASE, AWS_S3, GCP_CLOUD_STORAGE, LOCAL_FILESYSTEM
file.storage.type=DATABASE

# File Upload Limits
file.max-size=10485760
# 10MB = 10485760 bytes
# 50MB = 52428800 bytes
# 100MB = 104857600 bytes

# Allowed Content Types (comma-separated)
file.allowed-types=image/jpeg,image/png,image/gif,image/webp,application/pdf,text/plain,text/csv,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-powerpoint,application/vnd.openxmlformats-officedocument.presentationml.presentation,application/zip,video/mp4,audio/mpeg

# ==================== Database Storage Configuration ====================
# No additional configuration needed for database storage
# Files are stored as BLOB in the database

# ==================== AWS S3 Configuration ====================
# Uncomment and configure when using AWS S3
#file.storage.aws.bucket-name=your-s3-bucket-name
#file.storage.aws.region=us-east-1
#file.storage.aws.access-key-id=${AWS_ACCESS_KEY_ID}
#file.storage.aws.secret-access-key=${AWS_SECRET_ACCESS_KEY}

# Alternative: Use IAM roles (recommended for EC2/ECS)
# Leave access-key-id and secret-access-key empty to use IAM roles

# ==================== GCP Cloud Storage Configuration ====================
# Uncomment and configure when using GCP Cloud Storage
#file.storage.gcp.bucket-name=your-gcp-bucket-name
#file.storage.gcp.project-id=your-gcp-project-id
#file.storage.gcp.credentials-path=/path/to/service-account-key.json

# Alternative: Use Application Default Credentials (recommended for GCE/GKE)
# Leave credentials-path empty to use Application Default Credentials

# ==================== Local Filesystem Configuration ====================
# Uncomment and configure when using local filesystem
#file.storage.local.base-path=/var/app/files
#file.storage.local.create-directories=true

# ==================== Security Configuration ====================
# File virus scanning (if enabled)
file.security.virus-scan.enabled=false
file.security.virus-scan.provider=CLAMAV

# File encryption at rest (if enabled)
file.security.encryption.enabled=false
file.security.encryption.algorithm=AES-256-GCM

# ==================== Performance Configuration ====================
# File processing
file.processing.async=true
file.processing.thread-pool-size=5

# Thumbnail generation (for images)
file.thumbnail.enabled=true
file.thumbnail.sizes=150x150,300x300
file.thumbnail.quality=0.8

# ==================== Cleanup Configuration ====================
# Automatic cleanup of expired files
file.cleanup.enabled=true
file.cleanup.schedule=0 0 2 * * ?
# Runs daily at 2 AM

# Cleanup orphaned files (files without database records)
file.cleanup.orphaned.enabled=true
file.cleanup.orphaned.age-days=7

# ==================== Monitoring Configuration ====================
# File storage metrics
file.metrics.enabled=true
file.metrics.storage-usage-alert-threshold=0.8
# Alert when storage usage exceeds 80%

# ==================== Environment-Specific Examples ====================

# Development Environment
#file.storage.type=DATABASE
#file.max-size=5242880
# 5MB for development

# Staging Environment
#file.storage.type=AWS_S3
#file.storage.aws.bucket-name=staging-files-bucket
#file.max-size=52428800
# 50MB for staging

# Production Environment
#file.storage.type=AWS_S3
#file.storage.aws.bucket-name=prod-files-bucket
#file.max-size=104857600
# 100MB for production
#file.security.virus-scan.enabled=true
#file.security.encryption.enabled=true

# ==================== Content Type Categories ====================
# Image files
#file.allowed-types.images=image/jpeg,image/png,image/gif,image/webp,image/svg+xml

# Document files
#file.allowed-types.documents=application/pdf,text/plain,text/csv,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-powerpoint,application/vnd.openxmlformats-officedocument.presentationml.presentation

# Archive files
#file.allowed-types.archives=application/zip,application/x-rar-compressed,application/x-7z-compressed

# Media files
#file.allowed-types.media=video/mp4,video/avi,video/quicktime,audio/mpeg,audio/wav,audio/ogg

# ==================== Usage Examples ====================

# For Educational Platform:
# - Allow documents, images, and media files
# - Use cloud storage for scalability
# - Enable virus scanning for security
# - Set reasonable file size limits

# For Corporate Environment:
# - Restrict to business document types
# - Use enterprise cloud storage
# - Enable encryption and virus scanning
# - Implement strict access controls

# For Development:
# - Use database storage for simplicity
# - Allow all common file types
# - Smaller file size limits
# - Disable security features for faster development

# ==================== Dependencies Required ====================

# For AWS S3 (add to pom.xml):
# <dependency>
#     <groupId>software.amazon.awssdk</groupId>
#     <artifactId>s3</artifactId>
#     <version>2.20.26</version>
# </dependency>

# For GCP Cloud Storage (add to pom.xml):
# <dependency>
#     <groupId>com.google.cloud</groupId>
#     <artifactId>google-cloud-storage</artifactId>
#     <version>2.22.2</version>
# </dependency>

# For virus scanning with ClamAV (add to pom.xml):
# <dependency>
#     <groupId>fi.solita.clamav</groupId>
#     <artifactId>clamav-client</artifactId>
#     <version>1.0.1</version>
# </dependency>

# ==================== Security Notes ====================
# 1. Never commit credentials to version control
# 2. Use environment variables for sensitive configuration
# 3. Implement proper access controls
# 4. Enable virus scanning in production
# 5. Consider encryption for sensitive files
# 6. Regularly audit file access logs
# 7. Implement file retention policies
# 8. Monitor storage usage and costs
