# Local Development Guide

This guide explains how to run the Track My Class application in local development mode with security disabled for easier testing.

## Running in Local Mode

To run the application with security disabled:

```bash
# Run with local profile
./mvnw spring-boot:run -Dspring-boot.run.profiles=local
```

Or set the following VM options in your IDE run configuration:

```
-Dspring.profiles.active=local
```

You can also use environment variables:

```bash
# Windows
set SPRING_PROFILES_ACTIVE=local
./mvnw spring-boot:run

# Linux/Mac
export SPRING_PROFILES_ACTIVE=local
./mvnw spring-boot:run
```

### Important Note

Do not set `spring.profiles.active` inside the `application-local.properties` file itself, as this will cause a circular reference error.

## Local Profile Features

When running with the `local` profile:

1. **Security is disabled** - No authentication is required to access API endpoints
2. **H2 Console is enabled** - Access the database at http://localhost:8080/h2-console
3. **Enhanced logging** - More detailed logs for debugging
4. **In-memory database** - Data is reset on each application restart

## API Documentation

When running in local mode, the Swagger UI will indicate that security is disabled:

- Swagger UI: http://localhost:8080/swagger-ui.html
- OpenAPI JSON: http://localhost:8080/api-docs

## Security Warning

The local profile disables security to make development and testing easier. **Never use this profile in production environments.**

## Switching Back to Secure Mode

To run with security enabled, simply omit the profile parameter:

```bash
./mvnw spring-boot:run
```

Or run with a different profile:

```bash
./mvnw spring-boot:run -Dspring-boot.run.profiles=dev
```
