steps:
  # Build the application
  - name: 'maven:3.8.6-openjdk-21'
    entrypoint: 'mvn'
    args: ['clean', 'package', '-DskipTests']
  
  # Build the Docker image
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-t', 'gcr.io/$PROJECT_ID/trackmyclass:$COMMIT_SHA', '.']
  
  # Push the Docker image to Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/trackmyclass:$COMMIT_SHA']
  
  # Deploy to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: 'gcloud'
    args:
      - 'run'
      - 'deploy'
      - 'trackmyclass'
      - '--image'
      - 'gcr.io/$PROJECT_ID/trackmyclass:$COMMIT_SHA'
      - '--region'
      - 'us-central1'
      - '--platform'
      - 'managed'
      - '--allow-unauthenticated'
      - '--port'
      - '8080'
      - '--memory'
      - '1Gi'
      - '--cpu'
      - '1'

images:
  - 'gcr.io/$PROJECT_ID/trackmyclass:$COMMIT_SHA'