#
# Production configuration for GCP deployment
#

# Server configuration
server.port=8080

# OpenAPI/Swagger Configuration
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.operationsSorter=method
springdoc.swagger-ui.tagsSorter=alpha
springdoc.swagger-ui.filter=true
springdoc.swagger-ui.tryItOutEnabled=true
springdoc.swagger-ui.docExpansion=none

# Jackson
spring.jackson.default-property-inclusion=non_null

# H2 Database configuration for production
spring.datasource.url=jdbc:h2:file:/tmp/proddb
spring.datasource.username=sa
spring.datasource.password=sa
spring.jpa.generate-ddl=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.H2Dialect

# OAuth2 Configuration - minimal setup to prevent startup errors
spring.security.oauth2.resourceserver.jwt.issuer-uri=https://accounts.google.com
spring.security.oauth2.client.registration.google.client-id=************-qkuc8oh9340n70vr0imao3d8o2e2rj2k.apps.googleusercontent.com
spring.security.oauth2.client.registration.google.client-secret=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
spring.security.oauth2.client.registration.google.scope=openid,profile,email
spring.security.oauth2.client.registration.google.redirect-uri={baseUrl}/login/oauth2/code/google
spring.security.oauth2.client.provider.google.authorization-uri=https://accounts.google.com/o/oauth2/auth
spring.security.oauth2.client.provider.google.token-uri=https://oauth2.googleapis.com/token
spring.security.oauth2.client.provider.google.user-info-uri=https://www.googleapis.com/oauth2/v2/userinfo
spring.security.oauth2.client.provider.google.jwk-set-uri=https://www.googleapis.com/oauth2/v3/certs

# Logging
logging.level.root=INFO
logging.level.com.marglabs=INFO
logging.level.org.springframework.security=INFO