#
# Copyright (c) 2021. This file is copyright to MargLabs Tech Private Ltd.
#
#Swagger Generation
springdoc.swagger-ui.url=/api/classroom.yaml
#Spring REST
#server.servlet.context-path=/api
#Jackson
spring.jackson.default-property-inclusion=non_null
#H2 DB
spring.datasource.url=jdbc:h2:file:~/testdb18
spring.datasource.username=sa
spring.datasource.password=sa
spring.jpa.generate-ddl=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.H2Dialect

#AWS Cognito Configuration
spring.security.oauth2.resourceserver.jwt.issuer-uri=https://cognito-idp.us-east-1.amazonaws.com/us-east-1_CfPZWbR4P
spring.security.oauth2.client.registration.cognito.client-id=76u1v7el416ebllhpbhtqpmlh0
#spring.security.oauth2.client.registration.cognito.client-secret=fr7i55ir89cm0asb9md61v8ijv9f9pfk4qjm6fp85gmcc0bg8sj
spring.security.oauth2.client.registration.cognito.authorization-grant-type=authorization_code
spring.security.oauth2.client.provider.cognito.authorization-uri=https://us-east-1cfpzwbr4p.auth.us-east-1.amazoncognito.com/login
spring.security.oauth2.client.provider.cognito.jwk-set-uri=https://cognito-idp.us-east-1.amazonaws.com/us-east-1_CfPZWbR4P/.well-known/jwks.json
spring.security.oauth2.client.provider.cognito.token-uri=https://us-east-1cfpzwbr4p.auth.us-east-1.amazoncognito.com/oauth2/token
spring.security.oauth2.client.provider.cognito.user-info-uri=https://us-east-1cfpzwbr4p.auth.us-east-1.amazoncognito.com/oauth2/userInfo
spring.security.oauth2.client.registration.cognito.redirect-uri=http://localhost:8080/login/oauth2/code/cognito
spring.security.oauth2.client.registration.cognito.scope=openid, phone, email
#spring.security.oauth2.client.provider.cognito=https://us-east-1cfpzwbr4p.auth.us-east-1.amazoncognito.com
spring.security.oauth2.client.provider.cognito.user-name-attribute=email

logging.level.org.springframework.security=TRACE
logging.level.org.springframework.security.oauth2=TRACE