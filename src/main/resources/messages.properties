# Schedule Management Error Messages
SCHEDULE_BAD_REQUEST=Invalid schedule request
SCHEDULE_NOT_FOUND=Schedule not found
SCHEDULE_FORBIDDEN=Access denied to schedule
SCHEDULE_CONFLICT=Schedule conflict detected

# Schedule specific error details
schedule_not_found_details=Schedule with ID {0} was not found
teacher_not_authorized_for_schedule=Teacher is not authorized to access schedule {0}
start_time_must_be_before_end_time=Start time must be before end time
end_date_required_for_recurring_sessions=End date is required for multiple sessions
days_of_week_required_for_multiple_sessions=Days of week are required for multiple sessions
start_date_must_be_before_end_date=Start date must be before end date
single_session_cannot_have_recurring_fields=Single sessions cannot have recurring fields
schedule_time_conflict_detected=Schedule conflict detected for {0} from {1} to {2}

# Fee Structure Management Error Messages
FEE_STRUCTURE_BAD_REQUEST=Invalid fee structure request
FEE_STRUCTURE_NOT_FOUND=Fee structure not found
FEE_STRUCTURE_FORBIDDEN=Access denied to fee structure
FEE_STRUCTURE_CONFLICT=Fee structure conflict detected

# Fee structure specific error details
fee_structure_not_found_details=Fee structure with ID {0} was not found
teacher_not_authorized_for_fee_structure=Teacher is not authorized to access fee structure {0}
fee_structure_already_exists=Fee structure already exists for classroom {0}, country {1}, payment type {2}
invalid_country_code=Invalid or unsupported country code: {0}
invalid_country_code_format=Invalid country code format: {0}
fee_amount_must_be_positive=Fee amount must be greater than 0
fee_amount_max_two_decimal_places=Fee amount cannot have more than 2 decimal places

# Notification Management Error Messages
NOTIFICATION_BAD_REQUEST=Invalid notification request
NOTIFICATION_NOT_FOUND=Notification not found
NOTIFICATION_FORBIDDEN=Access denied to notification

# Notification specific error details
notification_not_found_details=Notification with ID {0} was not found
notification_title_too_long=Notification title cannot exceed 200 characters
notification_content_too_long=Notification content cannot exceed 2000 characters
notification_image_url_too_long=Image URL cannot exceed 500 characters

# Student Management Error Messages
STUDENT_BAD_REQUEST=Invalid student request
STUDENT_NOT_FOUND=Student not found
STUDENT_FORBIDDEN=Access denied to student
STUDENT_EMAIL_EXISTS=Student email already exists

# Student specific error details
student_not_found_details=Student with ID {0} was not found
student_email_already_exists=A student with email {0} already exists for this teacher
teacher_not_authorized_for_student=Teacher is not authorized to access student {0}
birth_date_cannot_be_future=Date of birth cannot be in the future
birth_date_too_old=Date of birth indicates age over 100 years
invalid_birth_date_format=Invalid date of birth format. Use YYYY-MM-DD

# Teacher Enrollment Error Messages
either_classroom_id_or_join_code_required=Either classroom ID or join code must be provided
provide_either_classroom_id_or_join_code_not_both=Provide either classroom ID or join code, not both

# User Creation Error Messages
USER_EMAIL_EXISTS=User email already exists
user_email_already_exists=A user with email {0} already exists

# Attendance Management Error Messages
ATTENDANCE_NOT_FOUND=Attendance record not found
ATTENDANCE_ALREADY_EXISTS=Attendance already recorded
UNAUTHORIZED_ATTENDANCE_ACCESS=Unauthorized attendance access
INVALID_ATTENDANCE_DATE=Invalid attendance date
INVALID_SESSION_TIME=Invalid session time

# Attendance specific error details
attendance_not_found_details=Attendance record with ID {0} was not found
attendance_already_recorded=Attendance already recorded for student {0} on {1}
teacher_not_authorized_for_attendance=Teacher is not authorized to access attendance record {0}
attendance_date_cannot_be_future=Attendance date cannot be in the future
attendance_date_too_old=Attendance date cannot be more than 1 year old
invalid_attendance_date_format=Invalid attendance date format. Use YYYY-MM-DD
session_end_time_must_be_after_start_time=Session end time must be after start time
invalid_session_time_format=Invalid session time format. Use HH:MM

# Payment Management Error Messages
PAYMENT_NOT_FOUND=Payment record not found
UNAUTHORIZED_PAYMENT_ACCESS=Unauthorized payment access
DUPLICATE_RECEIPT_NUMBER=Duplicate receipt number
INVALID_PAYMENT_AMOUNT=Invalid payment amount
INVALID_PAYMENT_DATE=Invalid payment date

# Payment specific error details
payment_not_found_details=Payment record with ID {0} was not found
teacher_not_authorized_for_payment=Teacher is not authorized to access payment record {0}
receipt_number_already_exists=Receipt number {0} already exists
payment_amount_must_be_positive=Payment amount must be greater than zero
payment_date_cannot_be_future=Payment date cannot be in the future
payment_date_too_old=Payment date cannot be more than 5 years old
invalid_payment_date_format=Invalid payment date format. Use YYYY-MM-DD

# Student Un-enrollment Error Messages
STUDENT_NOT_ENROLLED=Student not enrolled in classroom
ALREADY_UNENROLLED=Student already un-enrolled
USER_NOT_FOUND=User not found for student

# Student un-enrollment specific error details
student_not_enrolled=Student {0} is not enrolled in classroom {1}
student_already_unenrolled=Student {0} is already un-enrolled from classroom {1}
student_user_not_found=User information not found for student {0}

ENROLLMENT_NOT_FOUND=Enrollment not found

# Profile Management Error Messages
PROFILE_NOT_FOUND=Profile not found
PROFILE_BAD_REQUEST=Invalid profile request
PROFILE_FORBIDDEN=Access denied to profile
EDUCATION_NOT_FOUND=Education record not found
SKILL_NOT_FOUND=Skill not found
SKILL_ALREADY_EXISTS=Skill already exists
CERTIFICATION_NOT_FOUND=Certification not found
CERTIFICATION_ALREADY_EXISTS=Certification already exists
UNAUTHORIZED_PROFILE_ACCESS=Unauthorized profile access
UNAUTHORIZED_EDUCATION_ACCESS=Unauthorized education access
UNAUTHORIZED_SKILL_ACCESS=Unauthorized skill access
UNAUTHORIZED_CERTIFICATION_ACCESS=Unauthorized certification access

# Profile specific error details
profile_not_found_details=Profile not found for user {0}
user_not_authorized_for_profile=User is not authorized to access profile {0}
education_not_found_details=Education record with ID {0} was not found
user_not_authorized_for_education=User is not authorized to access education record {0}
skill_not_found_details=Skill with ID {0} was not found
skill_already_exists_for_user=Skill {0} already exists for user
user_not_authorized_for_skill=User is not authorized to access skill {0}
certification_not_found_details=Certification with ID {0} was not found
certification_already_exists_for_user=Certification {0} already exists for user
user_not_authorized_for_certification=User is not authorized to access certification {0}
invalid_gender_value=Invalid gender value. Must be MALE, FEMALE, OTHER, or PREFER_NOT_TO_SAY
invalid_start_year=Start year must be between 1900 and current year
invalid_end_year=End year must be between start year and current year + 10
end_year_before_start_year=End year cannot be before start year
invalid_skill_level=Invalid skill level. Must be BEGINNER, INTERMEDIATE, ADVANCED, or EXPERT
invalid_certification_level=Invalid certification level format

# Student Attendance Dashboard Error Messages
NO_ATTENDANCE_RECORDS=No attendance records found
NO_CLASS_ATTENDANCE_RECORDS=No attendance records found for class
INVALID_DATE_RANGE=Invalid date range
UNAUTHORIZED_STUDENT_ACCESS=Unauthorized student access

# Student attendance specific error details
no_attendance_records_found=No attendance records found for student {0}
no_attendance_records_found_for_class=No attendance records found for student {0} in class {1}
user_not_authorized_for_student=User is not authorized to access student {0}
invalid_date_format=Invalid date format. Use YYYY-MM-DD
start_date_after_end_date=Start date cannot be after end date
date_range_too_large=Date range cannot exceed 1 year

# Student Enrolled Classes Error Messages
NO_ENROLLMENTS_FOUND=No enrollments found
CLASSROOM_ACCESS_DENIED=Classroom access denied

# Student enrolled classes specific error details
no_enrollments_found_for_student=No enrollments found for student {0}
student_not_enrolled_in_classroom=Student {0} is not enrolled in classroom {1}
classroom_not_found_details=Classroom with ID {0} was not found

# Work Experience Management Error Messages
WORK_EXPERIENCE_NOT_FOUND=Work experience not found
WORK_EXPERIENCE_BAD_REQUEST=Invalid work experience request
WORK_EXPERIENCE_FORBIDDEN=Access denied to work experience
UNAUTHORIZED_WORK_EXPERIENCE_ACCESS=Unauthorized work experience access
INVALID_YEAR_RANGE=Invalid year range

# Work experience specific error details
work_experience_not_found_details=Work experience with ID {0} was not found
user_not_authorized_for_work_experience=User is not authorized to access work experience {0}
end_year_required_when_not_currently_working=End year is required when not currently working
start_year_after_end_year=Start year {0} cannot be after end year {1}
start_month_after_end_month=Start month cannot be after end month in the same year
start_year_cannot_be_future=Start year cannot be in the future
end_year_cannot_be_future=End year cannot be in the future
invalid_employment_type=Invalid employment type. Must be FULL_TIME, PART_TIME, CONTRACT, FREELANCE, or INTERNSHIP

# Teacher Review Management Error Messages
TEACHER_REVIEW_NOT_FOUND=Teacher review not found
TEACHER_REVIEW_BAD_REQUEST=Invalid teacher review request
TEACHER_REVIEW_FORBIDDEN=Access denied to teacher review
UNAUTHORIZED_REVIEW_ACCESS=Unauthorized review access
REVIEW_ALREADY_EXISTS=Review already exists
STUDENT_CANNOT_REVIEW_TEACHER=Student cannot review teacher
REVIEW_CANNOT_BE_UPDATED=Review cannot be updated
REVIEW_CANNOT_BE_DELETED=Review cannot be deleted
TEACHER_NOT_AUTHORIZED_FOR_CLASSROOM=Teacher not authorized for classroom

# Teacher review specific error details
teacher_review_not_found_details=Teacher review with ID {0} was not found
student_not_authorized_for_review=Student {0} is not authorized to access review {1}
student_already_reviewed_teacher=Student {0} has already reviewed teacher {1}
student_not_enrolled_with_teacher=Student {0} is not enrolled with teacher {1}
teacher_not_found_details=Teacher with ID {0} was not found
review_not_pending=Review {0} is not in pending status and cannot be modified
invalid_review_category=Invalid review category. Must be TEACHING_QUALITY, COMMUNICATION, HELPFULNESS, or OVERALL
invalid_rating_value=Rating must be between 1 and 5
teacher_not_authorized_for_classroom=Teacher {0} is not authorized to access classroom {1}

# User Management Error Messages
EMAIL_ALREADY_EXISTS=Email already exists
email_already_exists=Email {0} is already registered by another user

# Expense Management Error Messages
EXPENSE_NOT_FOUND=Expense not found
UNAUTHORIZED_EXPENSE_ACCESS=Unauthorized expense access
INVALID_AMOUNT=Invalid amount
INVALID_DATE_FORMAT=Invalid date format
INVALID_CATEGORY=Invalid expense category

# Expense specific error details
expense_not_found_details=Expense with ID {0} was not found
teacher_not_authorized_for_expense=Teacher {0} is not authorized to access expense {1}
amount_must_be_positive=Amount must be greater than 0
invalid_expense_category=Invalid expense category {0}. Must be one of: TEACHING_MATERIALS, TRANSPORTATION, OFFICE_SUPPLIES, TECHNOLOGY, PROFESSIONAL_DEVELOPMENT, MEALS, ACCOMMODATION, OTHER

# Student Fee Selection Error Messages
FEE_SELECTION=Fee Selection Error
SELECTION_NOT_FOUND=Fee selection not found
FEE_SELECTION_NOT_FOUND=Fee selection not found
INVALID_FEE_STRUCTURE=Invalid fee structure

# Student fee selection specific error details
student_fee_selection_not_found=Student fee selection with ID {0} was not found
fee_structure_not_for_classroom=Fee structure {0} does not belong to classroom {1}
no_fee_selection_found=No fee selection found for student {0} in classroom {1}
