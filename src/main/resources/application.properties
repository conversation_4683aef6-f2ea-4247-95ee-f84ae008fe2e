#
# Copyright (c) 2021. This file is copyright to MargLabs Tech Private Ltd.
#
#OpenAPI/Swagger Configuration
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.operationsSorter=method
springdoc.swagger-ui.tagsSorter=alpha
springdoc.swagger-ui.filter=true
springdoc.swagger-ui.tryItOutEnabled=true
springdoc.swagger-ui.docExpansion=none
#springdoc.packages-to-scan=com.marglabs.trackmyclass
#Spring REST
#server.servlet.context-path=/api

# Disable Spring Data REST auto-exposure
spring.data.rest.base-path=/api/data-rest
spring.data.rest.default-page-size=20
spring.data.rest.max-page-size=100
spring.data.rest.page-param-name=page
spring.data.rest.limit-param-name=size
spring.data.rest.sort-param-name=sort
spring.data.rest.default-media-type=application/json
spring.data.rest.return-body-on-create=false
spring.data.rest.return-body-on-update=false
#Jackson
spring.jackson.default-property-inclusion=non_null
spring.jackson.serialization.write-dates-as-timestamps=false
spring.jackson.deserialization.fail-on-unknown-properties=false
#H2 DB
spring.datasource.url=jdbc:h2:file:~/testdb18
spring.datasource.username=sa
spring.datasource.password=sa
spring.jpa.generate-ddl=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.H2Dialect

# OAuth2 Configuration - minimal setup to prevent startup errors
spring.security.oauth2.resourceserver.jwt.issuer-uri=https://accounts.google.com
spring.security.oauth2.client.registration.google.client-id=************-qkuc8oh9340n70vr0imao3d8o2e2rj2k.apps.googleusercontent.com
spring.security.oauth2.client.registration.google.client-secret=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
spring.security.oauth2.client.registration.google.scope=openid,profile,email
spring.security.oauth2.client.provider.google.authorization-uri=https://accounts.google.com/o/oauth2/auth
spring.security.oauth2.client.provider.google.token-uri=https://oauth2.googleapis.com/token
spring.security.oauth2.client.provider.google.user-info-uri=https://www.googleapis.com/oauth2/v2/userinfo
spring.security.oauth2.client.provider.google.jwk-set-uri=https://www.googleapis.com/oauth2/v3/certs

logging.level.org.springframework.security=TRACE
logging.level.org.springframework.security.oauth2=TRACE

# File Storage Configuration
file.storage.type=DATABASE
file.max-size=********
file.allowed-types=image/jpeg,image/png,image/gif,image/webp,application/pdf,text/plain,text/csv,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-powerpoint,application/vnd.openxmlformats-officedocument.presentationml.presentation,application/zip,video/mp4,audio/mpeg

# File Cleanup Configuration
file.cleanup.enabled=true
file.cleanup.schedule=0 0 2 * * ?
file.cleanup.orphaned.enabled=true
file.cleanup.orphaned.age-days=7