/*
 * Copyright (c) 2021. This file is copyright to MargLabs Tech Private Ltd.
 */

package com.marglabs.common.rest.error;

import lombok.Getter;
import lombok.experimental.Accessors;

@Getter
@Accessors(fluent = true)
public class MessageCode
{
    private String code;

    private String[] args;

    private static MessageCode with(String code, String... args)
    {
        MessageCode messageCode = new MessageCode();
        messageCode.code = code;
        messageCode.args = args;

        return messageCode;
    }
}
