package com.marglabs.common.rest.error;

import lombok.Getter;
import org.springframework.http.HttpStatus;

@Getter
public class RestException extends RuntimeException
{

    private static final long serialVersionUID = 8221437846385833293L;

    private HttpStatus status;
    private String entity;
    private String code;
    private String detailsCode;
    private String[] args;

    public RestException(HttpStatus status, String entity, String code, String detailsCode, String... args)
    {
        this.status = status;
        this.entity = entity;
        this.code = code;
        this.detailsCode = detailsCode;
        this.args = args;
    }
}
