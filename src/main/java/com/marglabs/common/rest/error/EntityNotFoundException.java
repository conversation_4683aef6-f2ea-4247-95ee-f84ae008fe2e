/*
 * Copyright (c) 2021. This file is copyright to MargLabs Tech Private Ltd.
 */

package com.marglabs.common.rest.error;

import org.springframework.http.HttpStatus;

public class EntityNotFoundException extends RestException
{
    public EntityNotFoundException(HttpStatus status, String entity, String code, String detailsCode, String... args)
    {
        super(HttpStatus.NOT_FOUND, entity, code, detailsCode, args);
    }
}
