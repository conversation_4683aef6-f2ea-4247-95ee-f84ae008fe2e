package com.marglabs.common.rest.error;

import com.marglabs.http.models.Error;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.NoHandlerFoundException;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import java.util.Locale;

@RestControllerAdvice
@Slf4j
public class RestExceptionHandler extends ResponseEntityExceptionHandler
{

    public enum ErrorCode
    {
        INPUT_VALIDATION_ERROR,
        INTERNAL_SERVER_ERROR,
        ENTITY_NOT_FOUND
    }

    @Autowired
    private MessageSource messageSource;

    @Override
    protected ResponseEntity<Object> handleMethodArgumentNotValid(MethodArgumentNotValidException ex, HttpHeaders headers, HttpStatusCode status, WebRequest request) {
        Error error = new Error();

        error.setCode(ErrorCode.INPUT_VALIDATION_ERROR.toString());
        error.setReason(ex.getBindingResult().getAllErrors().get(0).getDefaultMessage());
        error.setDetails(error.getReason());

        return new ResponseEntity<>(error, HttpStatus.NOT_FOUND);
    }


    @Override
    protected ResponseEntity<Object> handleNoHandlerFoundException(NoHandlerFoundException ex, HttpHeaders headers, HttpStatusCode status, WebRequest request) {
        log.error("Unexpected error encountered. Reason: ", ex);

        Error error = new Error();
        error.setCode(status.toString());
        error.setReason(ex.getMessage());
        error.setDetails(ex.getMessage());

        return new ResponseEntity<>(error, status);
    }

    @Override
    protected ResponseEntity<Object> handleExceptionInternal(Exception ex, Object body, HttpHeaders headers, HttpStatusCode statusCode, WebRequest request) {
        ResponseEntity<Object> responseEntity = super.handleExceptionInternal(ex, body, headers, statusCode, request);

        Error error = new Error();
        error.setCode(responseEntity.getStatusCode().toString());
        error.setReason(ex.getMessage());
        error.setDetails(ex.getMessage());

        ResponseEntity<Object> transformedResponse = new ResponseEntity<>(error, headers, statusCode);
        return transformedResponse;
    }




    @ExceptionHandler(Exception.class)
    public ResponseEntity<Error> handleUnhandledException(Exception ex)
    {

        log.error("Unexpected error encountered. Reason: ", ex);

        Error error = new Error();
        error.setCode(ErrorCode.INTERNAL_SERVER_ERROR.toString());
        error.setReason(ex.getMessage());
        error.setDetails(ex.toString());
        return new ResponseEntity<>(error, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ExceptionHandler(RestException.class)
    public ResponseEntity<Error> handleRestException(RestException rex, Locale locale)
    {
        Error error = new Error();
        error.setCode(rex.getCode());
        error.setReason(messageSource.getMessage(rex.getEntity() + "_" + rex.getStatus().name(), null, locale));
        error.setDetails(messageSource.getMessage(rex.getDetailsCode(), rex.getArgs(), locale));
        return new ResponseEntity<>(error, rex.getStatus());
    }

    @ExceptionHandler(GeneralException.class)
    public ResponseEntity<Error> handleGeneralException(GeneralException gex, Locale locale)
    {
        Error error = new Error();
        error.setCode(gex.getCode());
        error.setReason(messageSource.getMessage(gex.getEntity() + "_" + gex.getStatus().name(), null, locale));
        error.setDetails(messageSource.getMessage(gex.getDetailsCode(), gex.getArgs(), locale));
        return new ResponseEntity<>(error, gex.getStatus());
    }

}
