/*
 * Copyright (c) 2021. This file is copyright to MargLabs Tech Private Ltd.
 */

package com.marglabs.common.mapper;

import org.joda.time.DateTime;
import org.mapstruct.Mapper;

import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;

@Mapper(componentModel = "spring")
public interface DateTimeMapper
{
    default OffsetDateTime mapToDate(long timestamp)
    {
        return OffsetDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneOffset.UTC);
    }

    default long mapToLong(OffsetDateTime dateTime)
    {
        if (dateTime != null)
        {
            return dateTime.toInstant().toEpochMilli();
        } else
        {
            return 0;
        }
    }
}
