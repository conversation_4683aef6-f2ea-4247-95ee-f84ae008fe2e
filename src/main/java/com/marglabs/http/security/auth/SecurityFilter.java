/*
 * Copyright (c) 2021. This file is copyright to MargLabs Tech Private Ltd.
 *//*


package com.marglabs.http.security.auth;

import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.auth.FirebaseAuthException;
import com.google.firebase.auth.FirebaseToken;
import com.marglabs.http.security.auth.models.Credentials;
import com.marglabs.http.security.auth.models.UserPrincipal;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Component
@Slf4j
public class SecurityFilter extends OncePerRequestFilter
{

    @Autowired
    private SecurityService securityService;

    @SneakyThrows
    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
    {
        verifyToken(request);
        filterChain.doFilter(request, response);
    }

    private void verifyToken(HttpServletRequest request) throws FirebaseAuthException
    {
        FirebaseToken decodedToken = null;
        Credentials.CredentialType type = null;
        String token = securityService.getBearerToken(request);
        if (token != null && !token.equalsIgnoreCase("undefined"))
        {
            if (!isTestToken(token))
            {
                decodedToken = FirebaseAuth.getInstance().verifyIdToken(token);
                type = Credentials.CredentialType.ID_TOKEN;
            }
        }
        UserPrincipal user;
        if (isTestToken(token))
        {
            user = createTestUser(token);
        } else
        {
            user = firebaseTokenToUserDto(decodedToken);
        }
        if (user != null)
        {
            var authentication = new UsernamePasswordAuthenticationToken(user,
                    new Credentials(type, decodedToken, token), null);
            authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
            SecurityContextHolder.getContext().setAuthentication(authentication);
        }
    }

    private UserPrincipal createTestUser(String token)
    {
        if (token != null)
        {
            UserPrincipal principal = new UserPrincipal();
            if (token.equals("abcd"))
            {
                principal.setUid("1");
            } else
            {
                principal.setUid("2");
            }
            return principal;
        }
        return null;
    }

    private boolean isTestToken(String token)
    {
        if (token != null)
        {
            if (token.equalsIgnoreCase("abcd") || token.equals("wxyz"))
            {
                return true;
            }
        }
        return false;
    }

    private UserPrincipal firebaseTokenToUserDto(FirebaseToken decodedToken)
    {
        UserPrincipal user = null;
        if (decodedToken != null)
        {
            user = new UserPrincipal();
            user.setUid(decodedToken.getUid());
            user.setName(decodedToken.getName());
            user.setEmail(decodedToken.getEmail());
            user.setPicture(decodedToken.getPicture());
            user.setIssuer(decodedToken.getIssuer());
            user.setEmailVerified(decodedToken.isEmailVerified());
        }
        return user;
    }

}
*/
