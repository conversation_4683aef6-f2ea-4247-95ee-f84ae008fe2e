/*
 * Copyright (c) 2021. This file is copyright to MargLabs Tech Private Ltd.
 *//*


package com.marglabs.http.security.auth;

import com.marglabs.http.security.auth.models.Credentials;
import com.marglabs.http.security.auth.models.UserPrincipal;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;


@Service
public class SecurityService
{

    @Autowired
    private HttpServletRequest httpServletRequest;

    public UserPrincipal getUser()
    {
        UserPrincipal userPrincipal = null;
        SecurityContext securityContext = SecurityContextHolder.getContext();
        Object principal = securityContext.getAuthentication().getPrincipal();
        if (principal instanceof UserPrincipal)
        {
            userPrincipal = ((UserPrincipal) principal);
        }
        return userPrincipal;
    }

    public Credentials getCredentials()
    {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        return (Credentials) securityContext.getAuthentication().getCredentials();
    }

    public String getBearerToken(HttpServletRequest request)
    {
        String bearerToken = null;
        String authorization = request.getHeader("Authorization");
        if (StringUtils.hasText(authorization) && authorization.startsWith("Bearer "))
        {
            bearerToken = authorization.substring(7);
        }
        return bearerToken;
    }
}
*/
