/*
 * Copyright (c) 2021. This file is copyright to MargLabs Tech Private Ltd.
 */

package com.marglabs.http.security.auth.models;

//import com.google.firebase.auth.FirebaseToken;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class Credentials
{

    public enum CredentialType
    {
        ID_TOKEN
    }

    private CredentialType type;
    //private FirebaseToken decodedToken;
    private String idToken;
}
