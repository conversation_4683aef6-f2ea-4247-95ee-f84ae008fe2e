 /* Copyright (c) 2021. This file is copyright to MargLabs Tech Private Ltd.*/



package com.marglabs.http.security.auth;

 import org.springframework.context.annotation.Bean;
 import org.springframework.context.annotation.Configuration;
 import org.springframework.context.annotation.Profile;
 import org.springframework.security.config.Customizer;
 import org.springframework.security.config.annotation.web.builders.HttpSecurity;
 import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
 import org.springframework.security.config.annotation.web.configurers.HeadersConfigurer;
 import org.springframework.security.web.SecurityFilterChain;



@Configuration
@EnableWebSecurity
//@EnableGlobalMethodSecurity(securedEnabled = true, jsr250Enabled = true, prePostEnabled = true)
@Profile("!local") // This configuration is not active when local profile is active
public class SecurityConfig
{
  @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http
                .csrf(csrf -> csrf.ignoringRequestMatchers("/h2-console/**", "/actuator/health","swagger-ui.html","swagger/**","/api/contact/v1/**"))
                .authorizeHttpRequests(authz -> authz
                        .requestMatchers("/h2-console/**", "/actuator/health","swagger-ui.html","swagger/**","/api/contact/v1/**").permitAll()  // Allow public URLs
                        .anyRequest().authenticated()                   // Protect all other endpoints
                )
                .headers(headers -> headers.frameOptions(HeadersConfigurer.FrameOptionsConfig::sameOrigin))
                // Enable OAuth2 login - Redirect user to Cognito
                .oauth2Login(oauth2 -> oauth2
                        .loginPage("/oauth2/authorization/google")  // Redirect endpoint for Google login
                )
                // Enable JWT validation for API endpoints
                .oauth2ResourceServer(oauth2 -> oauth2.jwt(Customizer.withDefaults()));

        return http.build();   }

}
