//package com.marglabs.http.security.auth;
//
//import com.marglabs.http.security.auth.models.UserPrincipal;
//import org.springframework.security.oauth2.jwt.Jwt;
//import org.springframework.core.convert.converter.Converter;
//import org.springframework.stereotype.Component;
//
//@Component
//public class JwtToUserPrincipalConverter implements Converter<Jwt, UserPrincipal> {
//    @Override
//    public UserPrincipal convert(Jwt jwt) {
//        String subject = jwt.getClaimAsString("sub");
//        String email = jwt.getClaimAsString("email");
//        String name = jwt.getClaimAsString("name");
//
//        UserPrincipal userPrincipal = new UserPrincipal();
//        userPrincipal.setUid(jwt.getClaimAsString("username"));
//
//        return userPrincipal;
//    }
//}