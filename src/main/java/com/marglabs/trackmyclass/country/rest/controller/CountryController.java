package com.marglabs.trackmyclass.country.rest.controller;

import com.marglabs.trackmyclass.country.model.Country;
import com.marglabs.trackmyclass.country.service.CountryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/api/common/v1")
@Tag(name = "Country Management", description = "API for retrieving country information")
public class CountryController {
    
    @Autowired
    private CountryService countryService;
    
    @Operation(summary = "Get all countries", description = "Returns a list of all countries with their details")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successful operation",
                content = @Content(schema = @Schema(implementation = Country.class)))
    })
    @GetMapping(value = "/countries", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<Country> getAllCountries() {
        return countryService.getAllCountries();
    }
    
    @Operation(summary = "Get country by ID", description = "Returns a country by its ID")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successful operation",
                content = @Content(schema = @Schema(implementation = Country.class))),
        @ApiResponse(responseCode = "404", description = "Country not found")
    })
    @GetMapping(value = "/countries/{id}", produces = "application/json")
    public ResponseEntity<Country> getCountryById(
            @Parameter(description = "Country ID") @PathVariable String id) {
        Optional<Country> country = countryService.getCountryById(id);
        return country.map(ResponseEntity::ok)
                     .orElse(ResponseEntity.notFound().build());
    }
    
    @Operation(summary = "Get country by ISO code", description = "Returns a country by its ISO code")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successful operation",
                content = @Content(schema = @Schema(implementation = Country.class))),
        @ApiResponse(responseCode = "404", description = "Country not found")
    })
    @GetMapping(value = "/countries/iso/{isoCode}", produces = "application/json")
    public ResponseEntity<Country> getCountryByIsoCode(
            @Parameter(description = "ISO country code (e.g., US, GB, IN)") @PathVariable String isoCode) {
        Optional<Country> country = countryService.getCountryByIsoCode(isoCode);
        return country.map(ResponseEntity::ok)
                     .orElse(ResponseEntity.notFound().build());
    }
    
    @Operation(summary = "Get countries by region", description = "Returns countries filtered by region")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successful operation")
    })
    @GetMapping(value = "/countries/region/{region}", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<Country> getCountriesByRegion(
            @Parameter(description = "Region name (e.g., Europe, Asia, Americas)") @PathVariable String region) {
        return countryService.getCountriesByRegion(region);
    }
    
    @Operation(summary = "Get countries by currency", description = "Returns countries that use a specific currency")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successful operation")
    })
    @GetMapping(value = "/countries/currency/{currencyCode}", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<Country> getCountriesByCurrency(
            @Parameter(description = "Currency code (e.g., USD, EUR, GBP)") @PathVariable String currencyCode) {
        return countryService.getCountriesByCurrencyCode(currencyCode);
    }
    
    @Operation(summary = "Search countries", description = "Search countries by name (partial match)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successful operation")
    })
    @GetMapping(value = "/countries/search", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<Country> searchCountries(
            @Parameter(description = "Country name to search for") @RequestParam String name) {
        return countryService.searchCountriesByName(name);
    }
    
    @Operation(summary = "Get country metadata", description = "Returns metadata like available regions, currencies, etc.")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successful operation")
    })
    @GetMapping(value = "/countries/metadata", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public Map<String, Object> getCountryMetadata() {
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("regions", countryService.getDistinctRegions());
        metadata.put("currencies", countryService.getDistinctCurrencies());
        metadata.put("currencyCodes", countryService.getDistinctCurrencyCodes());
        metadata.put("totalCountries", countryService.getAllCountries().size());
        return metadata;
    }
    
    @Operation(summary = "Get countries summary", description = "Returns a simplified list with just ID, name, ISO code, and currency")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successful operation")
    })
    @GetMapping(value = "/countries/summary", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<Map<String, String>> getCountriesSummary() {
        return countryService.getAllCountries().stream()
                .map(country -> {
                    Map<String, String> summary = new HashMap<>();
                    summary.put("id", country.getId());
                    summary.put("name", country.getName());
                    summary.put("isoCode", country.getIsoCode());
                    summary.put("currency", country.getCurrency());
                    summary.put("currencyCode", country.getCurrencyCode());
                    return summary;
                })
                .toList();
    }
}
