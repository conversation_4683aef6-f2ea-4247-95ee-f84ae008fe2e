package com.marglabs.trackmyclass.country.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.marglabs.trackmyclass.country.model.Country;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class CountryService {
    private static final Logger logger = LoggerFactory.getLogger(CountryService.class);
    
    @Autowired
    private ObjectMapper objectMapper;
    
    private List<Country> countries = new ArrayList<>();
    
    @PostConstruct
    public void loadCountries() {
        try {
            ClassPathResource resource = new ClassPathResource("data/countries.json");
            InputStream inputStream = resource.getInputStream();
            
            countries = objectMapper.readValue(inputStream, new TypeReference<List<Country>>() {});
            logger.info("Loaded {} countries from JSON file", countries.size());
            
        } catch (IOException e) {
            logger.error("Failed to load countries from JSON file", e);
            countries = new ArrayList<>();
        }
    }
    
    /**
     * Get all countries
     */
    public List<Country> getAllCountries() {
        return new ArrayList<>(countries);
    }
    
    /**
     * Get country by ID
     */
    public Optional<Country> getCountryById(String id) {
        return countries.stream()
                .filter(country -> country.getId().equals(id))
                .findFirst();
    }
    
    /**
     * Get country by ISO code
     */
    public Optional<Country> getCountryByIsoCode(String isoCode) {
        return countries.stream()
                .filter(country -> country.getIsoCode().equalsIgnoreCase(isoCode))
                .findFirst();
    }
    
    /**
     * Get countries by region
     */
    public List<Country> getCountriesByRegion(String region) {
        return countries.stream()
                .filter(country -> country.getRegion().equalsIgnoreCase(region))
                .collect(Collectors.toList());
    }
    
    /**
     * Get countries by currency code
     */
    public List<Country> getCountriesByCurrencyCode(String currencyCode) {
        return countries.stream()
                .filter(country -> country.getCurrencyCode().equalsIgnoreCase(currencyCode))
                .collect(Collectors.toList());
    }
    
    /**
     * Search countries by name (case-insensitive, partial match)
     */
    public List<Country> searchCountriesByName(String name) {
        return countries.stream()
                .filter(country -> country.getName().toLowerCase().contains(name.toLowerCase()))
                .collect(Collectors.toList());
    }
    
    /**
     * Get distinct regions
     */
    public List<String> getDistinctRegions() {
        return countries.stream()
                .map(Country::getRegion)
                .distinct()
                .sorted()
                .collect(Collectors.toList());
    }
    
    /**
     * Get distinct currency codes
     */
    public List<String> getDistinctCurrencyCodes() {
        return countries.stream()
                .map(Country::getCurrencyCode)
                .distinct()
                .sorted()
                .collect(Collectors.toList());
    }
    
    /**
     * Get distinct currencies
     */
    public List<String> getDistinctCurrencies() {
        return countries.stream()
                .map(Country::getCurrency)
                .distinct()
                .sorted()
                .collect(Collectors.toList());
    }
}
