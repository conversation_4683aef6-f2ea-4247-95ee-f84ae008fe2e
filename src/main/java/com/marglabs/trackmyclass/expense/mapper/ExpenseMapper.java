package com.marglabs.trackmyclass.expense.mapper;

import com.marglabs.trackmyclass.expense.entity.ExpenseEntity;
import com.marglabs.trackmyclass.expense.model.Expense;
import com.marglabs.trackmyclass.expense.model.ExpenseRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(componentModel = "spring")
public interface ExpenseMapper {
    
    @Mapping(target = "teacherName", ignore = true) // Will be set by service
    @Mapping(target = "className", ignore = true) // Will be set by service
    @Mapping(target = "createdDate", expression = "java(entity.getCreatedDate() != null ? entity.getCreatedDate().toEpochMilli() : 0)")
    @Mapping(target = "updatedDate", expression = "java(entity.getUpdatedDate() != null ? entity.getUpdatedDate().toEpochMilli() : 0)")
    Expense toDto(ExpenseEntity entity);
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "teacherId", ignore = true) // Will be set by service
    @Mapping(target = "createdDate", ignore = true)
    @Mapping(target = "updatedDate", ignore = true)
    ExpenseEntity toEntity(ExpenseRequest request);
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "teacherId", ignore = true)
    @Mapping(target = "createdDate", ignore = true)
    @Mapping(target = "updatedDate", ignore = true)
    void updateEntityFromRequest(ExpenseRequest request, @MappingTarget ExpenseEntity entity);
}
