package com.marglabs.trackmyclass.expense.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.Instant;

@Entity
@Table(name = "expenses")
@Data
@Accessors(chain = true)
public class ExpenseEntity {
    
    @Id
    @Column(name = "id")
    private String id;
    
    @Column(name = "teacher_id", nullable = false)
    private String teacherId;
    
    @Column(name = "amount", nullable = false, precision = 10, scale = 2)
    private BigDecimal amount;
    
    @Column(name = "category", nullable = false, length = 50)
    private String category;
    
    @Column(name = "title", nullable = false, length = 200)
    private String title;
    
    @Column(name = "description", length = 1000)
    private String description;
    
    @Column(name = "expense_date", nullable = false, length = 10)
    private String expenseDate;
    
    @Column(name = "class_id", length = 50)
    private String classId;
    
    @CreationTimestamp
    @Column(name = "created_date", nullable = false, updatable = false)
    private Instant createdDate;
    
    @UpdateTimestamp
    @Column(name = "updated_date", nullable = false)
    private Instant updatedDate;
}
