package com.marglabs.trackmyclass.expense.repository;

import com.marglabs.trackmyclass.expense.entity.ExpenseEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

@Repository
public interface ExpenseRepository extends JpaRepository<ExpenseEntity, String> {
    
    // Find expenses by teacher ID
    List<ExpenseEntity> findByTeacherId(String teacherId);
    
    // Find expenses by teacher ID and category
    List<ExpenseEntity> findByTeacherIdAndCategory(String teacherId, String category);
    
    // Find expenses by teacher ID and date range
    @Query("SELECT e FROM ExpenseEntity e WHERE e.teacherId = :teacherId AND e.expenseDate BETWEEN :startDate AND :endDate ORDER BY e.expenseDate DESC")
    List<ExpenseEntity> findByTeacherIdAndDateRange(@Param("teacherId") String teacherId, 
                                                   @Param("startDate") String startDate, 
                                                   @Param("endDate") String endDate);
    
    // Find expenses by teacher ID ordered by date (most recent first)
    List<ExpenseEntity> findByTeacherIdOrderByExpenseDateDesc(String teacherId);
    
    // Find expenses by teacher ID and category in date range
    @Query("SELECT e FROM ExpenseEntity e WHERE e.teacherId = :teacherId AND e.category = :category AND e.expenseDate BETWEEN :startDate AND :endDate ORDER BY e.expenseDate DESC")
    List<ExpenseEntity> findByTeacherIdAndCategoryAndDateRange(@Param("teacherId") String teacherId,
                                                              @Param("category") String category,
                                                              @Param("startDate") String startDate,
                                                              @Param("endDate") String endDate);
    
    // Calculate total expenses by teacher
    @Query("SELECT SUM(e.amount) FROM ExpenseEntity e WHERE e.teacherId = :teacherId")
    BigDecimal calculateTotalExpensesByTeacher(@Param("teacherId") String teacherId);
    
    // Calculate total expenses by teacher and date range
    @Query("SELECT SUM(e.amount) FROM ExpenseEntity e WHERE e.teacherId = :teacherId AND e.expenseDate BETWEEN :startDate AND :endDate")
    BigDecimal calculateTotalExpensesByTeacherAndDateRange(@Param("teacherId") String teacherId,
                                                          @Param("startDate") String startDate,
                                                          @Param("endDate") String endDate);
    
    // Calculate total expenses by teacher and category
    @Query("SELECT SUM(e.amount) FROM ExpenseEntity e WHERE e.teacherId = :teacherId AND e.category = :category")
    BigDecimal calculateTotalExpensesByTeacherAndCategory(@Param("teacherId") String teacherId,
                                                         @Param("category") String category);
    
    // Count expenses by teacher
    long countByTeacherId(String teacherId);
    
    // Count expenses by teacher and category
    long countByTeacherIdAndCategory(String teacherId, String category);
    
    // Find expenses by teacher ID with pagination (for recent expenses)
    @Query("SELECT e FROM ExpenseEntity e WHERE e.teacherId = :teacherId ORDER BY e.createdDate DESC")
    List<ExpenseEntity> findRecentExpensesByTeacher(@Param("teacherId") String teacherId);

    // Get monthly expense data for last 12 months
    @Query("SELECT SUBSTRING(e.expenseDate, 1, 7) as month, SUM(e.amount) as totalAmount, COUNT(e) as expenseCount " +
           "FROM ExpenseEntity e WHERE e.teacherId = :teacherId AND e.expenseDate >= :startDate " +
           "GROUP BY SUBSTRING(e.expenseDate, 1, 7) ORDER BY month DESC")
    List<Object[]> getMonthlyExpenseData(@Param("teacherId") String teacherId, @Param("startDate") String startDate);

    // Get category-wise expense distribution
    @Query("SELECT e.category, SUM(e.amount) as totalAmount, COUNT(e) as expenseCount " +
           "FROM ExpenseEntity e WHERE e.teacherId = :teacherId " +
           "GROUP BY e.category ORDER BY totalAmount DESC")
    List<Object[]> getCategoryExpenseData(@Param("teacherId") String teacherId);

    // Get category-wise expense distribution for date range
    @Query("SELECT e.category, SUM(e.amount) as totalAmount, COUNT(e) as expenseCount " +
           "FROM ExpenseEntity e WHERE e.teacherId = :teacherId AND e.expenseDate BETWEEN :startDate AND :endDate " +
           "GROUP BY e.category ORDER BY totalAmount DESC")
    List<Object[]> getCategoryExpenseDataForDateRange(@Param("teacherId") String teacherId,
                                                     @Param("startDate") String startDate,
                                                     @Param("endDate") String endDate);

    // Find expenses by teacher ID, date range, and optional filters
    @Query("SELECT e FROM ExpenseEntity e WHERE e.teacherId = :teacherId " +
           "AND e.expenseDate BETWEEN :startDate AND :endDate " +
           "AND (:category IS NULL OR e.category = :category) " +
           "AND (:title IS NULL OR LOWER(e.title) LIKE LOWER(CONCAT('%', :title, '%'))) " +
           "AND (:description IS NULL OR LOWER(e.description) LIKE LOWER(CONCAT('%', :description, '%'))) " +
           "ORDER BY e.expenseDate DESC")
    List<ExpenseEntity> findByTeacherIdAndDateRangeWithFilters(@Param("teacherId") String teacherId,
                                                              @Param("startDate") String startDate,
                                                              @Param("endDate") String endDate,
                                                              @Param("category") String category,
                                                              @Param("title") String title,
                                                              @Param("description") String description);
}
