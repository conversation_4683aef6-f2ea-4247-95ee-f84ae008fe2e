package com.marglabs.trackmyclass.expense.model;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class ExpenseRequest {
    
    @NotNull(message = "Amount is required")
    @DecimalMin(value = "0.01", message = "Amount must be greater than 0")
    private BigDecimal amount;
    
    @NotBlank(message = "Category is required")
    @Pattern(regexp = "^(TEACHING_MATERIALS|TRANSPORTATION|OFFICE_SUPPLIES|TECHNOLOGY|PROFESSIONAL_DEVELOPMENT|MEALS|ACCOMMODATION|OTHER)$",
             message = "Category must be one of: TEACHING_MATERIALS, TRANSPORTATION, OFFICE_SUPPLIES, TECHNOLOGY, PROFESSIONAL_DEVELOPMENT, MEALS, ACCOMMODATION, OTHER")
    private String category;
    
    @NotBlank(message = "Title is required")
    @Size(min = 3, max = 200, message = "Title must be between 3 and 200 characters")
    private String title;
    
    @Size(max = 1000, message = "Description cannot exceed 1000 characters")
    private String description;
    
    @NotBlank(message = "Expense date is required")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "Expense date must be in YYYY-MM-DD format")
    private String expenseDate;
    
    private String classId;
}
