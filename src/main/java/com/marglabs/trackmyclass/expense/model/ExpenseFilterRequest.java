package com.marglabs.trackmyclass.expense.model;

import jakarta.validation.constraints.Pattern;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ExpenseFilterRequest {
    
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "Start date must be in YYYY-MM-DD format")
    private String startDate;
    
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "End date must be in YYYY-MM-DD format")
    private String endDate;
    
    @Pattern(regexp = "^(TEACHING_MATERIALS|TRANSPORTATION|OFFICE_SUPPLIES|TECHNOLOGY|PROFESSIONAL_DEVELOPMENT|MEALS|ACCOMMODATION|OTHER)$",
             message = "Category must be one of: TEACHING_MATERIALS, TRANSPORTATION, OFFICE_SUPPLIES, TECHNOLOGY, PROFESSIONAL_DEVELOPMENT, MEALS, ACCOMMODATION, OTHER")
    private String category;
    
    private String title;
    
    private String description;
}
