package com.marglabs.trackmyclass.expense.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class Expense {
    
    private String id;
    private String teacherId;
    private String teacherName;
    private BigDecimal amount;
    private String category;
    private String title;
    private String description;
    private String expenseDate; // YYYY-MM-DD format
    private String classId;
    private String className;
    private long createdDate;
    private long updatedDate;
}
