package com.marglabs.trackmyclass.expense.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class CategoryExpenseData {
    
    private String category;
    private String categoryDisplayName;
    private BigDecimal totalAmount;
    private long expenseCount;
    private BigDecimal percentage; // Percentage of total expenses
}
