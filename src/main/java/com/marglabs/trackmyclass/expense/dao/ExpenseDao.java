package com.marglabs.trackmyclass.expense.dao;

import com.marglabs.common.rest.error.GeneralException;
import com.marglabs.trackmyclass.expense.entity.ExpenseEntity;
import com.marglabs.trackmyclass.expense.repository.ExpenseRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

@Component
public class ExpenseDao {
    
    @Autowired
    private ExpenseRepository repository;
    
    public ExpenseEntity save(ExpenseEntity entity) {
        return repository.save(entity);
    }
    
    public ExpenseEntity getById(String id) {
        return repository.findById(id)
                .orElseThrow(() -> new GeneralException(HttpStatus.NOT_FOUND, "EXPENSE", "EXPENSE_NOT_FOUND",
                        "expense_not_found_details", id));
    }
    
    public Optional<ExpenseEntity> findById(String id) {
        return repository.findById(id);
    }
    
    public List<ExpenseEntity> getByTeacherId(String teacherId) {
        return repository.findByTeacherId(teacherId);
    }
    
    public List<ExpenseEntity> getByTeacherIdAndCategory(String teacherId, String category) {
        return repository.findByTeacherIdAndCategory(teacherId, category);
    }
    
    public List<ExpenseEntity> getByTeacherIdAndDateRange(String teacherId, String startDate, String endDate) {
        return repository.findByTeacherIdAndDateRange(teacherId, startDate, endDate);
    }
    
    public List<ExpenseEntity> getByTeacherIdOrderByDate(String teacherId) {
        return repository.findByTeacherIdOrderByExpenseDateDesc(teacherId);
    }
    
    public List<ExpenseEntity> getByTeacherIdAndCategoryAndDateRange(String teacherId, String category, String startDate, String endDate) {
        return repository.findByTeacherIdAndCategoryAndDateRange(teacherId, category, startDate, endDate);
    }
    
    public BigDecimal calculateTotalExpensesByTeacher(String teacherId) {
        BigDecimal total = repository.calculateTotalExpensesByTeacher(teacherId);
        return total != null ? total : BigDecimal.ZERO;
    }
    
    public BigDecimal calculateTotalExpensesByTeacherAndDateRange(String teacherId, String startDate, String endDate) {
        BigDecimal total = repository.calculateTotalExpensesByTeacherAndDateRange(teacherId, startDate, endDate);
        return total != null ? total : BigDecimal.ZERO;
    }
    
    public BigDecimal calculateTotalExpensesByTeacherAndCategory(String teacherId, String category) {
        BigDecimal total = repository.calculateTotalExpensesByTeacherAndCategory(teacherId, category);
        return total != null ? total : BigDecimal.ZERO;
    }
    
    public long countByTeacherId(String teacherId) {
        return repository.countByTeacherId(teacherId);
    }
    
    public long countByTeacherIdAndCategory(String teacherId, String category) {
        return repository.countByTeacherIdAndCategory(teacherId, category);
    }
    
    public List<ExpenseEntity> getRecentExpensesByTeacher(String teacherId) {
        return repository.findRecentExpensesByTeacher(teacherId);
    }
    
    public ExpenseEntity update(ExpenseEntity entity) {
        return repository.save(entity);
    }
    
    public void deleteById(String id) {
        repository.deleteById(id);
    }
    
    public boolean existsById(String id) {
        return repository.existsById(id);
    }

    public List<Object[]> getMonthlyExpenseData(String teacherId, String startDate) {
        return repository.getMonthlyExpenseData(teacherId, startDate);
    }

    public List<Object[]> getCategoryExpenseData(String teacherId) {
        return repository.getCategoryExpenseData(teacherId);
    }

    public List<Object[]> getCategoryExpenseDataForDateRange(String teacherId, String startDate, String endDate) {
        return repository.getCategoryExpenseDataForDateRange(teacherId, startDate, endDate);
    }

    public List<ExpenseEntity> getByTeacherIdAndDateRangeWithFilters(String teacherId, String startDate, String endDate,
                                                                    String category, String title, String description) {
        return repository.findByTeacherIdAndDateRangeWithFilters(teacherId, startDate, endDate, category, title, description);
    }
    
    public BigDecimal getTotalExpensesByTeacher(String teacherId) {
        return calculateTotalExpensesByTeacher(teacherId);
    }
}
