package com.marglabs.trackmyclass.expense.service;

import com.marglabs.common.rest.error.GeneralException;
import com.marglabs.trackmyclass.expense.dao.ExpenseDao;
import com.marglabs.trackmyclass.expense.entity.ExpenseEntity;
import com.marglabs.trackmyclass.expense.mapper.ExpenseMapper;
import com.marglabs.trackmyclass.expense.model.Expense;
import com.marglabs.trackmyclass.expense.model.ExpenseRequest;
import com.marglabs.trackmyclass.expense.model.MonthlyExpenseData;
import com.marglabs.trackmyclass.expense.model.CategoryExpenseData;
import com.marglabs.trackmyclass.user.service.UserService;
import com.marglabs.trackmyclass.classroom.service.ClassroomService;
import com.marglabs.common.utils.IdGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class ExpenseService {
    
    private static final Logger logger = LoggerFactory.getLogger(ExpenseService.class);
    
    @Autowired
    private ExpenseDao expenseDao;
    
    @Autowired
    private ExpenseMapper mapper;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private ClassroomService classroomService;
    
    @Autowired
    private IdGenerator idGenerator;
    
    /**
     * Create a new expense record
     */
    @Transactional
    public Expense createExpense(ExpenseRequest request, String teacherId) {
        logger.info("Creating expense for teacher {} with title: {}", teacherId, request.getTitle());
        
        // Validate the request
        validateExpenseRequest(request);
        
        // Verify teacher exists
        var teacher = userService.getUserById(teacherId);
        
        // Create entity
        ExpenseEntity entity = mapper.toEntity(request);
        entity.setId(idGenerator.generateId());
        entity.setTeacherId(teacherId);
        
        // Save entity
        ExpenseEntity savedEntity = expenseDao.save(entity);
        
        logger.info("Successfully created expense with ID: {} for teacher: {}", savedEntity.getId(), teacherId);
        return enrichExpenseWithDetails(mapper.toDto(savedEntity));
    }
    
    /**
     * Update an existing expense
     */
    @Transactional
    public Expense updateExpense(String expenseId, ExpenseRequest request, String teacherId) {
        logger.info("Updating expense {} for teacher {}", expenseId, teacherId);
        
        // Validate the request
        validateExpenseRequest(request);
        
        // Get existing expense and verify ownership
        ExpenseEntity entity = expenseDao.getById(expenseId);
        
        if (!entity.getTeacherId().equals(teacherId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "EXPENSE", "UNAUTHORIZED_EXPENSE_ACCESS",
                    "teacher_not_authorized_for_expense", teacherId, expenseId);
        }
        
        // Update entity
        mapper.updateEntityFromRequest(request, entity);
        
        // Save updated entity
        ExpenseEntity updatedEntity = expenseDao.update(entity);
        
        logger.info("Successfully updated expense with ID: {}", expenseId);
        return enrichExpenseWithDetails(mapper.toDto(updatedEntity));
    }
    
    /**
     * Get expense by ID
     */
    public Expense getExpenseById(String expenseId, String teacherId) {
        logger.info("Getting expense {} for teacher: {}", expenseId, teacherId);
        
        ExpenseEntity entity = expenseDao.getById(expenseId);
        
        // Verify ownership
        if (!entity.getTeacherId().equals(teacherId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "EXPENSE", "UNAUTHORIZED_EXPENSE_ACCESS",
                    "teacher_not_authorized_for_expense", teacherId, expenseId);
        }
        
        return enrichExpenseWithDetails(mapper.toDto(entity));
    }
    
    /**
     * Get all expenses for a teacher
     */
    public List<Expense> getExpensesByTeacher(String teacherId) {
        logger.info("Getting all expenses for teacher: {}", teacherId);
        
        // Verify teacher exists
        userService.getUserById(teacherId);
        
        List<ExpenseEntity> entities = expenseDao.getByTeacherIdOrderByDate(teacherId);
        
        return entities.stream()
                .map(entity -> enrichExpenseWithDetails(mapper.toDto(entity)))
                .collect(Collectors.toList());
    }
    
    /**
     * Get expenses by teacher and category
     */
    public List<Expense> getExpensesByTeacherAndCategory(String teacherId, String category) {
        logger.info("Getting expenses for teacher {} in category: {}", teacherId, category);
        
        // Verify teacher exists
        userService.getUserById(teacherId);
        
        List<ExpenseEntity> entities = expenseDao.getByTeacherIdAndCategory(teacherId, category);
        
        return entities.stream()
                .map(entity -> enrichExpenseWithDetails(mapper.toDto(entity)))
                .collect(Collectors.toList());
    }
    
    /**
     * Get expenses by teacher and date range
     */
    public List<Expense> getExpensesByTeacherAndDateRange(String teacherId, String startDate, String endDate) {
        logger.info("Getting expenses for teacher {} from {} to {}", teacherId, startDate, endDate);

        // Verify teacher exists
        userService.getUserById(teacherId);

        // Validate dates
        validateDate(startDate);
        validateDate(endDate);

        List<ExpenseEntity> entities = expenseDao.getByTeacherIdAndDateRange(teacherId, startDate, endDate);

        return entities.stream()
                .map(entity -> enrichExpenseWithDetails(mapper.toDto(entity)))
                .collect(Collectors.toList());
    }

    /**
     * Get expenses by teacher, date range, and optional filters (category, title, description)
     */
    public List<Expense> getExpensesByTeacherAndDateRangeWithFilters(String teacherId, String startDate, String endDate,
                                                                    String category, String title, String description) {
        logger.info("Getting filtered expenses for teacher {} from {} to {} with filters - category: {}, title: {}, description: {}",
                   teacherId, startDate, endDate, category, title, description);

        // Verify teacher exists
        userService.getUserById(teacherId);

        // Validate dates
        validateDate(startDate);
        validateDate(endDate);

        // Validate category if provided
        if (category != null && !category.trim().isEmpty()) {
            validateCategory(category);
        }

        // Clean up filter parameters (null or empty strings should be treated as null)
        String cleanCategory = (category != null && !category.trim().isEmpty()) ? category.trim() : null;
        String cleanTitle = (title != null && !title.trim().isEmpty()) ? title.trim() : null;
        String cleanDescription = (description != null && !description.trim().isEmpty()) ? description.trim() : null;

        List<ExpenseEntity> entities = expenseDao.getByTeacherIdAndDateRangeWithFilters(
                teacherId, startDate, endDate, cleanCategory, cleanTitle, cleanDescription);

        return entities.stream()
                .map(entity -> enrichExpenseWithDetails(mapper.toDto(entity)))
                .collect(Collectors.toList());
    }
    
    /**
     * Delete an expense
     */
    @Transactional
    public void deleteExpense(String expenseId, String teacherId) {
        logger.info("Deleting expense {} for teacher {}", expenseId, teacherId);
        
        ExpenseEntity entity = expenseDao.getById(expenseId);
        
        // Verify ownership
        if (!entity.getTeacherId().equals(teacherId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "EXPENSE", "UNAUTHORIZED_EXPENSE_ACCESS",
                    "teacher_not_authorized_for_expense", teacherId, expenseId);
        }
        
        expenseDao.deleteById(expenseId);
        
        logger.info("Successfully deleted expense with ID: {}", expenseId);
    }
    
    /**
     * Calculate total expenses for a teacher
     */
    public BigDecimal calculateTotalExpenses(String teacherId) {
        logger.info("Calculating total expenses for teacher: {}", teacherId);
        
        // Verify teacher exists
        userService.getUserById(teacherId);
        
        return expenseDao.calculateTotalExpensesByTeacher(teacherId);
    }
    
    /**
     * Calculate total expenses for a teacher in date range
     */
    public BigDecimal calculateTotalExpensesInDateRange(String teacherId, String startDate, String endDate) {
        logger.info("Calculating total expenses for teacher {} from {} to {}", teacherId, startDate, endDate);
        
        // Verify teacher exists
        userService.getUserById(teacherId);
        
        // Validate dates
        validateDate(startDate);
        validateDate(endDate);
        
        return expenseDao.calculateTotalExpensesByTeacherAndDateRange(teacherId, startDate, endDate);
    }

    /**
     * Get monthly expense data for last 12 months for bar chart
     */
    public List<MonthlyExpenseData> getMonthlyExpenseChartData(String teacherId) {
        logger.info("Getting monthly expense chart data for teacher: {}", teacherId);

        // Verify teacher exists
        userService.getUserById(teacherId);

        // Calculate start date (12 months ago)
        LocalDate startDate = LocalDate.now().minusMonths(12).withDayOfMonth(1);
        String startDateStr = startDate.format(DateTimeFormatter.ISO_LOCAL_DATE);

        // Get monthly data from database
        List<Object[]> rawData = expenseDao.getMonthlyExpenseData(teacherId, startDateStr);

        // Convert to MonthlyExpenseData objects
        List<MonthlyExpenseData> monthlyData = rawData.stream()
                .map(row -> {
                    String month = (String) row[0];
                    BigDecimal totalAmount = (BigDecimal) row[1];
                    Long expenseCount = (Long) row[2];

                    return new MonthlyExpenseData()
                            .setMonth(month)
                            .setMonthName(formatMonthName(month))
                            .setTotalAmount(totalAmount != null ? totalAmount : BigDecimal.ZERO)
                            .setExpenseCount(expenseCount != null ? expenseCount : 0L);
                })
                .collect(Collectors.toList());

        // Fill in missing months with zero data
        return fillMissingMonths(monthlyData, 12);
    }

    /**
     * Get category-wise expense distribution for pie/bar chart
     */
    public List<CategoryExpenseData> getCategoryExpenseChartData(String teacherId) {
        logger.info("Getting category expense chart data for teacher: {}", teacherId);

        // Verify teacher exists
        userService.getUserById(teacherId);

        // Get category data from database
        List<Object[]> rawData = expenseDao.getCategoryExpenseData(teacherId);

        // Calculate total for percentage calculation
        BigDecimal grandTotal = rawData.stream()
                .map(row -> (BigDecimal) row[1])
                .filter(amount -> amount != null)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // Convert to CategoryExpenseData objects
        return rawData.stream()
                .map(row -> {
                    String category = (String) row[0];
                    BigDecimal totalAmount = (BigDecimal) row[1];
                    Long expenseCount = (Long) row[2];

                    BigDecimal percentage = BigDecimal.ZERO;
                    if (grandTotal.compareTo(BigDecimal.ZERO) > 0 && totalAmount != null) {
                        percentage = totalAmount.divide(grandTotal, 4, BigDecimal.ROUND_HALF_UP)
                                .multiply(new BigDecimal("100"));
                    }

                    return new CategoryExpenseData()
                            .setCategory(category)
                            .setCategoryDisplayName(formatCategoryDisplayName(category))
                            .setTotalAmount(totalAmount != null ? totalAmount : BigDecimal.ZERO)
                            .setExpenseCount(expenseCount != null ? expenseCount : 0L)
                            .setPercentage(percentage);
                })
                .collect(Collectors.toList());
    }

    /**
     * Get category-wise expense distribution for specific date range
     */
    public List<CategoryExpenseData> getCategoryExpenseChartDataForDateRange(String teacherId, String startDate, String endDate) {
        logger.info("Getting category expense chart data for teacher {} from {} to {}", teacherId, startDate, endDate);

        // Verify teacher exists
        userService.getUserById(teacherId);

        // Validate dates
        validateDate(startDate);
        validateDate(endDate);

        // Get category data from database
        List<Object[]> rawData = expenseDao.getCategoryExpenseDataForDateRange(teacherId, startDate, endDate);

        // Calculate total for percentage calculation
        BigDecimal grandTotal = rawData.stream()
                .map(row -> (BigDecimal) row[1])
                .filter(amount -> amount != null)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // Convert to CategoryExpenseData objects
        return rawData.stream()
                .map(row -> {
                    String category = (String) row[0];
                    BigDecimal totalAmount = (BigDecimal) row[1];
                    Long expenseCount = (Long) row[2];

                    BigDecimal percentage = BigDecimal.ZERO;
                    if (grandTotal.compareTo(BigDecimal.ZERO) > 0 && totalAmount != null) {
                        percentage = totalAmount.divide(grandTotal, 4, BigDecimal.ROUND_HALF_UP)
                                .multiply(new BigDecimal("100"));
                    }

                    return new CategoryExpenseData()
                            .setCategory(category)
                            .setCategoryDisplayName(formatCategoryDisplayName(category))
                            .setTotalAmount(totalAmount != null ? totalAmount : BigDecimal.ZERO)
                            .setExpenseCount(expenseCount != null ? expenseCount : 0L)
                            .setPercentage(percentage);
                })
                .collect(Collectors.toList());
    }

    private void validateExpenseRequest(ExpenseRequest request) {
        // Validate date format
        validateDate(request.getExpenseDate());
        
        // Validate amount
        if (request.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new GeneralException(HttpStatus.BAD_REQUEST, "EXPENSE", "INVALID_AMOUNT",
                    "amount_must_be_positive");
        }
        
        // Validate classId if provided
        if (request.getClassId() != null && !request.getClassId().trim().isEmpty()) {
            try {
                classroomService.getClassroomById(request.getClassId());
            } catch (Exception e) {
                throw new GeneralException(HttpStatus.BAD_REQUEST, "EXPENSE", "INVALID_CLASS_ID",
                        "invalid_class_id", request.getClassId());
            }
        }
    }
    
    private void validateDate(String date) {
        try {
            LocalDate.parse(date, DateTimeFormatter.ISO_LOCAL_DATE);
        } catch (DateTimeParseException e) {
            throw new GeneralException(HttpStatus.BAD_REQUEST, "EXPENSE", "INVALID_DATE_FORMAT",
                    "invalid_date_format", date);
        }
    }

    private void validateCategory(String category) {
        List<String> validCategories = List.of(
                "TEACHING_MATERIALS", "TRANSPORTATION", "OFFICE_SUPPLIES",
                "TECHNOLOGY", "PROFESSIONAL_DEVELOPMENT", "MEALS",
                "ACCOMMODATION", "OTHER"
        );

        if (!validCategories.contains(category)) {
            throw new GeneralException(HttpStatus.BAD_REQUEST, "EXPENSE", "INVALID_CATEGORY",
                    "invalid_expense_category", category);
        }
    }
    
    private Expense enrichExpenseWithDetails(Expense expense) {
        try {
            // Get teacher name
            var teacher = userService.getUserById(expense.getTeacherId());
            expense.setTeacherName(teacher.getName());
        } catch (Exception e) {
            logger.warn("Failed to enrich expense details for expense {}: {}", expense.getId(), e.getMessage());
            expense.setTeacherName("Unknown Teacher");
        }
        
        // Get class name if classId is present
        if (expense.getClassId() != null && !expense.getClassId().trim().isEmpty()) {
            try {
                var classroom = classroomService.getClassroomById(expense.getClassId());
                expense.setClassName(classroom.getClassName());
            } catch (Exception e) {
                logger.warn("Failed to get class name for classId {}: {}", expense.getClassId(), e.getMessage());
                expense.setClassName("Unknown Class");
            }
        }
        
        return expense;
    }

    private String formatMonthName(String month) {
        try {
            LocalDate date = LocalDate.parse(month + "-01", DateTimeFormatter.ISO_LOCAL_DATE);
            return date.format(DateTimeFormatter.ofPattern("MMMM yyyy"));
        } catch (Exception e) {
            return month;
        }
    }

    private String formatCategoryDisplayName(String category) {
        if (category == null) return "Unknown";

        switch (category) {
            case "TEACHING_MATERIALS": return "Teaching Materials";
            case "TRANSPORTATION": return "Transportation";
            case "OFFICE_SUPPLIES": return "Office Supplies";
            case "TECHNOLOGY": return "Technology";
            case "PROFESSIONAL_DEVELOPMENT": return "Professional Development";
            case "MEALS": return "Meals";
            case "ACCOMMODATION": return "Accommodation";
            case "OTHER": return "Other";
            default: return category.replace("_", " ");
        }
    }

    private List<MonthlyExpenseData> fillMissingMonths(List<MonthlyExpenseData> existingData, int monthCount) {
        List<MonthlyExpenseData> result = new ArrayList<>();
        LocalDate currentMonth = LocalDate.now().withDayOfMonth(1);

        // Create a map for quick lookup of existing data
        Map<String, MonthlyExpenseData> dataMap = existingData.stream()
                .collect(Collectors.toMap(MonthlyExpenseData::getMonth, data -> data));

        // Generate data for last 'monthCount' months
        for (int i = monthCount - 1; i >= 0; i--) {
            LocalDate month = currentMonth.minusMonths(i);
            String monthKey = month.format(DateTimeFormatter.ofPattern("yyyy-MM"));

            MonthlyExpenseData data = dataMap.get(monthKey);
            if (data == null) {
                // Create zero data for missing months
                data = new MonthlyExpenseData()
                        .setMonth(monthKey)
                        .setMonthName(formatMonthName(monthKey))
                        .setTotalAmount(BigDecimal.ZERO)
                        .setExpenseCount(0L);
            }

            result.add(data);
        }

        return result;
    }
}
