package com.marglabs.trackmyclass.expense.rest.controller;

import com.marglabs.trackmyclass.expense.model.Expense;
import com.marglabs.trackmyclass.expense.model.ExpenseRequest;
import com.marglabs.trackmyclass.expense.model.ExpenseFilterRequest;
import com.marglabs.trackmyclass.expense.model.MonthlyExpenseData;
import com.marglabs.trackmyclass.expense.model.CategoryExpenseData;
import com.marglabs.trackmyclass.expense.service.ExpenseService;
import com.marglabs.trackmyclass.user.model.User;
import com.marglabs.trackmyclass.user.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/expense/v1")
@Tag(name = "Expense Management", description = "APIs for teachers to manage their expenses")
public class ExpenseController {
    
    @Autowired
    private ExpenseService expenseService;
    
    @Autowired
    private UserService userService;
    
    @Operation(summary = "Create expense", description = "Create a new expense record for the authenticated teacher")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Expense created successfully",
                content = @Content(schema = @Schema(implementation = Expense.class))),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @PostMapping(value = "/expenses", consumes = "application/json", produces = "application/json")
    @ResponseStatus(HttpStatus.CREATED)
    public Expense createExpense(
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal,
            @Parameter(description = "Expense details") @Valid @RequestBody ExpenseRequest request) {

        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());
        return expenseService.createExpense(request, teacher.getId());
    }
    
    @Operation(summary = "Update expense", description = "Update an existing expense record")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Expense updated successfully",
                content = @Content(schema = @Schema(implementation = Expense.class))),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Not authorized to update this expense"),
        @ApiResponse(responseCode = "404", description = "Expense not found")
    })
    @PutMapping(value = "/expenses/{expenseId}", consumes = "application/json", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public Expense updateExpense(
            @Parameter(description = "Expense ID") @PathVariable String expenseId,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal,
            @Parameter(description = "Updated expense details") @Valid @RequestBody ExpenseRequest request) {

        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());
        return expenseService.updateExpense(expenseId, request, teacher.getId());
    }
    
    @Operation(summary = "Get expense by ID", description = "Get a specific expense by ID")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Expense retrieved successfully",
                content = @Content(schema = @Schema(implementation = Expense.class))),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Not authorized to view this expense"),
        @ApiResponse(responseCode = "404", description = "Expense not found")
    })
    @GetMapping(value = "/expenses/{expenseId}", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public Expense getExpense(
            @Parameter(description = "Expense ID") @PathVariable String expenseId,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {

        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());
        return expenseService.getExpenseById(expenseId, teacher.getId());
    }
    
    @Operation(summary = "Get all expenses", description = "Get all expenses for the authenticated teacher")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Expenses retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/expenses", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<Expense> getTeacherExpenses(@Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());
        return expenseService.getExpensesByTeacher(teacher.getId());
    }
    
    @Operation(summary = "Get expenses by category", description = "Get expenses filtered by category")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Expenses retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/expenses/category/{category}", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<Expense> getExpensesByCategory(
            @Parameter(description = "Expense category") @PathVariable String category,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());
        return expenseService.getExpensesByTeacherAndCategory(teacher.getId(), category);
    }
    
    @Operation(summary = "Get expenses by date range with filters",
               description = "Get expenses within a specific date range with optional category, title, and description filters")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Expenses retrieved successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid date format or filter parameters"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/expenses/date-range", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<Expense> getExpensesByDateRange(
            @Parameter(description = "Expense filter parameters") @Valid ExpenseFilterRequest filterRequest,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {

        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());
        return expenseService.getExpensesByTeacherAndDateRangeWithFilters(
                teacher.getId(),
                filterRequest.getStartDate(),
                filterRequest.getEndDate(),
                filterRequest.getCategory(),
                filterRequest.getTitle(),
                filterRequest.getDescription());
    }
    
    @Operation(summary = "Delete expense", description = "Delete an expense record")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "Expense deleted successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Not authorized to delete this expense"),
        @ApiResponse(responseCode = "404", description = "Expense not found")
    })
    @DeleteMapping(value = "/expenses/{expenseId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void deleteExpense(
            @Parameter(description = "Expense ID") @PathVariable String expenseId,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {

        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());
        expenseService.deleteExpense(expenseId, teacher.getId());
    }
    


    @Operation(summary = "Get monthly expense chart data",
               description = "Get expense data for the last 12 months for bar chart visualization")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Monthly expense data retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/expenses/chart/monthly", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<MonthlyExpenseData> getMonthlyExpenseChartData(@Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());
        return expenseService.getMonthlyExpenseChartData(teacher.getId());
    }

    @Operation(summary = "Get category expense chart data",
               description = "Get expense distribution by category for pie/bar chart visualization")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Category expense data retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/expenses/chart/category", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<CategoryExpenseData> getCategoryExpenseChartData(@Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());
        return expenseService.getCategoryExpenseChartData(teacher.getId());
    }

    @Operation(summary = "Get category expense chart data for date range",
               description = "Get expense distribution by category for a specific date range for chart visualization")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Category expense data retrieved successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid date format"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/expenses/chart/category/date-range", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<CategoryExpenseData> getCategoryExpenseChartDataForDateRange(
            @Parameter(description = "Date range filter") @Valid ExpenseFilterRequest filterRequest,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {

        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());
        return expenseService.getCategoryExpenseChartDataForDateRange(
                teacher.getId(),
                filterRequest.getStartDate(),
                filterRequest.getEndDate());
    }
}
