package com.marglabs.trackmyclass.classroom.dao;

import com.marglabs.common.rest.error.EntityNotFoundException;
import com.marglabs.trackmyclass.classroom.entity.ClassroomEntity;
import com.marglabs.trackmyclass.classroom.repository.ClassroomRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

@Component
public class ClassroomDao {
    private static final Logger logger = LoggerFactory.getLogger(ClassroomDao.class);

    @Autowired
    private ClassroomRepository repository;

    public ClassroomEntity create(ClassroomEntity entity) {
        long now = Instant.now().toEpochMilli();
        entity.setCreatedDate(now);
        entity.setUpdatedDate(now);
        entity.setActive(true);
        entity.setCurrentEnrollment(0);
        return repository.save(entity);
    }

    public ClassroomEntity update(ClassroomEntity entity) {
        ClassroomEntity existingEntity = getById(entity.getId());
        entity.setCreatedDate(existingEntity.getCreatedDate());
        entity.setUpdatedDate(Instant.now().toEpochMilli());
        entity.setJoinCode(existingEntity.getJoinCode()); // Prevent join code from being changed
        return repository.save(entity);
    }

    public ClassroomEntity getById(String id) {
        Optional<ClassroomEntity> entity = repository.findById(id);
        if (entity.isPresent()) {
            return entity.get();
        }
        throw new EntityNotFoundException(HttpStatus.NOT_FOUND, "CLASSROOM", "CLASSROOM_NOT_FOUND", "classroom_not_found_details", id);
    }

    public Optional<ClassroomEntity> findByJoinCode(String joinCode) {
        return repository.findByJoinCode(joinCode);
    }

    public boolean existsByJoinCode(String joinCode) {
        return repository.existsByJoinCode(joinCode);
    }

    public List<ClassroomEntity> findByTeacherId(String teacherId){
        return repository.findByTeacherId(teacherId);
    }

    public Page<ClassroomEntity> getByTeacherIdPaginated(String teacherId, Pageable pageable) {
        logger.info("Searching for classrooms with teacherId: '{}', pageable: {}", teacherId, pageable);

        // Validate input parameters
        if (teacherId == null) {
            logger.warn("teacherId is null");
            throw new IllegalArgumentException("teacherId cannot be null");
        }

        if (teacherId.trim().isEmpty()) {
            logger.warn("teacherId is empty or whitespace");
            throw new IllegalArgumentException("teacherId cannot be empty");
        }


        // Execute the original paginated query
        Page<ClassroomEntity> result = repository.findByTeacherId(teacherId, pageable);
        logger.info("Paginated query returned {} results out of {} total elements",
                   result.getNumberOfElements(), result.getTotalElements());

        return result;
    }

    public List<ClassroomEntity> searchClassrooms(String searchTerm) {
        return repository.findByClassNameContainingIgnoreCaseOrSubjectNameContainingIgnoreCase(searchTerm, searchTerm);
    }

    public List<ClassroomEntity> getAll() {
        return repository.findAll();
    }

    public Page<ClassroomEntity> getAllPaginated(Pageable pageable) {
        return repository.findAll(pageable);
    }

    public void deleteById(String id) {
        try {
            repository.deleteById(id);
        } catch (EmptyResultDataAccessException e) {
            throw new EntityNotFoundException(HttpStatus.NOT_FOUND, "CLASSROOM", "CLASSROOM_NOT_FOUND", "classroom_not_found_details", id);
        }
    }

    public ClassroomEntity incrementEnrollment(String id) {
        ClassroomEntity entity = getById(id);
        if (entity.getCurrentEnrollment() >= entity.getCapacity()) {
            throw new IllegalStateException("Classroom is already at full capacity");
        }
        entity.setCurrentEnrollment(entity.getCurrentEnrollment() + 1);
        return repository.save(entity);
    }

    public ClassroomEntity decrementEnrollment(String id) {
        ClassroomEntity entity = getById(id);
        if (entity.getCurrentEnrollment() > 0) {
            entity.setCurrentEnrollment(entity.getCurrentEnrollment() - 1);
            return repository.save(entity);
        }
        return entity;
    }
    
    public long countByTeacherId(String teacherId) {
        return repository.countByTeacherId(teacherId);
    }
}
