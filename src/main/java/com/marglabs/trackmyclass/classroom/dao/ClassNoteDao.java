package com.marglabs.trackmyclass.classroom.dao;

import com.marglabs.common.rest.error.EntityNotFoundException;
import com.marglabs.trackmyclass.classroom.entity.ClassNoteEntity;
import com.marglabs.trackmyclass.classroom.repository.ClassNoteRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class ClassNoteDao {
    
    @Autowired
    private ClassNoteRepository repository;
    
    public ClassNoteEntity save(ClassNoteEntity entity) {
        return repository.save(entity);
    }
    
    public ClassNoteEntity getById(String id) {
        return repository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException(HttpStatus.NOT_FOUND, "CLASS_NOTE", "NOTE_NOT_FOUND",
                        "class_note_not_found", id));
    }
    
    public List<ClassNoteEntity> getByClassId(String classId) {
        return repository.findByClassIdOrderByCreatedDateDesc(classId);
    }
    
    public void deleteById(String id) {
        repository.deleteById(id);
    }
}