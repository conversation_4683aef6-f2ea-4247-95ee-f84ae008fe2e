package com.marglabs.trackmyclass.classroom.repository;

import com.marglabs.trackmyclass.classroom.entity.ClassNoteEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ClassNoteRepository extends JpaRepository<ClassNoteEntity, String> {
    
    List<ClassNoteEntity> findByClassIdOrderByCreatedDateDesc(String classId);
    
    List<ClassNoteEntity> findByTeacherIdOrderByCreatedDateDesc(String teacherId);
}