package com.marglabs.trackmyclass.classroom.repository;

import com.marglabs.trackmyclass.classroom.entity.ClassroomEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.data.rest.core.annotation.RepositoryRestResource;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
@RepositoryRestResource(exported = false)
public interface ClassroomRepository extends JpaRepository<ClassroomEntity, String> {
    Page<ClassroomEntity> findByTeacherId(String teacherId, Pageable pageable);

    // Additional debugging methods
    List<ClassroomEntity> findByTeacherId(String teacherId);

    @Query("SELECT c FROM ClassroomEntity c WHERE c.teacherId = :teacherId")
    List<ClassroomEntity> findByTeacherIdCustomQuery(@Param("teacherId") String teacherId);


    Optional<ClassroomEntity> findByJoinCode(String joinCode);
    boolean existsByJoinCode(String joinCode);
    List<ClassroomEntity> findByClassNameContainingIgnoreCaseOrSubjectNameContainingIgnoreCase(String className, String subjectName);
    
    long countByTeacherId(String teacherId);
}
