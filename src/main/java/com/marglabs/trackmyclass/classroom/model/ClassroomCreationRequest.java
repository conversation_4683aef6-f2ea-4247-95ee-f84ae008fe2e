package com.marglabs.trackmyclass.classroom.model;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
public class ClassroomCreationRequest {
    @NotBlank(message = "Class name is required")
    @Size(min = 2, max = 100, message = "Class name must be between 2 and 100 characters")
    private String className;
    
    @NotBlank(message = "Subject name is required")
    @Size(min = 2, max = 100, message = "Subject name must be between 2 and 100 characters")
    private String subjectName;
    
    @NotBlank(message = "Level is required")
    @Size(min = 1, max = 50, message = "Level must be between 2 and 50 characters")
    private String level;
    
    @NotBlank(message = "Batch name is required")
    @Size(min = 2, max = 100, message = "Batch name must be between 2 and 100 characters")
    private String batchName;
    
    @NotNull(message = "Class type is required")
    private ClassType classType;
    
    @Size(max = 5000, message = "Description cannot exceed 2000 characters")
    private String description;

    @Min(value = 1, message = "Capacity must be at least 1")
    private int capacity;

    private String posterFileId; // Optional poster image for the classroom
}
