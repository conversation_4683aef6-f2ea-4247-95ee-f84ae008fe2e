package com.marglabs.trackmyclass.classroom.model;

import com.marglabs.trackmyclass.file.model.FileInfo;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class StudentEnrolledClass {

    // Classroom Information
    private String classroomId;
    private String className;
    private String subjectName;
    private String level;
    private String batchName;
    private ClassType classType;
    private String description;
    private String joinCode;

    // Teacher Information
    private String teacherId;
    private String teacherName;

    // Classroom Poster Information
    private String posterFileId;
    private FileInfo posterFile; // Populated by service layer

    // Enrollment Information
    private String enrollmentId;
    private long enrollmentDate;
    private String enrollmentStatus; // ACTIVE, INACTIVE

    // Class Statistics (optional - can be populated for dashboard)
    private int totalStudents;
    private boolean classFull;
    private int capacity;

    // Recent Activity (optional)
    private String lastAttendanceDate;
    private String nextClassDate;
    private String nextClassTime;

    // Schedule Information
    private int totalSchedules;
    private int pendingSchedules;

    // Quick Stats for Student Dashboard
    private StudentClassStats stats;

    @Data
    @Accessors(chain = true)
    public static class StudentClassStats {
        private int totalClassesHeld;
        private int classesAttended;
        private int totalPresent;
        private int totalAbsent;
        private int totalLate;
        private double attendancePercentage;
        private String attendanceGrade; // EXCELLENT, GOOD, AVERAGE, POOR
        private boolean meetingRequirement; // Above 75%
    }
}
