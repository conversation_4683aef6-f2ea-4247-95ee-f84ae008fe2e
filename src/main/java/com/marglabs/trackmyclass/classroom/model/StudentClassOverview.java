package com.marglabs.trackmyclass.classroom.model;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class StudentClassOverview {
    
    private String classId;
    private String className;
    private StudentAttendanceStats attendanceStats;
    private StudentPaymentStats paymentStats;
    
    @Data
    @Accessors(chain = true)
    public static class StudentAttendanceStats {
        private int totalClassesHeld;
        private int classesAttended;
        private int totalPresent;
        private int totalAbsent;
        private int totalLate;
        private double attendancePercentage;
    }
    
    @Data
    @Accessors(chain = true)
    public static class StudentPaymentStats {
        private int totalPaymentsDue;
        private int paymentsCompleted;
        private int paymentsPending;
        private double paymentCompletionPercentage;
    }
}