package com.marglabs.trackmyclass.classroom.model;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ClassOverview {
    
    private String classId;
    private String className;
    private ClassCompletionStatus completionStatus;
    private AttendanceStatistics attendanceStatistics;
    private PaymentStatistics paymentStatistics;
    
    @Data
    @Accessors(chain = true)
    public static class ClassCompletionStatus {
        private int totalSchedules;
        private int pendingSchedules;
        private int completedSchedules;
        private double completionPercentage;
    }
    
    @Data
    @Accessors(chain = true)
    public static class AttendanceStatistics {
        private int totalStudents;
        private double overallPresencePercentage;
        private double overallAbsencePercentage;
        private int totalClassesHeld;
        private int totalPresentCount;
        private int totalAbsentCount;
    }
    
    @Data
    @Accessors(chain = true)
    public static class PaymentStatistics {
        private int totalStudents;
        private double paymentCompletedPercentage;
        private double paymentPendingPercentage;
        private int studentsWithCompletedPayment;
        private int studentsWithPendingPayment;
    }
}