package com.marglabs.trackmyclass.classroom.model;

import com.marglabs.trackmyclass.file.model.FileInfo;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class Classroom {
    private String id;
    private String className;
    private String subjectName;
    private String level;
    private String batchName;
    private ClassType classType;
    private String description;
    private int capacity;
    private String joinCode;
    private String teacherId;
    private String teacherName; // Transient field, not stored in entity
    private String posterFileId;
    private FileInfo posterFile; // Transient field, populated by service
    private long createdDate;
    private long updatedDate;
    private boolean active;
    private int currentEnrollment;
    private boolean classFull; // Transient field, calculated

    // Schedule statistics (transient fields)
    private int totalSchedules; // Total number of schedules for this classroom
    private int pendingSchedules; // Number of pending/scheduled sessions
}
