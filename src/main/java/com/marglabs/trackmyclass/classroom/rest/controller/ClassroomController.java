package com.marglabs.trackmyclass.classroom.rest.controller;

import com.marglabs.trackmyclass.classroom.model.Classroom;
import com.marglabs.trackmyclass.classroom.model.ClassroomCreationRequest;
import com.marglabs.trackmyclass.classroom.model.ClassroomSummary;
import com.marglabs.trackmyclass.classroom.model.StudentEnrolledClass;
import com.marglabs.trackmyclass.classroom.service.ClassroomService;
import com.marglabs.trackmyclass.user.model.RoleType;
import com.marglabs.trackmyclass.user.model.User;
import com.marglabs.trackmyclass.user.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/classroomManagement/v1")
@Tag(name = "Classroom Management", description = "API for managing classrooms")
public class ClassroomController {
    @Autowired
    private ClassroomService classroomService;

    @Autowired
    private UserService userService;

    @Operation(summary = "Create a new classroom", description = "Creates a new classroom for a teacher. Optionally include a posterFileId for a classroom poster image. Upload the poster image first using the file upload API, then reference the file ID.")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Classroom created successfully",
                content = @Content(schema = @Schema(implementation = Classroom.class))),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Forbidden - only teachers can create classrooms")
    })
    @PostMapping(value = "/classrooms", consumes = "application/json", produces = "application/json")
    @ResponseStatus(HttpStatus.CREATED)
    public Classroom createClassroom(
            @AuthenticationPrincipal Jwt userPrincipal,
            @Parameter(description = "Classroom details") @Valid @RequestBody ClassroomCreationRequest request) {

        // Get user from Cognito token
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());


        // Verify user is a teacher
        boolean isTeacher = user.getRoles().stream()
                .anyMatch(role -> role.getRole() == RoleType.TEACHER);

        if (!isTeacher) {
            throw new AccessDeniedException("Only teachers can create classrooms");
        }

        return classroomService.createClassroom(request, user.getId());
    }

    @Operation(summary = "Get classroom by ID", description = "Returns a classroom by its ID")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successful operation",
                content = @Content(schema = @Schema(implementation = Classroom.class))),
        @ApiResponse(responseCode = "404", description = "Classroom not found")
    })
    @GetMapping(value = "/classrooms/{id}", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public Classroom getClassroomById(
            @Parameter(description = "Classroom ID") @PathVariable String id) {
        return classroomService.getClassroomById(id);
    }

    @Operation(summary = "Get classroom by join code", description = "Returns a classroom by its join code")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successful operation",
                content = @Content(schema = @Schema(implementation = Classroom.class))),
        @ApiResponse(responseCode = "404", description = "Classroom not found")
    })
    @GetMapping(value = "/classrooms/code/{joinCode}", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public Classroom getClassroomByJoinCode(
            @Parameter(description = "Classroom join code") @PathVariable String joinCode) {
        return classroomService.getClassroomByJoinCode(joinCode);
    }

    @Operation(summary = "Get classrooms by teacher", description = "Returns all classrooms for the current teacher")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successful operation"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Forbidden - only teachers can view their classrooms")
    })
    @GetMapping(value = "/classrooms/teacher", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public Page<Classroom> getTeacherClassrooms(
            @AuthenticationPrincipal Jwt userPrincipal,
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size) {

        // Get user from Cognito token
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());

        // Verify user is a teacher
        boolean isTeacher = user.getRoles().stream()
                .anyMatch(role -> role.getRole() == RoleType.TEACHER);

        if (!isTeacher) {
            throw new AccessDeniedException("Only teachers can view their classrooms");
        }

        return classroomService.getClassroomsByTeacherIdPaginated(
                user.getId(),
                PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdDate")));
    }

    @Operation(summary = "Get teacher's classroom list", description = "Returns a simple list of classrooms (ID and name only) for the authenticated teacher")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successful operation"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Forbidden - only teachers can view their classrooms")
    })
    @GetMapping(value = "/classrooms/teacher/list", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<ClassroomSummary> getTeacherClassroomList(
            @AuthenticationPrincipal Jwt userPrincipal) {

        // Get user from Cognito token
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());

        // Verify user is a teacher
        boolean isTeacher = user.getRoles().stream()
                .anyMatch(role -> role.getRole() == RoleType.TEACHER);

        if (!isTeacher) {
            throw new AccessDeniedException("Only teachers can view their classrooms");
        }

        return classroomService.getClassroomSummariesByTeacherId(user.getId());
    }

    @Operation(summary = "Update classroom", description = "Updates an existing classroom")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Classroom updated successfully",
                content = @Content(schema = @Schema(implementation = Classroom.class))),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Forbidden - only the teacher who created the classroom can update it"),
        @ApiResponse(responseCode = "404", description = "Classroom not found")
    })
    @PutMapping(value = "/classrooms/{id}", consumes = "application/json", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public Classroom updateClassroom(
            @AuthenticationPrincipal Jwt userPrincipal,
            @Parameter(description = "Classroom ID") @PathVariable String id,
            @Parameter(description = "Updated classroom details") @Valid @RequestBody ClassroomCreationRequest request) {

        // Get user from Cognito token
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());

        // Verify user is a teacher
        boolean isTeacher = user.getRoles().stream()
                .anyMatch(role -> role.getRole() == RoleType.TEACHER);

        if (!isTeacher) {
            throw new AccessDeniedException("Only teachers can update classrooms");
        }

        return classroomService.updateClassroom(id, request, user.getId());
    }

    @Operation(summary = "Delete classroom", description = "Deletes a classroom")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "Classroom deleted successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Forbidden - only the teacher who created the classroom can delete it"),
        @ApiResponse(responseCode = "404", description = "Classroom not found")
    })
    @DeleteMapping(value = "/classrooms/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void deleteClassroom(
            @AuthenticationPrincipal Jwt userPrincipal,
            @Parameter(description = "Classroom ID") @PathVariable String id) {

        // Get user from Cognito token
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());

        // Verify user is a teacher
        boolean isTeacher = user.getRoles().stream()
                .anyMatch(role -> role.getRole() == RoleType.TEACHER);

        if (!isTeacher) {
            throw new AccessDeniedException("Only teachers can delete classrooms");
        }

        classroomService.deleteClassroom(id, user.getId());
    }

    @Operation(summary = "Search classrooms", description = "Searches for classrooms by name or subject")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successful operation")
    })
    @GetMapping(value = "/classrooms/search", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<Classroom> searchClassrooms(
            @Parameter(description = "Search term") @RequestParam String term) {
        return classroomService.searchClassrooms(term);
    }

    @Operation(summary = "Get student enrolled classes",
               description = "Get all classes that the authenticated student is enrolled in with attendance statistics")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Student enrolled classes retrieved successfully",
                content = @Content(schema = @Schema(implementation = StudentEnrolledClass.class))),
        @ApiResponse(responseCode = "404", description = "No enrollments found"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/student/enrolled-classes", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<StudentEnrolledClass> getStudentEnrolledClasses(
            @AuthenticationPrincipal Jwt userPrincipal) {

        // Get student user from Cognito token
        // Note: This assumes the student is logged in directly
        // In a real implementation, you'd have a student service to get student info from JWT
//        String studentUserId = userPrincipal.getSubject(); // Use Cognito ID as user ID
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());

        return classroomService.getStudentEnrolledClasses(user.getId());
    }

    @Operation(summary = "Get student enrolled classes with filters",
               description = "Get student enrolled classes with optional filters for status and subject")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Filtered student enrolled classes retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/student/enrolled-classes/filtered", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<StudentEnrolledClass> getStudentEnrolledClassesFiltered(
            @Parameter(description = "Enrollment status filter (ACTIVE, INACTIVE)") @RequestParam(required = false) String status,
            @Parameter(description = "Subject name filter") @RequestParam(required = false) String subject,
            @Parameter(description = "Attendance grade filter (EXCELLENT, GOOD, AVERAGE, POOR)") @RequestParam(required = false) String attendanceGrade,
            @AuthenticationPrincipal Jwt userPrincipal) {

        String studentUserId = userPrincipal.getSubject();
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());
        List<StudentEnrolledClass> enrolledClasses = classroomService.getStudentEnrolledClasses(user.getId());

        // Apply filters
        return enrolledClasses.stream()
                .filter(enrolledClass -> {
                    // Filter by enrollment status
                    if (status != null && !status.isEmpty()) {
                        if (!status.equalsIgnoreCase(enrolledClass.getEnrollmentStatus())) {
                            return false;
                        }
                    }

                    // Filter by subject
                    if (subject != null && !subject.isEmpty()) {
                        if (!enrolledClass.getSubjectName().toLowerCase().contains(subject.toLowerCase())) {
                            return false;
                        }
                    }

                    // Filter by attendance grade
                    if (attendanceGrade != null && !attendanceGrade.isEmpty()) {
                        if (enrolledClass.getStats() == null ||
                            !attendanceGrade.equalsIgnoreCase(enrolledClass.getStats().getAttendanceGrade())) {
                            return false;
                        }
                    }

                    return true;
                })
                .collect(Collectors.toList());
    }

    @Operation(summary = "Update classroom poster",
               description = "Update the poster image for a classroom. Upload the poster image first using the file upload API, then reference the file ID. Only image files are allowed as posters.")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Classroom poster updated successfully",
                content = @Content(schema = @Schema(implementation = Classroom.class))),
        @ApiResponse(responseCode = "400", description = "Invalid file ID or file is not an image"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Only the classroom teacher can update the poster"),
        @ApiResponse(responseCode = "404", description = "Classroom not found")
    })
    @PutMapping(value = "/classrooms/{id}/poster", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public Classroom updateClassroomPoster(
            @Parameter(description = "Classroom ID") @PathVariable String id,
            @Parameter(description = "Poster file ID") @RequestParam String posterFileId,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {

        User user = userService.getUserByCognitoId(userPrincipal.getSubject());

        // Verify user is a teacher
        boolean isTeacher = user.getRoles().stream()
                .anyMatch(role -> role.getRole() == RoleType.TEACHER);

        if (!isTeacher) {
            throw new AccessDeniedException("Only teachers can update classroom posters");
        }

        return classroomService.updateClassroomPoster(id, posterFileId, user.getId());
    }

    @Operation(summary = "Remove classroom poster",
               description = "Remove the poster image from a classroom")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Classroom poster removed successfully",
                content = @Content(schema = @Schema(implementation = Classroom.class))),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Only the classroom teacher can remove the poster"),
        @ApiResponse(responseCode = "404", description = "Classroom not found")
    })
    @DeleteMapping(value = "/classrooms/{id}/poster", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public Classroom removeClassroomPoster(
            @Parameter(description = "Classroom ID") @PathVariable String id,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {

        User user = userService.getUserByCognitoId(userPrincipal.getSubject());

        // Verify user is a teacher
        boolean isTeacher = user.getRoles().stream()
                .anyMatch(role -> role.getRole() == RoleType.TEACHER);

        if (!isTeacher) {
            throw new AccessDeniedException("Only teachers can remove classroom posters");
        }

        return classroomService.removeClassroomPoster(id, user.getId());
    }
}
