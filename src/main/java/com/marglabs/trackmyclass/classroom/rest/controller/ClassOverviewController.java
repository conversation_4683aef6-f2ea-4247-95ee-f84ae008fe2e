package com.marglabs.trackmyclass.classroom.rest.controller;

import com.marglabs.trackmyclass.classroom.model.ClassOverview;
import com.marglabs.trackmyclass.classroom.model.StudentClassOverview;
import com.marglabs.trackmyclass.classroom.model.TeacherOverallStats;
import com.marglabs.trackmyclass.classroom.model.StudentOverallStats;
import com.marglabs.trackmyclass.user.model.RoleType;
import com.marglabs.trackmyclass.classroom.service.ClassOverviewService;
import com.marglabs.trackmyclass.user.model.User;
import com.marglabs.trackmyclass.user.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/classroom/v1")
@Tag(name = "Class Overview", description = "APIs for getting comprehensive class statistics and overview")
public class ClassOverviewController {
    
    @Autowired
    private ClassOverviewService classOverviewService;
    
    @Autowired
    private UserService userService;
    
    @Operation(summary = "Get class overview", 
               description = "Get comprehensive overview of a class including completion status, attendance statistics, and payment statistics")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Class overview retrieved successfully",
                content = @Content(schema = @Schema(implementation = ClassOverview.class))),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Not authorized to view this class"),
        @ApiResponse(responseCode = "404", description = "Class not found")
    })
    @GetMapping(value = "/classes/{classId}/overview", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public ClassOverview getClassOverview(
            @Parameter(description = "Class ID") @PathVariable String classId,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());
        return classOverviewService.getClassOverview(classId, teacher.getId());
    }
    
    @Operation(summary = "Get student class overview", 
               description = "Get student's own overview of a class including personal attendance and payment statistics")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Student class overview retrieved successfully",
                content = @Content(schema = @Schema(implementation = StudentClassOverview.class))),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Not enrolled in this class"),
        @ApiResponse(responseCode = "404", description = "Class not found")
    })
    @GetMapping(value = "/classes/{classId}/student-overview", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public StudentClassOverview getStudentClassOverview(
            @Parameter(description = "Class ID") @PathVariable String classId,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        
        User student = userService.getUserByCognitoId(userPrincipal.getSubject());
        return classOverviewService.getStudentClassOverview(classId, student.getId());
    }
    
    @Operation(summary = "Get overall statistics", 
               description = "Get overall statistics for teacher or student based on their role")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Overall statistics retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/overall-stats", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public Object getOverallStats(@Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());
        
        boolean isTeacher = user.getRoles().stream()
                .anyMatch(role -> role.getRole() == RoleType.TEACHER);
        
        if (isTeacher) {
            return classOverviewService.getTeacherOverallStats(user.getId());
        } else {
            return classOverviewService.getStudentOverallStats(user.getId());
        }
    }
}