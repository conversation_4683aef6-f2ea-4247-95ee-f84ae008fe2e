package com.marglabs.trackmyclass.classroom.rest.controller;

import com.marglabs.trackmyclass.classroom.model.ClassNote;
import com.marglabs.trackmyclass.classroom.model.ClassNoteRequest;
import com.marglabs.trackmyclass.classroom.service.ClassNoteService;
import com.marglabs.trackmyclass.user.model.User;
import com.marglabs.trackmyclass.user.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/classroom/v1")
@Tag(name = "Class Notes", description = "APIs for managing class-specific notes")
public class ClassNoteController {
    
    @Autowired
    private ClassNoteService classNoteService;
    
    @Autowired
    private UserService userService;
    
    @Operation(summary = "Create class note", description = "Create a new note for a class (teachers only)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Note created successfully",
                content = @Content(schema = @Schema(implementation = ClassNote.class))),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Not authorized to create notes for this class")
    })
    @PostMapping(value = "/notes", consumes = "application/json", produces = "application/json")
    @ResponseStatus(HttpStatus.CREATED)
    public ClassNote createNote(
            @Parameter(description = "Note details") @Valid @RequestBody ClassNoteRequest request,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());
        return classNoteService.createNote(request, teacher.getId());
    }
    
    @Operation(summary = "Update class note", description = "Update an existing class note (teachers only)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Note updated successfully",
                content = @Content(schema = @Schema(implementation = ClassNote.class))),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Not authorized to update this note"),
        @ApiResponse(responseCode = "404", description = "Note not found")
    })
    @PutMapping(value = "/notes/{noteId}", consumes = "application/json", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public ClassNote updateNote(
            @Parameter(description = "Note ID") @PathVariable String noteId,
            @Parameter(description = "Updated note details") @Valid @RequestBody ClassNoteRequest request,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());
        return classNoteService.updateNote(noteId, request, teacher.getId());
    }
    
    @Operation(summary = "Get class notes", description = "Get all notes for a class (visible to teachers and enrolled students)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Notes retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Not authorized to view notes for this class"),
        @ApiResponse(responseCode = "404", description = "Class not found")
    })
    @GetMapping(value = "/classes/{classId}/notes", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<ClassNote> getClassNotes(
            @Parameter(description = "Class ID") @PathVariable String classId,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());
        return classNoteService.getClassNotes(classId, user.getId());
    }
    
    @Operation(summary = "Delete class note", description = "Delete a class note (teachers only)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "Note deleted successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Not authorized to delete this note"),
        @ApiResponse(responseCode = "404", description = "Note not found")
    })
    @DeleteMapping(value = "/notes/{noteId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void deleteNote(
            @Parameter(description = "Note ID") @PathVariable String noteId,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());
        classNoteService.deleteNote(noteId, teacher.getId());
    }
}