package com.marglabs.trackmyclass.classroom.entity;

import jakarta.persistence.*;
import lombok.Data;

@Entity
@Table(name = "classrooms")
@Data
public class ClassroomEntity {
    @Id
    private String id;
    
    @Column(name = "class_name", nullable = false)
    private String className;
    
    @Column(name = "subject_name", nullable = false)
    private String subjectName;
    
    @Column(nullable = false)
    private String level;
    
    @Column(name = "batch_name", nullable = false)
    private String batchName;
    
    @Column(name = "class_type", nullable = false)
    private String classType;
    
    @Column(name = "description", length = 2000)
    private String description;
    
    @Column(nullable = false)
    private int capacity;
    
    @Column(name = "join_code", nullable = false, unique = true)
    private String joinCode;
    
    @Column(name = "teacher_id", nullable = false)
    private String teacherId;

    @Column(name = "poster_file_id")
    private String posterFileId;

    @Column(name = "created_date")
    private long createdDate;
    
    @Column(name = "updated_date")
    private long updatedDate;
    
    @Column
    private boolean active;
    
    @Column(name = "current_enrollment")
    private int currentEnrollment;
}
