package com.marglabs.trackmyclass.classroom.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.Instant;

@Entity
@Table(name = "class_notes")
@Data
@Accessors(chain = true)
public class ClassNoteEntity {
    
    @Id
    @Column(name = "id")
    private String id;
    
    @Column(name = "class_id", nullable = false)
    private String classId;
    
    @Column(name = "title", nullable = false, length = 200)
    private String title;
    
    @Column(name = "content", nullable = false, length = 5000)
    private String content;
    
    @Column(name = "teacher_id", nullable = false)
    private String teacherId;
    
    @CreationTimestamp
    @Column(name = "created_date", nullable = false, updatable = false)
    private Instant createdDate;
    
    @UpdateTimestamp
    @Column(name = "updated_date", nullable = false)
    private Instant updatedDate;
}