package com.marglabs.trackmyclass.classroom.service;

import com.marglabs.trackmyclass.classroom.dao.ClassroomDao;
import com.marglabs.trackmyclass.classroom.model.ClassOverview;
import com.marglabs.trackmyclass.classroom.model.StudentClassOverview;
import com.marglabs.trackmyclass.classroom.model.TeacherOverallStats;
import com.marglabs.trackmyclass.classroom.model.StudentOverallStats;
import com.marglabs.trackmyclass.expense.dao.ExpenseDao;
import java.math.BigDecimal;
import com.marglabs.trackmyclass.schedule.dao.ClassScheduleDao;
import com.marglabs.trackmyclass.attendance.dao.AttendanceDao;
import com.marglabs.trackmyclass.payment.dao.PaymentDao;
import com.marglabs.trackmyclass.enrollment.dao.EnrollmentDao;
import com.marglabs.trackmyclass.attendance.model.AttendanceStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ClassOverviewService {
    
    private static final Logger logger = LoggerFactory.getLogger(ClassOverviewService.class);
    
    @Autowired
    private ClassroomDao classroomDao;
    
    @Autowired
    private ClassScheduleDao classScheduleDao;
    
    @Autowired
    private AttendanceDao attendanceDao;
    
    @Autowired
    private PaymentDao paymentDao;
    
    @Autowired
    private EnrollmentDao enrollmentDao;
    
    @Autowired
    private ExpenseDao expenseDao;
    
    public ClassOverview getClassOverview(String classId, String teacherId) {
        logger.info("Getting class overview for classId: {} by teacher: {}", classId, teacherId);
        
        // Verify classroom exists and teacher has access
        var classroom = classroomDao.getById(classId);
        if (!classroom.getTeacherId().equals(teacherId)) {
            throw new RuntimeException("Teacher not authorized to view this class overview");
        }
        
        ClassOverview overview = new ClassOverview()
                .setClassId(classId)
                .setClassName(classroom.getClassName());
        
        // Get completion status
        overview.setCompletionStatus(getCompletionStatus(classId));
        
        // Get attendance statistics
        overview.setAttendanceStatistics(getAttendanceStatistics(classId));
        
        // Get payment statistics
        overview.setPaymentStatistics(getPaymentStatistics(classId));
        
        return overview;
    }
    
    public StudentClassOverview getStudentClassOverview(String classId, String studentId) {
        logger.info("Getting student class overview for classId: {} by student: {}", classId, studentId);
        
        // Verify classroom exists
        var classroom = classroomDao.getById(classId);
        
        // Verify student is enrolled in this class
        if (!enrollmentDao.existsByClassroomIdAndUserIdAndRole(classId, studentId, "STUDENT")) {
            throw new RuntimeException("Student not enrolled in this class");
        }
        
        StudentClassOverview overview = new StudentClassOverview()
                .setClassId(classId)
                .setClassName(classroom.getClassName());
        
        // Get student's attendance statistics
        overview.setAttendanceStats(getStudentAttendanceStats(classId, studentId));
        
        // Get student's payment statistics
        overview.setPaymentStats(getStudentPaymentStats(classId, studentId));
        
        return overview;
    }
    
    private ClassOverview.ClassCompletionStatus getCompletionStatus(String classId) {
        try {
            int totalSchedules = (int) classScheduleDao.countByClassroomId(classId);
            int pendingSchedules = (int) classScheduleDao.countPendingSchedulesByClassroom(classId);
            int completedSchedules = totalSchedules - pendingSchedules;
            double completionPercentage = totalSchedules > 0 ? (double) completedSchedules / totalSchedules * 100 : 0.0;
            
            return new ClassOverview.ClassCompletionStatus()
                    .setTotalSchedules(totalSchedules)
                    .setPendingSchedules(pendingSchedules)
                    .setCompletedSchedules(completedSchedules)
                    .setCompletionPercentage(completionPercentage);
        } catch (Exception e) {
            logger.warn("Failed to get completion status for class {}: {}", classId, e.getMessage());
            return new ClassOverview.ClassCompletionStatus()
                    .setTotalSchedules(0)
                    .setPendingSchedules(0)
                    .setCompletedSchedules(0)
                    .setCompletionPercentage(0.0);
        }
    }
    
    private ClassOverview.AttendanceStatistics getAttendanceStatistics(String classId) {
        try {
            int totalStudents = (int) enrollmentDao.countByClassroomIdLong(classId);
            int totalClassesHeld = (int) attendanceDao.countDistinctClassesByClassroom(classId);
            int totalPresentCount = (int) attendanceDao.countPresentByClassroom(classId);
            int totalAbsentCount = (int) attendanceDao.countAbsentByClassroom(classId);
            
            int totalAttendanceRecords = totalPresentCount + totalAbsentCount;
            double presencePercentage = totalAttendanceRecords > 0 ? (double) totalPresentCount / totalAttendanceRecords * 100 : 0.0;
            double absencePercentage = 100.0 - presencePercentage;
            
            return new ClassOverview.AttendanceStatistics()
                    .setTotalStudents(totalStudents)
                    .setOverallPresencePercentage(presencePercentage)
                    .setOverallAbsencePercentage(absencePercentage)
                    .setTotalClassesHeld(totalClassesHeld)
                    .setTotalPresentCount(totalPresentCount)
                    .setTotalAbsentCount(totalAbsentCount);
        } catch (Exception e) {
            logger.warn("Failed to get attendance statistics for class {}: {}", classId, e.getMessage());
            return new ClassOverview.AttendanceStatistics()
                    .setTotalStudents(0)
                    .setOverallPresencePercentage(0.0)
                    .setOverallAbsencePercentage(0.0)
                    .setTotalClassesHeld(0)
                    .setTotalPresentCount(0)
                    .setTotalAbsentCount(0);
        }
    }
    
    private ClassOverview.PaymentStatistics getPaymentStatistics(String classId) {
        try {
            int totalStudents = (int) enrollmentDao.countByClassroomIdLong(classId);
            int studentsWithCompletedPayment = (int) paymentDao.countDistinctStudentsWithCompletedPayments(classId);
            int studentsWithPendingPayment = totalStudents - studentsWithCompletedPayment;
            
            double completedPercentage = totalStudents > 0 ? (double) studentsWithCompletedPayment / totalStudents * 100 : 0.0;
            double pendingPercentage = 100.0 - completedPercentage;
            
            return new ClassOverview.PaymentStatistics()
                    .setTotalStudents(totalStudents)
                    .setPaymentCompletedPercentage(completedPercentage)
                    .setPaymentPendingPercentage(pendingPercentage)
                    .setStudentsWithCompletedPayment(studentsWithCompletedPayment)
                    .setStudentsWithPendingPayment(studentsWithPendingPayment);
        } catch (Exception e) {
            logger.warn("Failed to get payment statistics for class {}: {}", classId, e.getMessage());
            return new ClassOverview.PaymentStatistics()
                    .setTotalStudents(0)
                    .setPaymentCompletedPercentage(0.0)
                    .setPaymentPendingPercentage(0.0)
                    .setStudentsWithCompletedPayment(0)
                    .setStudentsWithPendingPayment(0);
        }
    }
    
    private StudentClassOverview.StudentAttendanceStats getStudentAttendanceStats(String classId, String studentId) {
        try {
            var attendanceRecords = attendanceDao.getByStudentAndClassroom(studentId, classId);
            int totalClassesHeld = attendanceRecords.size();
            int totalPresent = 0, totalAbsent = 0, totalLate = 0;
            
            for (var record : attendanceRecords) {
                switch (record.getStatus()) {
                    case PRESENT -> totalPresent++;
                    case ABSENT, EXCUSED -> totalAbsent++;
                    case LATE -> totalLate++;
                    case PARTIAL -> totalPresent++;
                }
            }
            
            int classesAttended = totalPresent + totalLate;
            double attendancePercentage = totalClassesHeld > 0 ? (double) classesAttended / totalClassesHeld * 100 : 0.0;
            
            return new StudentClassOverview.StudentAttendanceStats()
                    .setTotalClassesHeld(totalClassesHeld)
                    .setClassesAttended(classesAttended)
                    .setTotalPresent(totalPresent)
                    .setTotalAbsent(totalAbsent)
                    .setTotalLate(totalLate)
                    .setAttendancePercentage(attendancePercentage);
        } catch (Exception e) {
            logger.warn("Failed to get student attendance stats for student {} in class {}: {}", studentId, classId, e.getMessage());
            return new StudentClassOverview.StudentAttendanceStats()
                    .setTotalClassesHeld(0)
                    .setClassesAttended(0)
                    .setTotalPresent(0)
                    .setTotalAbsent(0)
                    .setTotalLate(0)
                    .setAttendancePercentage(0.0);
        }
    }
    
    private StudentClassOverview.StudentPaymentStats getStudentPaymentStats(String classId, String studentId) {
        try {
            var payments = paymentDao.getByClassroomAndStudent(classId, studentId);
            int totalPaymentsDue = payments.size();
            int paymentsCompleted = (int) payments.stream().filter(p -> p.getStatus().name().equals("COMPLETED")).count();
            int paymentsPending = totalPaymentsDue - paymentsCompleted;
            double completionPercentage = totalPaymentsDue > 0 ? (double) paymentsCompleted / totalPaymentsDue * 100 : 0.0;
            
            return new StudentClassOverview.StudentPaymentStats()
                    .setTotalPaymentsDue(totalPaymentsDue)
                    .setPaymentsCompleted(paymentsCompleted)
                    .setPaymentsPending(paymentsPending)
                    .setPaymentCompletionPercentage(completionPercentage);
        } catch (Exception e) {
            logger.warn("Failed to get student payment stats for student {} in class {}: {}", studentId, classId, e.getMessage());
            return new StudentClassOverview.StudentPaymentStats()
                    .setTotalPaymentsDue(0)
                    .setPaymentsCompleted(0)
                    .setPaymentsPending(0)
                    .setPaymentCompletionPercentage(0.0);
        }
    }
    
    public TeacherOverallStats getTeacherOverallStats(String teacherId) {
        logger.info("Getting overall statistics for teacher: {}", teacherId);
        
        try {
            int totalClasses = (int) classroomDao.countByTeacherId(teacherId);
            int totalEnrollments = (int) enrollmentDao.countByTeacherId(teacherId);
            BigDecimal totalExpenses = expenseDao.getTotalExpensesByTeacher(teacherId);
            BigDecimal totalIncome = paymentDao.getTotalIncomeByTeacher(teacherId);
            
            return new TeacherOverallStats()
                    .setTotalClassesCreated(totalClasses)
                    .setTotalEnrollments(totalEnrollments)
                    .setTotalExpenses(totalExpenses != null ? totalExpenses : BigDecimal.ZERO)
                    .setTotalIncome(totalIncome != null ? totalIncome : BigDecimal.ZERO);
        } catch (Exception e) {
            logger.warn("Failed to get teacher overall stats for teacher {}: {}", teacherId, e.getMessage());
            return new TeacherOverallStats()
                    .setTotalClassesCreated(0)
                    .setTotalEnrollments(0)
                    .setTotalExpenses(BigDecimal.ZERO)
                    .setTotalIncome(BigDecimal.ZERO);
        }
    }
    
    public StudentOverallStats getStudentOverallStats(String studentId) {
        logger.info("Getting overall statistics for student: {}", studentId);
        
        try {
            int totalClasses = (int) enrollmentDao.countByStudentId(studentId);
            BigDecimal totalPayments = paymentDao.getTotalPaymentsByStudent(studentId);
            
            return new StudentOverallStats()
                    .setTotalClassesEnrolled(totalClasses)
                    .setTotalPaymentsMade(totalPayments != null ? totalPayments : BigDecimal.ZERO);
        } catch (Exception e) {
            logger.warn("Failed to get student overall stats for student {}: {}", studentId, e.getMessage());
            return new StudentOverallStats()
                    .setTotalClassesEnrolled(0)
                    .setTotalPaymentsMade(BigDecimal.ZERO);
        }
    }
}