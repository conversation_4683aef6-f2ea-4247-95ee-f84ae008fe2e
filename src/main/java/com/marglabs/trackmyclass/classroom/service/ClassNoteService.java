package com.marglabs.trackmyclass.classroom.service;

import com.marglabs.common.rest.error.GeneralException;
import com.marglabs.common.utils.IdGenerator;
import com.marglabs.trackmyclass.classroom.dao.ClassNoteDao;
import com.marglabs.trackmyclass.classroom.dao.ClassroomDao;
import com.marglabs.trackmyclass.classroom.entity.ClassNoteEntity;
import com.marglabs.trackmyclass.classroom.mapper.ClassNoteMapper;
import com.marglabs.trackmyclass.classroom.model.ClassNote;
import com.marglabs.trackmyclass.classroom.model.ClassNoteRequest;
import com.marglabs.trackmyclass.enrollment.dao.EnrollmentDao;
import com.marglabs.trackmyclass.user.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class ClassNoteService {
    
    private static final Logger logger = LoggerFactory.getLogger(ClassNoteService.class);
    
    @Autowired
    private ClassNoteDao classNoteDao;
    
    @Autowired
    private ClassroomDao classroomDao;
    
    @Autowired
    private EnrollmentDao enrollmentDao;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private ClassNoteMapper mapper;
    
    @Autowired
    private IdGenerator idGenerator;
    
    @Transactional
    public ClassNote createNote(ClassNoteRequest request, String teacherId) {
        logger.info("Creating class note for class {} by teacher {}", request.getClassId(), teacherId);
        
        validateTeacherAccess(request.getClassId(), teacherId);
        
        ClassNoteEntity entity = mapper.toEntity(request);
        entity.setId(idGenerator.generateId());
        entity.setTeacherId(teacherId);
        
        ClassNoteEntity savedEntity = classNoteDao.save(entity);
        return enrichNote(mapper.toDto(savedEntity));
    }
    
    @Transactional
    public ClassNote updateNote(String noteId, ClassNoteRequest request, String teacherId) {
        logger.info("Updating class note {} by teacher {}", noteId, teacherId);
        
        ClassNoteEntity entity = classNoteDao.getById(noteId);
        
        if (!entity.getTeacherId().equals(teacherId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "CLASS_NOTE", "UNAUTHORIZED_ACCESS",
                    "teacher_not_authorized_for_note", teacherId, noteId);
        }
        
        mapper.updateEntityFromRequest(request, entity);
        ClassNoteEntity updatedEntity = classNoteDao.save(entity);
        return enrichNote(mapper.toDto(updatedEntity));
    }
    
    public List<ClassNote> getClassNotes(String classId, String userId) {
        logger.info("Getting class notes for class {} by user {}", classId, userId);
        
        validateUserAccess(classId, userId);
        
        List<ClassNoteEntity> entities = classNoteDao.getByClassId(classId);
        return entities.stream()
                .map(entity -> enrichNote(mapper.toDto(entity)))
                .collect(Collectors.toList());
    }
    
    @Transactional
    public void deleteNote(String noteId, String teacherId) {
        logger.info("Deleting class note {} by teacher {}", noteId, teacherId);
        
        ClassNoteEntity entity = classNoteDao.getById(noteId);
        
        if (!entity.getTeacherId().equals(teacherId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "CLASS_NOTE", "UNAUTHORIZED_ACCESS",
                    "teacher_not_authorized_for_note", teacherId, noteId);
        }
        
        classNoteDao.deleteById(noteId);
    }
    
    private void validateTeacherAccess(String classId, String teacherId) {
        var classroom = classroomDao.getById(classId);
        if (!classroom.getTeacherId().equals(teacherId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "CLASS_NOTE", "UNAUTHORIZED_ACCESS",
                    "teacher_not_authorized_for_class", teacherId, classId);
        }
    }
    
    private void validateUserAccess(String classId, String userId) {
        var classroom = classroomDao.getById(classId);
        
        // Check if user is teacher or enrolled student
        boolean isTeacher = classroom.getTeacherId().equals(userId);
        boolean isEnrolled = enrollmentDao.existsByClassroomIdAndUserIdAndRole(classId, userId, "STUDENT");
        
        if (!isTeacher && !isEnrolled) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "CLASS_NOTE", "UNAUTHORIZED_ACCESS",
                    "user_not_authorized_for_class", userId, classId);
        }
    }
    
    private ClassNote enrichNote(ClassNote note) {
        try {
            var classroom = classroomDao.getById(note.getClassId());
            note.setClassName(classroom.getClassName());
            
            var teacher = userService.getUserById(note.getTeacherId());
            note.setTeacherName(teacher.getName());
        } catch (Exception e) {
            logger.warn("Failed to enrich note details for note {}: {}", note.getId(), e.getMessage());
        }
        return note;
    }
}