package com.marglabs.trackmyclass.classroom.service;

import com.marglabs.common.rest.error.EntityNotFoundException;
import com.marglabs.common.rest.error.GeneralException;
import com.marglabs.common.utils.IdGenerator;
import com.marglabs.trackmyclass.classroom.dao.ClassroomDao;
import com.marglabs.trackmyclass.classroom.entity.ClassroomEntity;
import com.marglabs.trackmyclass.classroom.mapper.ClassroomMapper;
import com.marglabs.trackmyclass.classroom.model.Classroom;
import com.marglabs.trackmyclass.classroom.model.ClassroomCreationRequest;
import com.marglabs.trackmyclass.classroom.model.ClassroomSummary;
import com.marglabs.trackmyclass.classroom.model.StudentEnrolledClass;
import com.marglabs.trackmyclass.file.model.FileInfo;
import com.marglabs.trackmyclass.file.service.FileService;
import com.marglabs.trackmyclass.schedule.dao.ClassScheduleDao;
import com.marglabs.trackmyclass.user.model.User;
import com.marglabs.trackmyclass.user.service.UserService;
import com.marglabs.trackmyclass.enrollment.dao.EnrollmentDao;
import com.marglabs.trackmyclass.email.service.EmailService;
import com.marglabs.trackmyclass.email.model.EmailTemplate;
import com.marglabs.trackmyclass.attendance.dao.AttendanceDao;
import org.apache.coyote.BadRequestException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.ArrayList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.security.SecureRandom;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class ClassroomService {
    private static final String JOIN_CODE_CHARS = "ABCDEFGHJKLMNPQRSTUVWXYZ23456789";
    private static final int JOIN_CODE_LENGTH = 6;
    private static final Logger logger = LoggerFactory.getLogger(ClassroomService.class);

    @Autowired
    private ClassroomDao classroomDao;

    @Autowired
    private ClassroomMapper mapper;

    @Autowired
    private IdGenerator idGenerator;

    @Autowired
    private UserService userService;

    @Autowired
    private FileService fileService;

    @Autowired
    private ClassScheduleDao classScheduleDao;

    @Autowired
    private EnrollmentDao enrollmentDao;

    @Autowired
    private AttendanceDao attendanceDao;
    
    @Autowired
    private EmailService emailService;

    /**
     * Create a new classroom
     */
    @Transactional
    public Classroom createClassroom(ClassroomCreationRequest request, String teacherId) {
        // Generate a unique join code
        String joinCode = generateUniqueJoinCode();

        // Validate poster file if provided
        String validatedPosterFileId = validatePosterFile(request.getPosterFileId(), teacherId);

        // Create classroom entity
        Classroom classroom = new Classroom()
                .setId(idGenerator.generateId())
                .setClassName(request.getClassName())
                .setSubjectName(request.getSubjectName())
                .setLevel(request.getLevel())
                .setBatchName(request.getBatchName())
                .setClassType(request.getClassType())
                .setDescription(request.getDescription())
                .setCapacity(request.getCapacity())
                .setJoinCode(joinCode)
                .setTeacherId(teacherId)
                .setPosterFileId(validatedPosterFileId)
                .setActive(true)
                .setCurrentEnrollment(0);

        ClassroomEntity entity = mapper.toEntity(classroom);
        entity = classroomDao.create(entity);

        // Get teacher name for the response
        User teacher = userService.getUserById(teacherId);

        Classroom result = mapper.toDto(entity);
        result.setTeacherName(teacher.getName());

        // Enrich with poster file information
        result = enrichClassroomWithPosterFile(result);
        
        // Send class creation email
        try {
            Map<String, Object> templateData = Map.of(
                "teacherName", teacher.getName(),
                "className", result.getClassName(),
                "subjectName", result.getSubjectName(),
                "level", result.getLevel(),
                "batchName", result.getBatchName(),
                "joinCode", result.getJoinCode()
            );
            emailService.sendEmail(teacher.getEmail(), EmailTemplate.CLASS_CREATION, templateData);
        } catch (Exception e) {
            // Log error but don't fail the operation
        }

        return result;
    }

    /**
     * Get classroom by ID
     */
    public Classroom getClassroomById(String id) {
        ClassroomEntity entity = classroomDao.getById(id);
        Classroom classroom = mapper.toDto(entity);

        // Get teacher name
        try {
            User teacher = userService.getUserById(entity.getTeacherId());
            classroom.setTeacherName(teacher.getName());
        } catch (Exception e) {
            // If teacher not found, just leave the name blank
            classroom.setTeacherName("Unknown Teacher");
        }

        // Enrich with poster file information
        classroom = enrichClassroomWithPosterFile(classroom);

        return classroom;
    }

    /**
     * Get classroom by join code
     */
    public Classroom getClassroomByJoinCode(String joinCode) {
        ClassroomEntity entity = classroomDao.findByJoinCode(joinCode)
                .orElseThrow(() -> new EntityNotFoundException(HttpStatus.NOT_FOUND, "CLASSROOM", "CLASSROOM_NOT_FOUND",
                        "classroom_not_found_by_join_code", joinCode));

        Classroom classroom = mapper.toDto(entity);

        // Get teacher name
        try {
            User teacher = userService.getUserById(entity.getTeacherId());
            classroom.setTeacherName(teacher.getName());
        } catch (Exception e) {
            classroom.setTeacherName("Unknown Teacher");
        }

        return classroom;
    }



    /**
     * Get classrooms by teacher ID with pagination
     */
    public Page<Classroom> getClassroomsByTeacherIdPaginated(String teacherId, Pageable pageable) {
        Page<ClassroomEntity> entities = classroomDao.getByTeacherIdPaginated(teacherId, pageable);

        // Get teacher name once for all classrooms
        String teacherName = "Unknown Teacher";
        try {
            User teacher = userService.getUserById(teacherId);
            teacherName = teacher.getName();
        } catch (Exception e) {
            // If teacher not found, just leave the default name
        }

        final String finalTeacherName = teacherName;

        return entities.map(entity -> {
            Classroom classroom = mapper.toDto(entity);
            classroom.setTeacherName(finalTeacherName);

            // Get schedule statistics for this classroom
            try {
                long totalSchedules = classScheduleDao.countByClassroomId(entity.getId());
                long pendingSchedules = classScheduleDao.countPendingSchedulesByClassroom(entity.getId());
                classroom.setTotalSchedules((int) totalSchedules);
                classroom.setPendingSchedules((int) pendingSchedules);
            } catch (Exception e) {
                // If schedule queries fail, set default values
                classroom.setTotalSchedules(0);
                classroom.setPendingSchedules(0);
            }

            return classroom;
        });
    }

    /**
     * Get simple list of classrooms for a teacher (ID and name only)
     */
    public List<ClassroomSummary> getClassroomSummariesByTeacherId(String teacherId) {
        List<ClassroomEntity> classroomEntities = classroomDao.findByTeacherId(teacherId);
        return classroomEntities.stream()
                .map(entity -> new ClassroomSummary()
                        .setId(entity.getId())
                        .setClassName(entity.getClassName()))
                .collect(Collectors.toList());
    }

    /**
     * Update classroom
     */
    @Transactional
    public Classroom updateClassroom(String id, ClassroomCreationRequest request, String teacherId) {
        ClassroomEntity entity = classroomDao.getById(id);

        // Verify that the teacher is the owner of the classroom
        if (!entity.getTeacherId().equals(teacherId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "CLASSROOM", "CLASSROOM_UPDATE_FORBIDDEN",
                    "classroom_update_forbidden", id);
        }

        // Update fields
        entity.setClassName(request.getClassName());
        entity.setSubjectName(request.getSubjectName());
        entity.setLevel(request.getLevel());
        entity.setBatchName(request.getBatchName());
        entity.setClassType(request.getClassType().name());
        entity.setDescription(request.getDescription());
        entity.setCapacity(request.getCapacity());
        entity.setPosterFileId(request.getPosterFileId());

        entity = classroomDao.update(entity);

        Classroom classroom = mapper.toDto(entity);

        // Get teacher name
        try {
            User teacher = userService.getUserById(teacherId);
            classroom.setTeacherName(teacher.getName());
        } catch (Exception e) {
            classroom.setTeacherName("Unknown Teacher");
        }

        return classroom;
    }

    /**
     * Delete classroom
     */
    @Transactional
    public void deleteClassroom(String id, String teacherId) {
        ClassroomEntity entity = classroomDao.getById(id);

        // Verify that the teacher is the owner of the classroom
        if (!entity.getTeacherId().equals(teacherId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "CLASSROOM", "CLASSROOM_DELETE_FORBIDDEN",
                    "classroom_delete_forbidden", id);
        }

        classroomDao.deleteById(id);
    }

    /**
     * Search classrooms
     */
    public List<Classroom> searchClassrooms(String searchTerm) {
        List<ClassroomEntity> entities = classroomDao.searchClassrooms(searchTerm);

        return entities.stream()
                .map(entity -> {
                    Classroom classroom = mapper.toDto(entity);

                    // Get teacher name
                    try {
                        User teacher = userService.getUserById(entity.getTeacherId());
                        classroom.setTeacherName(teacher.getName());
                    } catch (Exception e) {
                        classroom.setTeacherName("Unknown Teacher");
                    }

                    // Enrich with poster file information
                    classroom = enrichClassroomWithPosterFile(classroom);

                    return classroom;
                })
                .collect(Collectors.toList());
    }

    /**
     * Generate a unique join code
     */
    private String generateUniqueJoinCode() {
        SecureRandom random = new SecureRandom();
        int maxAttempts = 10;

        for (int attempt = 0; attempt < maxAttempts; attempt++) {
            StringBuilder codeBuilder = new StringBuilder(JOIN_CODE_LENGTH);
            for (int i = 0; i < JOIN_CODE_LENGTH; i++) {
                int randomIndex = random.nextInt(JOIN_CODE_CHARS.length());
                codeBuilder.append(JOIN_CODE_CHARS.charAt(randomIndex));
            }

            String joinCode = codeBuilder.toString();

            // Check if code already exists
            if (!classroomDao.existsByJoinCode(joinCode)) {
                return joinCode;
            }
        }

        // If we couldn't generate a unique code after several attempts, throw an exception
        throw new RuntimeException("Failed to generate a unique join code after " + maxAttempts + " attempts");
    }

    /**
     * Increment enrollment count
     */
    @Transactional
    public Classroom incrementEnrollment(String classroomId) {
        ClassroomEntity entity = classroomDao.incrementEnrollment(classroomId);
        Classroom classroom = mapper.toDto(entity);

        // Get teacher name
        try {
            User teacher = userService.getUserById(entity.getTeacherId());
            classroom.setTeacherName(teacher.getName());
        } catch (Exception e) {
            classroom.setTeacherName("Unknown Teacher");
        }

        return classroom;
    }

    /**
     * Decrement enrollment count
     */
    @Transactional
    public Classroom decrementEnrollment(String classroomId) {
        ClassroomEntity entity = classroomDao.decrementEnrollment(classroomId);
        Classroom classroom = mapper.toDto(entity);

        // Get teacher name
        try {
            User teacher = userService.getUserById(entity.getTeacherId());
            classroom.setTeacherName(teacher.getName());
        } catch (Exception e) {
            classroom.setTeacherName("Unknown Teacher");
        }

        return classroom;
    }

    /**
     * Get all classes a student is enrolled in
     */
    public List<StudentEnrolledClass> getStudentEnrolledClasses(String studentUserId) {
        logger.info("Getting enrolled classes for student user ID: {}", studentUserId);

        // Get all enrollments for this student user
        List<com.marglabs.trackmyclass.enrollment.entity.EnrollmentEntity> enrollments =
            enrollmentDao.getByUserIdAndRole(studentUserId, "STUDENT");

        if (enrollments.isEmpty()) {
            logger.info("No enrollments found for student user ID: {}", studentUserId);
            return new ArrayList<>();
        }

        List<StudentEnrolledClass> enrolledClasses = new ArrayList<>();

        for (com.marglabs.trackmyclass.enrollment.entity.EnrollmentEntity enrollment : enrollments) {
            try {
                // Get classroom details
                Classroom classroom = getClassroomById(enrollment.getClassroomId());

                // Create student enrolled class
                StudentEnrolledClass enrolledClass = new StudentEnrolledClass()
                        .setClassroomId(classroom.getId())
                        .setClassName(classroom.getClassName())
                        .setSubjectName(classroom.getSubjectName())
                        .setLevel(classroom.getLevel())
                        .setBatchName(classroom.getBatchName())
                        .setClassType(classroom.getClassType())
                        .setDescription(classroom.getDescription())
                        .setJoinCode(classroom.getJoinCode())
                        .setTeacherId(classroom.getTeacherId())
                        .setTeacherName(classroom.getTeacherName())
                        .setPosterFileId(classroom.getPosterFileId())
                        .setPosterFile(classroom.getPosterFile())
                        .setEnrollmentId(enrollment.getId())
                        .setEnrollmentDate(enrollment.getCreatedDate())
                        .setEnrollmentStatus(enrollment.getStatus())
                        .setTotalStudents(classroom.getCurrentEnrollment())
                        .setClassFull(classroom.isClassFull())
                        .setCapacity(classroom.getCapacity());

                // Get schedule counts for this classroom
                try {
                    long totalSchedules = classScheduleDao.countByClassroomId(classroom.getId());
                    long pendingSchedules = classScheduleDao.countPendingSchedulesByClassroom(classroom.getId());


                    enrolledClass.setTotalSchedules((int) totalSchedules);
                    enrolledClass.setPendingSchedules((int) pendingSchedules);
                } catch (Exception e) {
                    logger.warn("Failed to get schedule counts for classroom {}: {}", classroom.getId(), e.getMessage());
                    enrolledClass.setTotalSchedules(0);
                    enrolledClass.setPendingSchedules(0);
                }

                // Get basic attendance stats for this student in this class (optional)
                try {
                    StudentEnrolledClass.StudentClassStats stats = getStudentClassStats(studentUserId, classroom.getId());
                    enrolledClass.setStats(stats);
                } catch (Exception e) {
                    logger.warn("Failed to get attendance stats for student {} in classroom {}: {}",
                            studentUserId, classroom.getId(), e.getMessage());
                    // Set default stats
                    enrolledClass.setStats(new StudentEnrolledClass.StudentClassStats()
                            .setTotalClassesHeld(0)
                            .setClassesAttended(0)
                            .setTotalPresent(0)
                            .setTotalAbsent(0)
                            .setTotalLate(0)
                            .setAttendancePercentage(0.0)
                            .setAttendanceGrade("N/A")
                            .setMeetingRequirement(true));
                }

                // Get last attendance date (optional)
                try {
                    String lastAttendanceDate = getLastAttendanceDate(studentUserId, classroom.getId());
                    enrolledClass.setLastAttendanceDate(lastAttendanceDate);
                } catch (Exception e) {
                    enrolledClass.setLastAttendanceDate("N/A");
                }

                // Set next class info (simplified - in real implementation, get from schedule)
                enrolledClass.setNextClassDate("TBD")
                           .setNextClassTime("TBD");

                enrolledClasses.add(enrolledClass);

            } catch (Exception e) {
                logger.warn("Failed to get classroom details for enrollment {}: {}",
                        enrollment.getId(), e.getMessage());
                // Skip this enrollment if classroom details can't be retrieved
            }
        }

        // Sort by enrollment date (most recent first)
        enrolledClasses.sort((a, b) -> Long.compare(b.getEnrollmentDate(), a.getEnrollmentDate()));

        logger.info("Found {} enrolled classes for student user ID: {}", enrolledClasses.size(), studentUserId);
        return enrolledClasses;
    }

    private StudentEnrolledClass.StudentClassStats getStudentClassStats(String studentUserId, String classroomId) {
        // This is a simplified implementation
        // In a real implementation, you would call the AttendanceService to get detailed stats

        try {
            // Get attendance records for this student in this classroom
            List<com.marglabs.trackmyclass.attendance.entity.AttendanceEntity> attendanceRecords =
                attendanceDao.getByStudentAndClassroom(studentUserId, classroomId);

            int totalClassesHeld = attendanceRecords.size();
            int totalPresent = 0, totalAbsent = 0, totalLate = 0;

            for (com.marglabs.trackmyclass.attendance.entity.AttendanceEntity record : attendanceRecords) {
                switch (record.getStatus()) {
                    case PRESENT -> totalPresent++;
                    case ABSENT -> totalAbsent++;
                    case LATE -> totalLate++;
                    case EXCUSED -> totalAbsent++; // Count excused as absent for stats
                    case PARTIAL -> totalPresent++; // Count partial as present
                }
            }

            int classesAttended = totalPresent + totalLate;
            double attendancePercentage = totalClassesHeld > 0 ? (double) classesAttended / totalClassesHeld * 100 : 0.0;

            String attendanceGrade = calculateAttendanceGrade(attendancePercentage);
            boolean meetingRequirement = attendancePercentage >= 75.0;

            return new StudentEnrolledClass.StudentClassStats()
                    .setTotalClassesHeld(totalClassesHeld)
                    .setClassesAttended(classesAttended)
                    .setTotalPresent(totalPresent)
                    .setTotalAbsent(totalAbsent)
                    .setTotalLate(totalLate)
                    .setAttendancePercentage(attendancePercentage)
                    .setAttendanceGrade(attendanceGrade)
                    .setMeetingRequirement(meetingRequirement);

        } catch (Exception e) {
            logger.warn("Failed to calculate attendance stats for student {} in classroom {}: {}",
                    studentUserId, classroomId, e.getMessage());

            // Return default stats
            return new StudentEnrolledClass.StudentClassStats()
                    .setTotalClassesHeld(0)
                    .setClassesAttended(0)
                    .setTotalPresent(0)
                    .setTotalAbsent(0)
                    .setTotalLate(0)
                    .setAttendancePercentage(0.0)
                    .setAttendanceGrade("N/A")
                    .setMeetingRequirement(true);
        }
    }

    private String calculateAttendanceGrade(double attendancePercentage) {
        if (attendancePercentage >= 95.0) return "EXCELLENT";
        if (attendancePercentage >= 85.0) return "GOOD";
        if (attendancePercentage >= 75.0) return "AVERAGE";
        return "POOR";
    }

    private String getLastAttendanceDate(String studentUserId, String classroomId) {
        try {
            // Get the most recent attendance record for this student in this classroom
            List<com.marglabs.trackmyclass.attendance.entity.AttendanceEntity> attendanceRecords =
                attendanceDao.getByStudentAndClassroom(studentUserId, classroomId);

            return attendanceRecords.stream()
                    .filter(record -> record.getStatus() == com.marglabs.trackmyclass.attendance.model.AttendanceStatus.PRESENT ||
                                    record.getStatus() == com.marglabs.trackmyclass.attendance.model.AttendanceStatus.LATE ||
                                    record.getStatus() == com.marglabs.trackmyclass.attendance.model.AttendanceStatus.PARTIAL)
                    .map(com.marglabs.trackmyclass.attendance.entity.AttendanceEntity::getAttendanceDate)
                    .max(String::compareTo)
                    .orElse("N/A");
        } catch (Exception e) {
            return "N/A";
        }
    }

    /**
     * Validate poster file and ensure it belongs to the teacher or is public
     */
    private String validatePosterFile(String posterFileId, String teacherId) {
        if (posterFileId == null || posterFileId.trim().isEmpty()) {
            return null;
        }

        try {
            FileInfo fileInfo = fileService.getFileInfo(posterFileId);

            // Check if file belongs to the teacher
            if (fileInfo.getUploadedBy().equals(teacherId)) {
                // Verify it's an image file
                String contentType = fileInfo.getContentType();
                if (contentType != null && contentType.startsWith("image/")) {
                    logger.debug("Validated poster file: {} for teacher: {}", posterFileId, teacherId);
                    return posterFileId;
                } else {
                    logger.warn("Poster file {} is not an image file. Content type: {}", posterFileId, contentType);
                    return null;
                }
            } else {
                logger.warn("Teacher {} attempted to use poster file {} they don't own", teacherId, posterFileId);
                return null;
            }
        } catch (Exception e) {
            logger.warn("Invalid poster file ID {} provided by teacher {}: {}", posterFileId, teacherId, e.getMessage());
            return null;
        }
    }

    /**
     * Enrich classroom with poster file information
     */
    private Classroom enrichClassroomWithPosterFile(Classroom classroom) {
        if (classroom.getPosterFileId() == null || classroom.getPosterFileId().trim().isEmpty()) {
            classroom.setPosterFile(null);
            return classroom;
        }

        try {
            FileInfo posterFile = fileService.getFileInfo(classroom.getPosterFileId());
            classroom.setPosterFile(posterFile);
            logger.debug("Enriched classroom {} with poster file: {}", classroom.getId(), posterFile.getOriginalFilename());
        } catch (Exception e) {
            logger.warn("Failed to load poster file {} for classroom {}: {}",
                       classroom.getPosterFileId(), classroom.getId(), e.getMessage());
            classroom.setPosterFile(null);
        }

        return classroom;
    }

    /**
     * Update classroom poster image
     */
    public Classroom updateClassroomPoster(String classroomId, String posterFileId, String teacherId) {
        logger.info("Updating poster for classroom: {} by teacher: {}", classroomId, teacherId);

        // Get existing classroom
        ClassroomEntity entity = classroomDao.getById(classroomId);

        // Verify teacher owns this classroom
        if (!entity.getTeacherId().equals(teacherId)) {
            throw new RuntimeException("Only the classroom teacher can update the poster");
        }

        // Validate poster file if provided
        String validatedPosterFileId = validatePosterFile(posterFileId, teacherId);

        // Update poster file ID
        entity.setPosterFileId(validatedPosterFileId);

        // Save updated entity
        ClassroomEntity updatedEntity = classroomDao.update(entity);

        // Convert to DTO and enrich with details
        Classroom classroom = mapper.toDto(updatedEntity);

        // Get teacher name
        try {
            User teacher = userService.getUserById(classroom.getTeacherId());
            classroom.setTeacherName(teacher.getName());
        } catch (Exception e) {
            classroom.setTeacherName("Unknown Teacher");
        }

        // Enrich with poster file information
        classroom = enrichClassroomWithPosterFile(classroom);

        logger.info("Successfully updated poster for classroom: {}", classroomId);
        return classroom;
    }

    /**
     * Remove classroom poster image
     */
    public Classroom removeClassroomPoster(String classroomId, String teacherId) {
        logger.info("Removing poster for classroom: {} by teacher: {}", classroomId, teacherId);

        // Get existing classroom
        ClassroomEntity entity = classroomDao.getById(classroomId);

        // Verify teacher owns this classroom
        if (!entity.getTeacherId().equals(teacherId)) {
            throw new RuntimeException("Only the classroom teacher can remove the poster");
        }

        // Remove poster file ID
        entity.setPosterFileId(null);

        // Save updated entity
        ClassroomEntity updatedEntity = classroomDao.update(entity);

        // Convert to DTO and enrich with details
        Classroom classroom = mapper.toDto(updatedEntity);

        // Get teacher name
        try {
            User teacher = userService.getUserById(classroom.getTeacherId());
            classroom.setTeacherName(teacher.getName());
        } catch (Exception e) {
            classroom.setTeacherName("Unknown Teacher");
        }

        // Enrich with poster file information (will be null)
        classroom = enrichClassroomWithPosterFile(classroom);

        logger.info("Successfully removed poster for classroom: {}", classroomId);
        return classroom;
    }
}
