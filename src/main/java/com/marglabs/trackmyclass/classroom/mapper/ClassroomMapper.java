package com.marglabs.trackmyclass.classroom.mapper;

import com.marglabs.trackmyclass.classroom.entity.ClassroomEntity;
import com.marglabs.trackmyclass.classroom.model.Classroom;
import com.marglabs.trackmyclass.classroom.model.ClassType;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

@Mapper(componentModel = "spring")
public interface ClassroomMapper {

    @Mapping(target = "classType", expression = "java(mapToClassType(entity.getClassType()))")
    @Mapping(target = "classFull", expression = "java(entity.getCurrentEnrollment() >= entity.getCapacity())")
    @Mapping(target = "teacherName", ignore = true) // This will be set by the service
    @Mapping(target = "totalSchedules", ignore = true) // This will be set by the service
    @Mapping(target = "pendingSchedules", ignore = true) // This will be set by the service
    Classroom toDto(ClassroomEntity entity);

    @Mapping(target = "classType", expression = "java(classroom.getClassType().name())")
    ClassroomEntity toEntity(Classroom classroom);

    @Named("mapToClassType")
    default ClassType mapToClassType(String classType) {
        return ClassType.valueOf(classType);
    }
}
