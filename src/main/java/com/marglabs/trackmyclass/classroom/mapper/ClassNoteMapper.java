package com.marglabs.trackmyclass.classroom.mapper;

import com.marglabs.trackmyclass.classroom.entity.ClassNoteEntity;
import com.marglabs.trackmyclass.classroom.model.ClassNote;
import com.marglabs.trackmyclass.classroom.model.ClassNoteRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(componentModel = "spring")
public interface ClassNoteMapper {
    
    @Mapping(target = "className", ignore = true)
    @Mapping(target = "teacherName", ignore = true)
    @Mapping(target = "createdDate", expression = "java(entity.getCreatedDate() != null ? entity.getCreatedDate().toEpochMilli() : 0)")
    @Mapping(target = "updatedDate", expression = "java(entity.getUpdatedDate() != null ? entity.getUpdatedDate().toEpochMilli() : 0)")
    ClassNote toDto(ClassNoteEntity entity);
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "teacherId", ignore = true)
    @Mapping(target = "createdDate", ignore = true)
    @Mapping(target = "updatedDate", ignore = true)
    ClassNoteEntity toEntity(ClassNoteRequest request);
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "teacherId", ignore = true)
    @Mapping(target = "createdDate", ignore = true)
    @Mapping(target = "updatedDate", ignore = true)
    void updateEntityFromRequest(ClassNoteRequest request, @MappingTarget ClassNoteEntity entity);
}