package com.marglabs.trackmyclass.schedule.entity;

import com.marglabs.trackmyclass.schedule.model.DayOfWeek;
import com.marglabs.trackmyclass.schedule.model.RecurrenceType;
import com.marglabs.trackmyclass.schedule.model.SessionStatus;
import com.marglabs.trackmyclass.schedule.model.SessionType;
import jakarta.persistence.*;
import lombok.Data;

import java.time.Instant;
import java.util.List;

@Entity
@Table(name = "class_schedules")
@Data
public class ClassScheduleEntity {
    @Id
    private String id;
    
    @Column(name = "classroom_id", nullable = false)
    private String classroomId;
    
    @Column(name = "teacher_id", nullable = false)
    private String teacherId;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "session_type", nullable = false)
    private SessionType sessionType;
    
    // UTC Instant fields for precise storage and queries
    @Column(name = "start_date_utc", nullable = false)
    private Instant startDateUtc;
    
    @Column(name = "end_date_utc")
    private Instant endDateUtc;
    
    @Column(name = "session_start_time_utc", nullable = false)
    private Instant sessionStartTimeUtc;
    
    @Column(name = "session_end_time_utc", nullable = false)
    private Instant sessionEndTimeUtc;
    
    @Column(name = "meeting_link", nullable = false, length = 500)
    private String meetingLink;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private SessionStatus status;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "recurrence_type")
    private RecurrenceType recurrenceType;
    
    @ElementCollection(targetClass = DayOfWeek.class)
    @Enumerated(EnumType.STRING)
    @CollectionTable(name = "schedule_days_of_week", joinColumns = @JoinColumn(name = "schedule_id"))
    @Column(name = "day_of_week")
    private List<DayOfWeek> daysOfWeek;
    
    @Column(name = "parent_schedule_id")
    private String parentScheduleId;
    
    @Column(name = "created_date")
    private long createdDate;
    
    @Column(name = "updated_date")
    private long updatedDate;
    
    @Column
    private boolean active;
}
