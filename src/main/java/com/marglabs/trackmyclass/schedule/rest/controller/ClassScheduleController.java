package com.marglabs.trackmyclass.schedule.rest.controller;

import com.marglabs.trackmyclass.schedule.model.ClassSchedule;
import com.marglabs.trackmyclass.schedule.model.ScheduleCreationRequest;
import com.marglabs.trackmyclass.schedule.model.TimezoneAwareSchedule;
import com.marglabs.trackmyclass.schedule.model.ScheduleUpdateRequest;
import com.marglabs.trackmyclass.schedule.service.ClassScheduleService;
import com.marglabs.trackmyclass.user.model.RoleType;
import com.marglabs.trackmyclass.user.model.User;
import com.marglabs.trackmyclass.user.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/scheduleManagement/v1")
@Tag(name = "Class Schedule Management", description = "API for managing class schedules")
public class ClassScheduleController {

    @Autowired
    private ClassScheduleService scheduleService;

    @Autowired
    private UserService userService;

    @Operation(summary = "Create a new class schedule", description = "Creates a new class schedule (single or recurring). Specify the timezone for accurate time conversion to UTC. Times will be converted from the specified timezone to UTC for storage.")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Schedule created successfully",
                content = @Content(schema = @Schema(implementation = ClassSchedule.class))),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Forbidden - only teachers can create schedules"),
        @ApiResponse(responseCode = "409", description = "Schedule conflict detected")
    })
    @PostMapping(value = "/schedules", consumes = "application/json", produces = "application/json")
    @ResponseStatus(HttpStatus.CREATED)
    public ClassSchedule createSchedule(
            @AuthenticationPrincipal Jwt userPrincipal,
            @Parameter(description = "Schedule details") @Valid @RequestBody ScheduleCreationRequest request) {

        // Get user from Cognito token
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());

        // Verify user is a teacher
        boolean isTeacher = user.getRoles().stream()
                .anyMatch(role -> role.getRole() == RoleType.TEACHER);

        if (!isTeacher) {
            throw new AccessDeniedException("Only teachers can create schedules");
        }

        return scheduleService.createSchedule(request, user.getId());
    }

    @Operation(summary = "Get schedule by ID", description = "Returns a schedule by its ID")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successful operation",
                content = @Content(schema = @Schema(implementation = ClassSchedule.class))),
        @ApiResponse(responseCode = "404", description = "Schedule not found")
    })
    @GetMapping(value = "/schedules/{id}", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public ClassSchedule getScheduleById(
            @Parameter(description = "Schedule ID") @PathVariable String id) {
        return scheduleService.getScheduleById(id);
    }

    @Operation(summary = "Update schedule", description = "Updates an existing schedule")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Schedule updated successfully",
                content = @Content(schema = @Schema(implementation = ClassSchedule.class))),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Forbidden - only the teacher who created the schedule can update it"),
        @ApiResponse(responseCode = "404", description = "Schedule not found"),
        @ApiResponse(responseCode = "409", description = "Schedule conflict detected")
    })
    @PutMapping(value = "/schedules/{id}", consumes = "application/json", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public ClassSchedule updateSchedule(
            @AuthenticationPrincipal Jwt userPrincipal,
            @Parameter(description = "Schedule ID") @PathVariable String id,
            @Parameter(description = "Updated schedule details") @Valid @RequestBody ScheduleUpdateRequest request) {

        // Get user from Cognito token
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());

        // Verify user is a teacher
        boolean isTeacher = user.getRoles().stream()
                .anyMatch(role -> role.getRole() == RoleType.TEACHER);

        if (!isTeacher) {
            throw new AccessDeniedException("Only teachers can update schedules");
        }

        return scheduleService.updateSchedule(id, request, user.getId());
    }

    @Operation(summary = "Cancel schedule", description = "Cancels an existing schedule")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Schedule cancelled successfully",
                content = @Content(schema = @Schema(implementation = ClassSchedule.class))),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Forbidden - only the teacher who created the schedule can cancel it"),
        @ApiResponse(responseCode = "404", description = "Schedule not found")
    })
    @PatchMapping(value = "/schedules/{id}/cancel", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public ClassSchedule cancelSchedule(
            @AuthenticationPrincipal Jwt userPrincipal,
            @Parameter(description = "Schedule ID") @PathVariable String id) {

        // Get user from Cognito token
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());

        // Verify user is a teacher
        boolean isTeacher = user.getRoles().stream()
                .anyMatch(role -> role.getRole() == RoleType.TEACHER);

        if (!isTeacher) {
            throw new AccessDeniedException("Only teachers can cancel schedules");
        }
        scheduleService.cancelSchedule(id, user.getId());
        return scheduleService.getScheduleById(id);
    }
    
    @Operation(summary = "Update schedule status", description = "Updates the status of an existing schedule")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Schedule status updated successfully",
                content = @Content(schema = @Schema(implementation = ClassSchedule.class))),
        @ApiResponse(responseCode = "400", description = "Invalid status"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Forbidden - only the teacher who created the schedule can update status"),
        @ApiResponse(responseCode = "404", description = "Schedule not found")
    })
    @PatchMapping(value = "/schedules/{id}/status", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public ClassSchedule updateScheduleStatus(
            @AuthenticationPrincipal Jwt userPrincipal,
            @Parameter(description = "Schedule ID") @PathVariable String id,
            @Parameter(description = "New status") @RequestParam String status) {

        User user = userService.getUserByCognitoId(userPrincipal.getSubject());

        boolean isTeacher = user.getRoles().stream()
                .anyMatch(role -> role.getRole() == RoleType.TEACHER);

        if (!isTeacher) {
            throw new AccessDeniedException("Only teachers can update schedule status");
        }
        
        return scheduleService.updateScheduleStatus(id, status, user.getId());
    }

    @Operation(summary = "Get teacher's schedules", description = "Returns paginated schedules for the authenticated teacher")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successful operation"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Forbidden - only teachers can view their schedules")
    })
    @GetMapping(value = "/schedules/teacher", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public Page<ClassSchedule> getTeacherSchedules(
            @AuthenticationPrincipal Jwt userPrincipal,
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size) {

        // Get user from Cognito token
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());

        // Verify user is a teacher
        boolean isTeacher = user.getRoles().stream()
                .anyMatch(role -> role.getRole() == RoleType.TEACHER);

        if (!isTeacher) {
            throw new AccessDeniedException("Only teachers can view their schedules");
        }

        return scheduleService.getSchedulesByTeacherPaginated(
                user.getId(),
                PageRequest.of(page, size, Sort.by(Sort.Direction.ASC, "startDate", "startTime")));
    }

    @Operation(summary = "Get classroom schedules", description = "Returns paginated schedules for a specific classroom")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successful operation"),
        @ApiResponse(responseCode = "404", description = "Classroom not found")
    })
    @GetMapping(value = "/classrooms/{classroomId}/schedules", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public Page<ClassSchedule> getClassroomSchedules(
            @Parameter(description = "Classroom ID") @PathVariable String classroomId,
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size) {

        return scheduleService.getSchedulesByClassroomPaginated(
                classroomId,
                PageRequest.of(page, size, Sort.by(Sort.Direction.ASC, "startDate", "startTime")));
    }

    @Operation(summary = "Get upcoming schedules", description = "Returns top 5 upcoming schedules for the authenticated user (teacher, student, or parent)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successful operation"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/schedules/upcoming", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<ClassSchedule> getUpcomingSchedules(
            @AuthenticationPrincipal Jwt userPrincipal) {

        // Get user from Cognito token
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());

        // Check user role and get appropriate schedules
        boolean isTeacher = user.getRoles().stream()
                .anyMatch(role -> role.getRole() == RoleType.TEACHER);
        boolean isStudent = user.getRoles().stream()
                .anyMatch(role -> role.getRole() == RoleType.STUDENT);
        boolean isParent = user.getRoles().stream()
                .anyMatch(role -> role.getRole() == RoleType.PARENT);

        if (isTeacher) {
            return scheduleService.getTop5UpcomingSchedulesByTeacher(user.getId());
        } else if (isStudent) {
            return scheduleService.getTop5UpcomingSchedulesByStudent(user.getId());
        } else if (isParent) {
            return scheduleService.getTop5UpcomingSchedulesByParent(user.getId());
        } else {
            throw new AccessDeniedException("User must have a valid role to view schedules");
        }
    }

    @Operation(summary = "Get today's schedules", description = "Returns today's schedules for the authenticated teacher")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successful operation"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Forbidden - only teachers can view their schedules")
    })
    @GetMapping(value = "/schedules/teacher/today", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<ClassSchedule> getTodaySchedules(
            @AuthenticationPrincipal Jwt userPrincipal) {

        // Get user from Cognito token
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());

        // Verify user is a teacher
        boolean isTeacher = user.getRoles().stream()
                .anyMatch(role -> role.getRole() == RoleType.TEACHER);

        if (!isTeacher) {
            throw new AccessDeniedException("Only teachers can view their schedules");
        }

        return scheduleService.getTodaySchedulesByTeacher(user.getId());
    }

    @Operation(summary = "Get next upcoming class for teacher",
               description = "Get the next upcoming class for the authenticated teacher")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Next upcoming class retrieved successfully"),
        @ApiResponse(responseCode = "204", description = "No upcoming classes found"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Only teachers can access this endpoint")
    })
    @GetMapping(value = "/schedules/teacher/next-upcoming", produces = "application/json")
    public ResponseEntity<ClassSchedule> getNextUpcomingClassForTeacher(@Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());

        // Verify user is a teacher
        boolean isTeacher = teacher.getRoles().stream()
                .anyMatch(role -> role.getRole() == RoleType.TEACHER);

        if (!isTeacher) {
            throw new AccessDeniedException("Only teachers can access this endpoint");
        }

        ClassSchedule nextClass = scheduleService.getNextUpcomingClassByTeacher(teacher.getId());

        if (nextClass == null) {
            return ResponseEntity.noContent().build();
        }

        return ResponseEntity.ok(nextClass);
    }

    @Operation(summary = "Get next upcoming class for student",
               description = "Get the next upcoming class for the authenticated student based on their enrollments")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Next upcoming class retrieved successfully"),
        @ApiResponse(responseCode = "204", description = "No upcoming classes found"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/schedules/student/next-upcoming", produces = "application/json")
    public ResponseEntity<ClassSchedule> getNextUpcomingClassForStudent(@Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        User student = userService.getUserByCognitoId(userPrincipal.getSubject());

        ClassSchedule nextClass = scheduleService.getNextUpcomingClassByStudent(student.getId());

        if (nextClass == null) {
            return ResponseEntity.noContent().build();
        }

        return ResponseEntity.ok(nextClass);
    }

    @Operation(summary = "Get schedule with timezone information",
               description = "Get a specific schedule with time information converted to multiple timezones for international students and teachers")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Schedule with timezone information retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "404", description = "Schedule not found")
    })
    @GetMapping(value = "/schedules/{scheduleId}/timezones", produces = "application/json")
    public ResponseEntity<TimezoneAwareSchedule> getScheduleWithTimezones(
            @Parameter(description = "Schedule ID") @PathVariable String scheduleId,
            @Parameter(description = "Comma-separated list of timezone IDs (e.g., America/New_York,Asia/Kolkata,Europe/London). If not provided, common educational timezones will be used.")
            @RequestParam(required = false) String timezones,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {

        User user = userService.getUserByCognitoId(userPrincipal.getSubject());

        // Parse requested timezones
        List<String> requestedTimezones = null;
        if (timezones != null && !timezones.trim().isEmpty()) {
            requestedTimezones = Arrays.asList(timezones.split(","));
            // Trim whitespace from each timezone
            requestedTimezones = requestedTimezones.stream()
                    .map(String::trim)
                    .collect(Collectors.toList());
        }

        TimezoneAwareSchedule timezoneAwareSchedule = scheduleService.getScheduleByIdWithTimezones(scheduleId, requestedTimezones);

        return ResponseEntity.ok(timezoneAwareSchedule);
    }

    @Operation(summary = "Get available timezones",
               description = "Get list of common timezones used in educational institutions")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Available timezones retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/timezones", produces = "application/json")
    public ResponseEntity<Map<String, Object>> getAvailableTimezones(@Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());

        // Get timezone utility from service
        List<String> commonTimezones = Arrays.asList(
            "UTC",
            "America/New_York",    // Eastern Time
            "America/Chicago",     // Central Time
            "America/Denver",      // Mountain Time
            "America/Los_Angeles", // Pacific Time
            "Europe/London",       // GMT/BST
            "Europe/Paris",        // CET/CEST
            "Asia/Kolkata",        // IST
            "Asia/Shanghai",       // CST
            "Asia/Tokyo",          // JST
            "Australia/Sydney",    // AEST/AEDT
            "Asia/Dubai",          // GST
            "America/Toronto",     // Eastern Time (Canada)
            "Europe/Berlin",       // CET/CEST
            "Asia/Singapore"       // SGT
        );

        Map<String, Object> response = new HashMap<>();
        response.put("commonTimezones", commonTimezones);
        response.put("totalCount", commonTimezones.size());
        response.put("usage", "Use these timezone IDs in the 'timezones' parameter for the schedule endpoint");
        response.put("example", "?timezones=America/New_York,Asia/Kolkata,Europe/London");

        return ResponseEntity.ok(response);
    }

}
