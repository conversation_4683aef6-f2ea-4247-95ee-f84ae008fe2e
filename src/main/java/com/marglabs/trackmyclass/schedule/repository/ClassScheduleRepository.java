package com.marglabs.trackmyclass.schedule.repository;

import com.marglabs.trackmyclass.schedule.entity.ClassScheduleEntity;
import com.marglabs.trackmyclass.schedule.model.SessionStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.data.rest.core.annotation.RepositoryRestResource;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

@Repository
@RepositoryRestResource(exported = false)
public interface ClassScheduleRepository extends JpaRepository<ClassScheduleEntity, String> {

    // Find schedules by classroom
    Page<ClassScheduleEntity> findByClassroomIdAndActiveTrue(String classroomId, Pageable pageable);
    List<ClassScheduleEntity> findByClassroomIdAndActiveTrue(String classroomId);

    // Find schedules by teacher
    Page<ClassScheduleEntity> findByTeacherIdAndActiveTrue(String teacherId, Pageable pageable);
    List<ClassScheduleEntity> findByTeacherIdAndActiveTrue(String teacherId);

    // Find schedules by date range using UTC timestamps
    @Query("SELECT s FROM ClassScheduleEntity s WHERE s.teacherId = :teacherId AND s.active = true " +
           "AND s.startDateUtc <= :endDateUtc AND (s.endDateUtc IS NULL OR s.endDateUtc >= :startDateUtc)")
    List<ClassScheduleEntity> findByTeacherIdAndDateRange(@Param("teacherId") String teacherId,
                                                          @Param("startDateUtc") Instant startDateUtc,
                                                          @Param("endDateUtc") Instant endDateUtc);

    // Check for time conflicts using UTC timestamps
    @Query("SELECT s FROM ClassScheduleEntity s WHERE s.teacherId = :teacherId AND s.active = true " +
           "AND s.status != 'CANCELLED' " +
           "AND ((s.sessionStartTimeUtc < :endTimeUtc AND s.sessionEndTimeUtc > :startTimeUtc))")
    List<ClassScheduleEntity> findConflictingSchedules(@Param("teacherId") String teacherId,
                                                       @Param("startTimeUtc") Instant startTimeUtc,
                                                       @Param("endTimeUtc") Instant endTimeUtc);

    // Check for time conflicts excluding a specific schedule (for updates)
    @Query("SELECT s FROM ClassScheduleEntity s WHERE s.teacherId = :teacherId AND s.active = true " +
           "AND s.status != 'CANCELLED' AND s.id != :excludeId " +
           "AND ((s.sessionStartTimeUtc < :endTimeUtc AND s.sessionEndTimeUtc > :startTimeUtc))")
    List<ClassScheduleEntity> findConflictingSchedulesExcluding(@Param("teacherId") String teacherId,
                                                                @Param("startTimeUtc") Instant startTimeUtc,
                                                                @Param("endTimeUtc") Instant endTimeUtc,
                                                                @Param("excludeId") String excludeId);

    // Find schedules by parent schedule ID (for recurring sessions)
    List<ClassScheduleEntity> findByParentScheduleIdAndActiveTrue(String parentScheduleId);

    // Find schedules by status
    List<ClassScheduleEntity> findByStatusAndActiveTrue(SessionStatus status);

    // Find upcoming schedules for a teacher using UTC timestamps
    @Query("SELECT s FROM ClassScheduleEntity s WHERE s.teacherId = :teacherId AND s.active = true " +
           "AND s.status = 'SCHEDULED' AND s.sessionStartTimeUtc >= :currentTimeUtc " +
           "ORDER BY s.sessionStartTimeUtc ASC")
    List<ClassScheduleEntity> findUpcomingSchedulesByTeacher(@Param("teacherId") String teacherId,
                                                             @Param("currentTimeUtc") Instant currentTimeUtc);

    // Find today's schedules for a teacher using UTC timestamps
    @Query("SELECT s FROM ClassScheduleEntity s WHERE s.teacherId = :teacherId AND s.active = true " +
           "AND s.status IN ('SCHEDULED', 'IN_PROGRESS') " +
           "AND s.sessionStartTimeUtc >= :startOfDayUtc AND s.sessionStartTimeUtc < :endOfDayUtc " +
           "ORDER BY s.sessionStartTimeUtc ASC")
    List<ClassScheduleEntity> findTodaySchedulesByTeacher(@Param("teacherId") String teacherId,
                                                          @Param("startOfDayUtc") Instant startOfDayUtc,
                                                          @Param("endOfDayUtc") Instant endOfDayUtc);

    // Count total schedules by classroom
    long countByClassroomIdAndActiveTrue(String classroomId);

    // Count pending/scheduled sessions by classroom
    @Query("SELECT COUNT(s) FROM ClassScheduleEntity s WHERE s.classroomId = :classroomId AND s.active = true " +
           "AND s.status IN ('SCHEDULED', 'IN_PROGRESS')")
    long countPendingSchedulesByClassroom(@Param("classroomId") String classroomId);

    // Get upcoming schedules for teacher (top N by most recent date)
    @Query("SELECT s FROM ClassScheduleEntity s WHERE s.teacherId = :teacherId AND s.active = true " +
           "AND s.status = 'SCHEDULED' AND s.sessionStartTimeUtc > :currentTime " +
           "ORDER BY s.sessionStartTimeUtc ASC")
    List<ClassScheduleEntity> findUpcomingSchedulesByTeacher(@Param("teacherId") String teacherId,
                                                             @Param("currentTime") Instant currentTime,
                                                             Pageable pageable);

    // Get upcoming schedules for student (top N by most recent date)
    @Query("SELECT s FROM ClassScheduleEntity s " +
           "JOIN EnrollmentEntity e ON s.classroomId = e.classroomId " +
           "WHERE e.userId = :studentId AND e.role = 'STUDENT' AND e.status = 'ACTIVE' " +
           "AND s.active = true AND s.status = 'SCHEDULED' " +
           "AND s.sessionStartTimeUtc > :currentTime " +
           "ORDER BY s.sessionStartTimeUtc ASC")
    List<ClassScheduleEntity> findUpcomingSchedulesByStudent(@Param("studentId") String studentId,
                                                             @Param("currentTime") Instant currentTime,
                                                             Pageable pageable);

    // Get upcoming schedules for parent (top N by most recent date)
    @Query("SELECT s FROM ClassScheduleEntity s " +
           "JOIN EnrollmentEntity e ON s.classroomId = e.classroomId " +
           "WHERE e.userId = :parentId AND e.role = 'PARENT' AND e.status = 'ACTIVE' " +
           "AND s.active = true AND s.status = 'SCHEDULED' " +
           "AND s.sessionStartTimeUtc > :currentTime " +
           "ORDER BY s.sessionStartTimeUtc ASC")
    List<ClassScheduleEntity> findUpcomingSchedulesByParent(@Param("parentId") String parentId,
                                                            @Param("currentTime") Instant currentTime,
                                                            Pageable pageable);
    
    List<ClassScheduleEntity> findByActiveTrueAndStatus(SessionStatus status);
}
