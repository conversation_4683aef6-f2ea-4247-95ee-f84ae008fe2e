package com.marglabs.trackmyclass.schedule.service;

import com.marglabs.common.rest.error.GeneralException;
import com.marglabs.common.utils.IdGenerator;
import com.marglabs.trackmyclass.classroom.service.ClassroomService;
import com.marglabs.trackmyclass.common.util.DateTimeUtil;
import com.marglabs.trackmyclass.enrollment.model.Enrollment;
import com.marglabs.trackmyclass.enrollment.model.EnrollmentStatus;
import com.marglabs.trackmyclass.enrollment.service.EnrollmentService;
import com.marglabs.trackmyclass.schedule.dao.ClassScheduleDao;
import com.marglabs.trackmyclass.schedule.entity.ClassScheduleEntity;
import com.marglabs.trackmyclass.schedule.mapper.ClassScheduleMapper;
import com.marglabs.trackmyclass.schedule.model.*;
import com.marglabs.trackmyclass.schedule.util.TimezoneUtil;
import com.marglabs.trackmyclass.user.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Transactional
public class ClassScheduleService {

    private static final Logger logger = LoggerFactory.getLogger(ClassScheduleService.class);

    @Autowired
    private ClassScheduleDao scheduleDao;

    @Autowired
    private ClassScheduleMapper mapper;

    @Autowired
    private IdGenerator idGenerator;

    @Autowired
    private ClassroomService classroomService;

    @Autowired
    private UserService userService;

    @Autowired
    private EnrollmentService enrollmentService;

    @Autowired
    private TimezoneUtil timezoneUtil;

    /**
     * Create a new schedule
     */
    public ClassSchedule createSchedule(ScheduleCreationRequest request, String teacherId) {
        logger.info("Creating schedule for teacher: {} in timezone: {}", teacherId, request.getTimezone());

        // Validate timezone
        if (!DateTimeUtil.isValidTimezone(request.getTimezone())) {
            throw new GeneralException(HttpStatus.BAD_REQUEST, "SCHEDULE", "INVALID_TIMEZONE",
                    "invalid_timezone", request.getTimezone());
        }

        // Validate the request
        validateScheduleRequest(request, teacherId);

        // Verify classroom exists and teacher has access
        var classroom = classroomService.getClassroomById(request.getClassroomId());
        if (!classroom.getTeacherId().equals(teacherId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "SCHEDULE", "UNAUTHORIZED_CLASSROOM_ACCESS",
                    "teacher_not_authorized_for_classroom", request.getClassroomId());
        }

        if (request.getRecurrenceType() == RecurrenceType.SINGLE) {
            return createSingleSession(request, teacherId);
        } else {
            return createRecurringSchedule(request, teacherId);
        }
    }

    /**
     * Create a single session
     */
    private ClassSchedule createSingleSession(ScheduleCreationRequest request, String teacherId) {
        // Create UTC Instants using user's timezone
        Instant sessionStartUtc = DateTimeUtil.combineToUtcInstantWithTimezone(
            request.getStartDate(), request.getSessionStartTime(), request.getTimezone());
        Instant sessionEndUtc = DateTimeUtil.combineToUtcInstantWithTimezone(
            request.getStartDate(), request.getSessionEndTime(), request.getTimezone());

        // Check for conflicts
        checkTimeConflicts(teacherId, sessionStartUtc, sessionEndUtc, null);

        ClassSchedule schedule = new ClassSchedule()
                .setId(idGenerator.generateId())
                .setClassroomId(request.getClassroomId())
                .setTeacherId(teacherId)
                .setSessionType(SessionType.SINGLE)
                .setStartDate(request.getStartDate())
                .setSessionStartTime(request.getSessionStartTime())
                .setSessionEndTime(request.getSessionEndTime())
                .setStartDateUtc(DateTimeUtil.combineToUtcInstantWithTimezone(request.getStartDate(), "00:00", request.getTimezone()))
                .setSessionStartTimeUtc(sessionStartUtc)
                .setSessionEndTimeUtc(sessionEndUtc)
                .setMeetingLink(request.getMeetingLink())
                .setStatus(SessionStatus.SCHEDULED)
                .setRecurrenceType(RecurrenceType.SINGLE)
                .setActive(true);

        // Set timestamps
        long currentTime = System.currentTimeMillis();
        schedule.setCreatedDate(currentTime);
        schedule.setUpdatedDate(currentTime);

        // Save to database
        ClassScheduleEntity entity = mapper.toEntity(schedule);
        // Set the UTC fields manually since mapper ignores them
        entity.setStartDateUtc(schedule.getStartDateUtc());
        entity.setSessionStartTimeUtc(schedule.getSessionStartTimeUtc());
        entity.setSessionEndTimeUtc(schedule.getSessionEndTimeUtc());
        entity = scheduleDao.create(entity);

        return enrichScheduleWithNames(mapper.toDto(entity));
    }

    /**
     * Create recurring schedule by generating individual sessions directly
     */
    private ClassSchedule createRecurringSchedule(ScheduleCreationRequest request, String teacherId) {
        logger.info("Creating recurring schedule for teacher {} with {} days of week",
                   teacherId, request.getDaysOfWeek().size());

        // Calculate all session dates based on date range and days of week
        LocalDate startDate = LocalDate.parse(request.getStartDate());
        LocalDate endDate = LocalDate.parse(request.getEndDate());
        List<LocalDate> sessionDates = calculateSessionDatesForDaysOfWeek(startDate, endDate, request.getDaysOfWeek());

        if (sessionDates.isEmpty()) {
            throw new GeneralException(HttpStatus.BAD_REQUEST, "SCHEDULE", "NO_SESSIONS_GENERATED",
                    "no_sessions_for_date_range", request.getStartDate(), request.getEndDate());
        }

        logger.info("Generated {} session dates for recurring schedule", sessionDates.size());

        List<ClassSchedule> createdSessions = new ArrayList<>();
        List<LocalDate> conflictDates = new ArrayList<>();

        // Create individual sessions for each calculated date
        for (LocalDate sessionDate : sessionDates) {
            try {
                // Create UTC instants for this session using user's timezone
                Instant sessionStartUtc = DateTimeUtil.combineToUtcInstantWithTimezone(
                    sessionDate.toString(), request.getSessionStartTime(), request.getTimezone());
                Instant sessionEndUtc = DateTimeUtil.combineToUtcInstantWithTimezone(
                    sessionDate.toString(), request.getSessionEndTime(), request.getTimezone());

                // Check for conflicts
                checkTimeConflicts(teacherId, sessionStartUtc, sessionEndUtc, null);

                // Create individual session
                ClassSchedule individualSession = new ClassSchedule()
                        .setId(idGenerator.generateId())
                        .setClassroomId(request.getClassroomId())
                        .setTeacherId(teacherId)
                        .setSessionType(SessionType.SINGLE)
                        .setStartDate(sessionDate.toString())
                        .setEndDate(sessionDate.toString()) // Same day for individual sessions
                        .setSessionStartTime(request.getSessionStartTime())
                        .setSessionEndTime(request.getSessionEndTime())
                        .setStartDateUtc(DateTimeUtil.combineToUtcInstantWithTimezone(sessionDate.toString(), "00:00", request.getTimezone()))
                        .setEndDateUtc(DateTimeUtil.combineToUtcInstantWithTimezone(sessionDate.toString(), "00:00", request.getTimezone()))
                        .setSessionStartTimeUtc(sessionStartUtc)
                        .setSessionEndTimeUtc(sessionEndUtc)
                        .setMeetingLink(request.getMeetingLink())
                        .setStatus(SessionStatus.SCHEDULED)
                        .setRecurrenceType(RecurrenceType.SINGLE) // Each session is individual
                        .setDaysOfWeek(null) // Not needed for individual sessions
                        .setParentScheduleId(null) // No parent schedule
                        .setActive(true);

                // Set timestamps
                long currentTime = System.currentTimeMillis();
                individualSession.setCreatedDate(currentTime);
                individualSession.setUpdatedDate(currentTime);

                // Save individual session
                ClassScheduleEntity sessionEntity = mapper.toEntity(individualSession);
                sessionEntity.setStartDateUtc(individualSession.getStartDateUtc());
                sessionEntity.setEndDateUtc(individualSession.getEndDateUtc());
                sessionEntity.setSessionStartTimeUtc(individualSession.getSessionStartTimeUtc());
                sessionEntity.setSessionEndTimeUtc(individualSession.getSessionEndTimeUtc());

                ClassScheduleEntity savedEntity = scheduleDao.create(sessionEntity);
                ClassSchedule savedSession = enrichScheduleWithNames(mapper.toDto(savedEntity));
                createdSessions.add(savedSession);

                logger.debug("Created individual session for date: {}", sessionDate);

            } catch (GeneralException e) {
                conflictDates.add(sessionDate);
                logger.warn("Conflict found for date: {} - skipping session. Error: {}", sessionDate, e.getMessage());
            } catch (Exception e) {
                logger.error("Failed to create session for date: {} - Error: {}", sessionDate, e.getMessage());
                conflictDates.add(sessionDate);
            }
        }

        if (createdSessions.isEmpty()) {
            throw new GeneralException(HttpStatus.CONFLICT, "SCHEDULE", "ALL_SESSIONS_CONFLICT",
                    "all_recurring_sessions_have_conflicts", String.valueOf(conflictDates.size()));
        }

        if (!conflictDates.isEmpty()) {
            logger.info("Created {} sessions successfully, {} sessions skipped due to conflicts on dates: {}",
                       createdSessions.size(), conflictDates.size(), conflictDates);
        } else {
            logger.info("Successfully created all {} recurring sessions", createdSessions.size());
        }

        // Return the first created session as representative
        ClassSchedule firstSession = createdSessions.get(0);

        logger.info("Recurring schedule creation completed. First session ID: {}, Total sessions: {}, Conflicts: {}",
                   firstSession.getId(), createdSessions.size(), conflictDates.size());

        return firstSession;
    }



    /**
     * Calculate session dates based on specific days of the week
     */
    private List<LocalDate> calculateSessionDatesForDaysOfWeek(LocalDate startDate, LocalDate endDate,
                                                              List<DayOfWeek> daysOfWeek) {
        List<LocalDate> dates = new ArrayList<>();
        LocalDate currentDate = startDate;

        // Convert our DayOfWeek enum to Java's DayOfWeek for easier comparison
        List<java.time.DayOfWeek> javaDaysOfWeek = daysOfWeek.stream()
                .map(DayOfWeek::toJavaTime)
                .collect(Collectors.toList());

        while (!currentDate.isAfter(endDate)) {
            // Check if current date's day of week is in our list
            if (javaDaysOfWeek.contains(currentDate.getDayOfWeek())) {
                dates.add(currentDate);
            }
            currentDate = currentDate.plusDays(1);
        }

        return dates;
    }

    /**
     * Update an existing schedule
     */
    public ClassSchedule updateSchedule(String scheduleId, ScheduleUpdateRequest request, String teacherId) {
        // Get existing schedule
        ClassScheduleEntity existingSchedule = scheduleDao.getById(scheduleId);

        // Verify teacher has access
        if (!existingSchedule.getTeacherId().equals(teacherId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "SCHEDULE", "UNAUTHORIZED_SCHEDULE_ACCESS",
                    "teacher_not_authorized_for_schedule", scheduleId);
        }

        // Validate timezone if provided
        if (request.getTimezone() != null && !DateTimeUtil.isValidTimezone(request.getTimezone())) {
            throw new GeneralException(HttpStatus.BAD_REQUEST, "SCHEDULE", "INVALID_TIMEZONE",
                    "invalid_timezone", request.getTimezone());
        }

        // Check for conflicts if time is being changed
        if (request.getSessionStartTime() != null || request.getSessionEndTime() != null || request.getTimezone() != null) {
            String timezone = request.getTimezone() != null ? request.getTimezone() : "UTC"; // Default to UTC if not provided
            String newStartTime = request.getSessionStartTime() != null ? request.getSessionStartTime() :
                DateTimeUtil.formatUtcInstantToTime(existingSchedule.getSessionStartTimeUtc());
            String newEndTime = request.getSessionEndTime() != null ? request.getSessionEndTime() :
                DateTimeUtil.formatUtcInstantToTime(existingSchedule.getSessionEndTimeUtc());
            String sessionDate = request.getStartDate() != null ? request.getStartDate() :
                DateTimeUtil.formatUtcInstantToDate(existingSchedule.getStartDateUtc());

            Instant newStartUtc = DateTimeUtil.combineToUtcInstantWithTimezone(sessionDate, newStartTime, timezone);
            Instant newEndUtc = DateTimeUtil.combineToUtcInstantWithTimezone(sessionDate, newEndTime, timezone);

            checkTimeConflicts(teacherId, newStartUtc, newEndUtc, scheduleId);
        }

        // Update fields using timezone-aware conversion
        String timezone = request.getTimezone() != null ? request.getTimezone() : "UTC"; // Default to UTC if not provided

        if (request.getStartDate() != null) {
            existingSchedule.setStartDateUtc(DateTimeUtil.combineToUtcInstantWithTimezone(request.getStartDate(), "00:00", timezone));
        }
        if (request.getEndDate() != null) {
            existingSchedule.setEndDateUtc(DateTimeUtil.combineToUtcInstantWithTimezone(request.getEndDate(), "23:59", timezone));
        }
        if (request.getSessionStartTime() != null) {
            String sessionDate = request.getStartDate() != null ? request.getStartDate() :
                DateTimeUtil.formatUtcInstantToDate(existingSchedule.getStartDateUtc());
            existingSchedule.setSessionStartTimeUtc(DateTimeUtil.combineToUtcInstantWithTimezone(sessionDate, request.getSessionStartTime(), timezone));
        }
        if (request.getSessionEndTime() != null) {
            String sessionDate = request.getStartDate() != null ? request.getStartDate() :
                DateTimeUtil.formatUtcInstantToDate(existingSchedule.getStartDateUtc());
            existingSchedule.setSessionEndTimeUtc(DateTimeUtil.combineToUtcInstantWithTimezone(sessionDate, request.getSessionEndTime(), timezone));
        }
        if (request.getMeetingLink() != null) {
            existingSchedule.setMeetingLink(request.getMeetingLink());
        }
        if (request.getDaysOfWeek() != null) {
            existingSchedule.setDaysOfWeek(request.getDaysOfWeek());
        }
        // Note: Status is managed automatically by the system, not set from update requests

        existingSchedule.setUpdatedDate(System.currentTimeMillis());

        // Save updated schedule
        ClassScheduleEntity updatedEntity = scheduleDao.update(existingSchedule);
        return enrichScheduleWithNames(mapper.toDto(updatedEntity));
    }

    /**
     * Get schedule by ID
     */
    public ClassSchedule getScheduleById(String id) {
        ClassScheduleEntity entity = scheduleDao.getById(id);
        return enrichScheduleWithNames(mapper.toDto(entity));
    }

    /**
     * Get schedules by teacher ID
     */
    public List<ClassSchedule> getSchedulesByTeacherId(String teacherId) {
        List<ClassScheduleEntity> entities = scheduleDao.getByTeacherId(teacherId);
        return entities.stream()
                .map(entity -> enrichScheduleWithNames(mapper.toDto(entity)))
                .collect(Collectors.toList());
    }

    /**
     * Get schedules by classroom ID
     */
    public List<ClassSchedule> getSchedulesByClassroomId(String classroomId) {
        List<ClassScheduleEntity> entities = scheduleDao.getByClassroomIdAndActiveTrue(classroomId);
        return entities.stream()
                .map(entity -> enrichScheduleWithNames(mapper.toDto(entity)))
                .collect(Collectors.toList());
    }

    /**
     * Get schedules with pagination
     */
    public Page<ClassSchedule> getSchedules(Pageable pageable) {
        List<ClassScheduleEntity> allEntities = scheduleDao.getAll();
        List<ClassSchedule> schedules = allEntities.stream()
                .map(entity -> enrichScheduleWithNames(mapper.toDto(entity)))
                .collect(Collectors.toList());

        // Simple pagination implementation
        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), schedules.size());
        List<ClassSchedule> pageContent = schedules.subList(start, end);

        return new org.springframework.data.domain.PageImpl<>(pageContent, pageable, schedules.size());
    }

    /**
     * Cancel schedule
     */
    public void cancelSchedule(String id, String teacherId) {
        ClassScheduleEntity schedule = scheduleDao.getById(id);

        // Verify teacher has access
        if (!schedule.getTeacherId().equals(teacherId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "SCHEDULE", "UNAUTHORIZED_SCHEDULE_ACCESS",
                    "teacher_not_authorized_for_schedule", id);
        }

        updateScheduleStatus(id,SessionStatus.CANCELLED,false);

    }

    /**
     * Update schedule status (public method for controller)
     */
    public ClassSchedule updateScheduleStatus(String scheduleId, String statusString, String teacherId) {
        ClassScheduleEntity schedule = scheduleDao.getById(scheduleId);
        
        // Verify teacher has access
        if (!schedule.getTeacherId().equals(teacherId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "SCHEDULE", "UNAUTHORIZED_SCHEDULE_ACCESS",
                    "teacher_not_authorized_for_schedule", scheduleId);
        }
        
        // Validate and convert status
        SessionStatus status;
        try {
            status = SessionStatus.valueOf(statusString.toUpperCase());
        } catch (IllegalArgumentException e) {
            throw new GeneralException(HttpStatus.BAD_REQUEST, "SCHEDULE", "INVALID_STATUS",
                    "invalid_session_status", statusString);
        }
        
        updateScheduleStatus(scheduleId, status, status != SessionStatus.CANCELLED);
        return enrichScheduleWithNames(mapper.toDto(scheduleDao.getById(scheduleId)));
    }
    
    /**
     * Cancel schedule
     */
    private void updateScheduleStatus(String scheduleId, SessionStatus status,boolean active) {
        ClassScheduleEntity schedule = scheduleDao.getById(scheduleId);

        schedule.setStatus(status);
        schedule.setActive(active);
        schedule.setUpdatedDate(System.currentTimeMillis());
        scheduleDao.update(schedule);
    }

    /**
     * Get schedules by teacher with pagination
     */
    public Page<ClassSchedule> getSchedulesByTeacherPaginated(String teacherId, Pageable pageable) {
        // For now, return all schedules and let Spring handle pagination
        List<ClassScheduleEntity> entities = scheduleDao.getByTeacherId(teacherId);
        List<ClassSchedule> schedules = entities.stream()
                .map(entity -> enrichScheduleWithNames(mapper.toDto(entity)))
                .collect(Collectors.toList());

        // Simple pagination implementation
        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), schedules.size());
        List<ClassSchedule> pageContent = schedules.subList(start, end);

        return new org.springframework.data.domain.PageImpl<>(pageContent, pageable, schedules.size());
    }

    /**
     * Get schedules by classroom with pagination
     */
    public Page<ClassSchedule> getSchedulesByClassroomPaginated(String classroomId, Pageable pageable) {
        List<ClassScheduleEntity> entities = scheduleDao.getByClassroomIdAndActiveTrue(classroomId);
        List<ClassSchedule> schedules = entities.stream()
                .map(entity -> enrichScheduleWithNames(mapper.toDto(entity)))
                .collect(Collectors.toList());

        // Simple pagination implementation
        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), schedules.size());
        List<ClassSchedule> pageContent = schedules.subList(start, end);

        return new org.springframework.data.domain.PageImpl<>(pageContent, pageable, schedules.size());
    }

    /**
     * Get upcoming schedules by teacher
     */
    public List<ClassSchedule> getUpcomingSchedulesByTeacher(String teacherId) {
        Instant now = Instant.now();
        List<ClassScheduleEntity> entities = scheduleDao.getUpcomingSchedulesByTeacher(teacherId, now);

        return entities.stream()
                .map(entity -> enrichScheduleWithNames(mapper.toDto(entity)))
                .collect(Collectors.toList());
    }

    /**
     * Get the next upcoming class for a teacher
     */
    public ClassSchedule getNextUpcomingClassByTeacher(String teacherId) {
        logger.info("Getting next upcoming class for teacher: {}", teacherId);

        Instant now = Instant.now();
        List<ClassScheduleEntity> entities = scheduleDao.getUpcomingSchedulesByTeacher(teacherId, now);

        if (entities.isEmpty()) {
            logger.info("No upcoming classes found for teacher: {}", teacherId);
            return null;
        }

        // Sort by start date/time to get the earliest upcoming class
        ClassScheduleEntity nextClass = entities.stream()
                .sorted((e1, e2) -> {
                    // Compare by start date first
                    int dateComparison = e1.getStartDateUtc().compareTo(e2.getStartDateUtc());
                    if (dateComparison != 0) {
                        return dateComparison;
                    }
                    // If same date, compare by session start time
                    return e1.getSessionStartTimeUtc().compareTo(e2.getSessionStartTimeUtc());
                })
                .findFirst()
                .orElse(null);

        if (nextClass != null) {
            ClassSchedule schedule = enrichScheduleWithNames(mapper.toDto(nextClass));
            logger.info("Next upcoming class for teacher {}: {} at {}", teacherId, schedule.getClassroomName(), schedule.getStartDate());
            return schedule;
        }

        return null;
    }

    /**
     * Get the next upcoming class for a student (based on enrollments)
     */
    public ClassSchedule getNextUpcomingClassByStudent(String studentId) {
        logger.info("Getting next upcoming class for student: {}", studentId);

        // Get student's enrolled classrooms
        List<Enrollment> enrollments = enrollmentService.getEnrollmentsByUserId(studentId);

        if (enrollments.isEmpty()) {
            logger.info("No enrollments found for student: {}", studentId);
            return null;
        }

        List<String> classroomIds = enrollments.stream()
                .filter(enrollment -> enrollment.getStatus() == EnrollmentStatus.ACTIVE)
                .map(Enrollment::getClassroomId)
                .collect(Collectors.toList());

        if (classroomIds.isEmpty()) {
            logger.info("No active enrollments found for student: {}", studentId);
            return null;
        }

        Instant now = Instant.now();
        List<ClassScheduleEntity> allUpcomingClasses = new ArrayList<>();

        // Get upcoming classes from all enrolled classrooms
        for (String classroomId : classroomIds) {
            List<ClassScheduleEntity> classroomSchedules = scheduleDao.getByClassroomIdAndActiveTrue(classroomId);

            // Filter for upcoming classes
            List<ClassScheduleEntity> upcomingSchedules = classroomSchedules.stream()
                    .filter(schedule -> schedule.isActive() &&
                                      schedule.getStartDateUtc().isAfter(now))
                    .collect(Collectors.toList());

            allUpcomingClasses.addAll(upcomingSchedules);
        }

        if (allUpcomingClasses.isEmpty()) {
            logger.info("No upcoming classes found for student: {}", studentId);
            return null;
        }

        // Sort by start date/time to get the earliest upcoming class
        ClassScheduleEntity nextClass = allUpcomingClasses.stream()
                .sorted((e1, e2) -> {
                    // Compare by start date first
                    int dateComparison = e1.getStartDateUtc().compareTo(e2.getStartDateUtc());
                    if (dateComparison != 0) {
                        return dateComparison;
                    }
                    // If same date, compare by session start time
                    return e1.getSessionStartTimeUtc().compareTo(e2.getSessionStartTimeUtc());
                })
                .findFirst()
                .orElse(null);

        if (nextClass != null) {
            ClassSchedule schedule = enrichScheduleWithNames(mapper.toDto(nextClass));
            logger.info("Next upcoming class for student {}: {} at {}", studentId, schedule.getClassroomName(), schedule.getStartDate());
            return schedule;
        }

        return null;
    }

    /**
     * Get today's schedules by teacher
     */
    public List<ClassSchedule> getTodaySchedulesByTeacher(String teacherId) {
        Instant startOfDay = LocalDate.now().atStartOfDay(java.time.ZoneOffset.UTC).toInstant();
        Instant endOfDay = startOfDay.plus(java.time.Duration.ofDays(1));
        List<ClassScheduleEntity> entities = scheduleDao.getTodaySchedulesByTeacher(teacherId, startOfDay, endOfDay);

        return entities.stream()
                .map(entity -> enrichScheduleWithNames(mapper.toDto(entity)))
                .collect(Collectors.toList());
    }

    /**
     * Delete schedule
     */
    public void deleteSchedule(String id) {
        scheduleDao.deleteById(id);
    }

    /**
     * Get schedule statistics for a classroom
     */
    public ScheduleStatistics getScheduleStatisticsByClassroom(String classroomId) {
        long totalSchedules = scheduleDao.countByClassroomId(classroomId);
        long pendingSchedules = scheduleDao.countPendingSchedulesByClassroom(classroomId);

        return new ScheduleStatistics()
                .setTotalSchedules((int) totalSchedules)
                .setPendingSchedules((int) pendingSchedules);
    }

    /**
     * Validate schedule request
     */
    private void validateScheduleRequest(ScheduleCreationRequest request, String teacherId) {
        // Validate time logic
        LocalTime startTime = LocalTime.parse(request.getSessionStartTime());
        LocalTime endTime = LocalTime.parse(request.getSessionEndTime());
        if (!startTime.isBefore(endTime)) {
            throw new GeneralException(HttpStatus.BAD_REQUEST, "SCHEDULE", "INVALID_TIME_RANGE",
                    "start_time_must_be_before_end_time");
        }

        // Validate that start date is not in the past
        LocalDate startDate = LocalDate.parse(request.getStartDate());
        if (startDate.isBefore(LocalDate.now())) {
            throw new GeneralException(HttpStatus.BAD_REQUEST, "SCHEDULE", "INVALID_START_DATE",
                    "start_date_must_be_today_or_future");
        }

        // Validate recurring schedule specific fields
        if (request.getRecurrenceType() == RecurrenceType.MULTIPLE) {
            if (request.getEndDate() == null) {
                throw new GeneralException(HttpStatus.BAD_REQUEST, "SCHEDULE", "MISSING_END_DATE",
                        "end_date_required_for_recurring_sessions");
            }
            if (request.getDaysOfWeek() == null || request.getDaysOfWeek().isEmpty()) {
                throw new GeneralException(HttpStatus.BAD_REQUEST, "SCHEDULE", "MISSING_DAYS_OF_WEEK",
                        "days_of_week_required_for_multiple_sessions");
            }
            LocalDate endDate = LocalDate.parse(request.getEndDate());
            if (!startDate.isBefore(endDate)) {
                throw new GeneralException(HttpStatus.BAD_REQUEST, "SCHEDULE", "INVALID_DATE_RANGE",
                        "start_date_must_be_before_end_date");
            }
        }

        // Validate single session doesn't have recurring fields
        if (request.getRecurrenceType() == RecurrenceType.SINGLE) {
            if (request.getEndDate() != null || (request.getDaysOfWeek() != null && !request.getDaysOfWeek().isEmpty())) {
                throw new GeneralException(HttpStatus.BAD_REQUEST, "SCHEDULE", "INVALID_SINGLE_SESSION",
                        "single_session_cannot_have_recurring_fields");
            }
        }
    }

    /**
     * Check for time conflicts using Instant
     */
    private void checkTimeConflicts(String teacherId, Instant startTime, Instant endTime, String excludeScheduleId) {
        // For now, we'll implement a simple overlap check
        List<ClassScheduleEntity> allSchedules = scheduleDao.getByTeacherId(teacherId);

        for (ClassScheduleEntity schedule : allSchedules) {
            if (excludeScheduleId != null && schedule.getId().equals(excludeScheduleId)) {
                continue;
            }

            if (schedule.getStatus() == SessionStatus.CANCELLED) {
                continue;
            }

            // Check for time overlap using UTC instants
            if (isTimeOverlap(startTime, endTime, schedule.getSessionStartTimeUtc(), schedule.getSessionEndTimeUtc())) {
                throw new GeneralException(HttpStatus.CONFLICT, "SCHEDULE", "TIME_CONFLICT",
                        "schedule_time_conflict_detected", startTime.toString(), endTime.toString());
            }
        }
    }

    /**
     * Check if two time periods overlap
     */
    private boolean isTimeOverlap(Instant start1, Instant end1, Instant start2, Instant end2) {
        return start1.isBefore(end2) && end1.isAfter(start2);
    }

    /**
     * Enrich schedule with classroom and teacher names
     */
    private ClassSchedule enrichScheduleWithNames(ClassSchedule schedule) {
        try {
            var classroom = classroomService.getClassroomById(schedule.getClassroomId());
            schedule.setClassroomName(classroom.getClassName());
            schedule.setTeacherName(classroom.getTeacherName());
            schedule.setPosterFileId(classroom.getPosterFileId());
        } catch (Exception e) {
            logger.warn("Failed to enrich schedule with names: {}", e.getMessage());
            schedule.setClassroomName("Unknown Classroom");
            schedule.setTeacherName("Unknown Teacher");
            schedule.setPosterFileId(null);
        }

        // Set string fields from UTC instants for display
        if (schedule.getStartDateUtc() != null) {
            schedule.setStartDate(DateTimeUtil.formatUtcInstantToDate(schedule.getStartDateUtc()));
        }
        if (schedule.getEndDateUtc() != null) {
            schedule.setEndDate(DateTimeUtil.formatUtcInstantToDate(schedule.getEndDateUtc()));
        }
        if (schedule.getSessionStartTimeUtc() != null) {
            schedule.setSessionStartTime(DateTimeUtil.formatUtcInstantToTime(schedule.getSessionStartTimeUtc()));
        }
        if (schedule.getSessionEndTimeUtc() != null) {
            schedule.setSessionEndTime(DateTimeUtil.formatUtcInstantToTime(schedule.getSessionEndTimeUtc()));
        }

        return schedule;
    }

    /**
     * Get top 5 upcoming schedules for teacher
     */
    public List<ClassSchedule> getTop5UpcomingSchedulesByTeacher(String teacherId) {
        logger.info("Getting top 5 upcoming schedules for teacher: {}", teacherId);

        Instant now = Instant.now();
        List<ClassScheduleEntity> entities = scheduleDao.getUpcomingSchedulesByTeacher(teacherId, now, 5);

        return entities.stream()
                .map(mapper::toDto)
                .map(this::enrichScheduleWithNames)
                .collect(Collectors.toList());
    }

    /**
     * Get top 5 upcoming schedules for student
     */
    public List<ClassSchedule> getTop5UpcomingSchedulesByStudent(String studentId) {
        logger.info("Getting top 5 upcoming schedules for student: {}", studentId);

        Instant now = Instant.now();
        List<ClassScheduleEntity> entities = scheduleDao.getUpcomingSchedulesByStudent(studentId, now, 5);

        return entities.stream()
                .map(mapper::toDto)
                .map(this::enrichScheduleWithNames)
                .collect(Collectors.toList());
    }

    /**
     * Get top 5 upcoming schedules for parent
     */
    public List<ClassSchedule> getTop5UpcomingSchedulesByParent(String parentId) {
        logger.info("Getting top 5 upcoming schedules for parent: {}", parentId);

        Instant now = Instant.now();
        List<ClassScheduleEntity> entities = scheduleDao.getUpcomingSchedulesByParent(parentId, now, 5);

        return entities.stream()
                .map(mapper::toDto)
                .map(this::enrichScheduleWithNames)
                .collect(Collectors.toList());
    }

    /**
     * Get schedule by ID with timezone-aware information
     */
    public TimezoneAwareSchedule getScheduleByIdWithTimezones(String scheduleId, List<String> requestedTimezones) {
        logger.info("Getting schedule {} with timezone information for timezones: {}", scheduleId, requestedTimezones);

        ClassScheduleEntity entity = scheduleDao.getById(scheduleId);
        ClassSchedule schedule = enrichScheduleWithNames(mapper.toDto(entity));

        // Create timezone-aware schedule
        TimezoneAwareSchedule timezoneAwareSchedule = new TimezoneAwareSchedule();

        // Copy basic information
        timezoneAwareSchedule.setId(schedule.getId());
        timezoneAwareSchedule.setClassroomId(schedule.getClassroomId());
        timezoneAwareSchedule.setTeacherId(schedule.getTeacherId());
        timezoneAwareSchedule.setSessionType(schedule.getSessionType());
        timezoneAwareSchedule.setMeetingLink(schedule.getMeetingLink());
        timezoneAwareSchedule.setStatus(schedule.getStatus());
        timezoneAwareSchedule.setRecurrenceType(schedule.getRecurrenceType());
        timezoneAwareSchedule.setDaysOfWeek(schedule.getDaysOfWeek());
        timezoneAwareSchedule.setParentScheduleId(schedule.getParentScheduleId());
        timezoneAwareSchedule.setCreatedDate(schedule.getCreatedDate());
        timezoneAwareSchedule.setUpdatedDate(schedule.getUpdatedDate());
        timezoneAwareSchedule.setActive(schedule.isActive());
        timezoneAwareSchedule.setClassroomName(schedule.getClassroomName());
        timezoneAwareSchedule.setTeacherName(schedule.getTeacherName());

        // Create UTC time information
        Instant startUtc = entity.getStartDateUtc();
        Instant endUtc = entity.getEndDateUtc();
        Instant sessionStartUtc = entity.getSessionStartTimeUtc();
        Instant sessionEndUtc = entity.getSessionEndTimeUtc();

        TimezoneAwareSchedule.UtcTimeInfo utcInfo = timezoneUtil.createUtcTimeInfo(
            startUtc, endUtc, sessionStartUtc, sessionEndUtc);
        timezoneAwareSchedule.setUtcTime(utcInfo);

        // Convert to requested timezones
        List<String> timezonesToConvert = requestedTimezones != null && !requestedTimezones.isEmpty()
            ? requestedTimezones
            : timezoneUtil.getCommonTimezones();

        Map<String, TimezoneAwareSchedule.TimezoneInfo> timezones =
            timezoneUtil.convertToMultipleTimezones(sessionStartUtc, sessionEndUtc, timezonesToConvert);
        timezoneAwareSchedule.setTimezones(timezones);

        // Set teacher timezone (assume first timezone or UTC)
        String teacherTimezoneId = timezonesToConvert.get(0);
        timezoneAwareSchedule.setTeacherTimezone(timezones.get(teacherTimezoneId));

        // Set student timezones (all requested timezones)
        List<TimezoneAwareSchedule.TimezoneInfo> studentTimezones = new ArrayList<>();
        for (String timezoneId : timezonesToConvert) {
            TimezoneAwareSchedule.TimezoneInfo timezoneInfo = timezones.get(timezoneId);
            if (timezoneInfo != null) {
                studentTimezones.add(timezoneInfo);
            }
        }
        timezoneAwareSchedule.setStudentTimezones(studentTimezones);

        logger.info("Successfully created timezone-aware schedule for {} timezones", timezones.size());
        return timezoneAwareSchedule;
    }
}
