package com.marglabs.trackmyclass.schedule.service;

import com.marglabs.trackmyclass.schedule.dao.ClassScheduleDao;
import com.marglabs.trackmyclass.schedule.entity.ClassScheduleEntity;
import com.marglabs.trackmyclass.schedule.model.SessionStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;

@Service
public class ScheduleStatusUpdateJob {
    
    private static final Logger logger = LoggerFactory.getLogger(ScheduleStatusUpdateJob.class);
    
    @Autowired
    private ClassScheduleDao scheduleDao;
    
    @Scheduled(fixedRate = 300000) // 5 minutes = 300,000 milliseconds
    @Transactional
    public void updateExpiredSchedules() {
        logger.info("Running schedule status update job");
        
        Instant now = Instant.now();
        List<ClassScheduleEntity> activeSchedules = scheduleDao.getActiveScheduledSessions();
        
        int updatedCount = 0;
        
        for (ClassScheduleEntity schedule : activeSchedules) {
            if (schedule.getSessionEndTimeUtc() != null && 
                schedule.getSessionEndTimeUtc().isBefore(now) &&
                schedule.getStatus() == SessionStatus.SCHEDULED) {
                
                schedule.setStatus(SessionStatus.COMPLETED);
                schedule.setUpdatedDate(System.currentTimeMillis());
                scheduleDao.update(schedule);
                updatedCount++;
                
                logger.debug("Updated schedule {} to COMPLETED", schedule.getId());
            }
        }
        
        logger.info("Schedule status update job completed. Updated {} schedules to COMPLETED", updatedCount);
    }
}