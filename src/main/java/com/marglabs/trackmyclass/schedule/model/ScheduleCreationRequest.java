package com.marglabs.trackmyclass.schedule.model;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

import java.util.List;

@Data
public class ScheduleCreationRequest {
    @NotBlank(message = "Classroom ID is required")
    private String classroomId;

    @NotNull(message = "Session type is required")
    private SessionType sessionType;

    @NotBlank(message = "Start date is required")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "Start date must be in format YYYY-MM-DD")
    private String startDate;

    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "End date must be in format YYYY-MM-DD")
    private String endDate; // Required only for recurring sessions

    @NotBlank(message = "Session start time is required")
    @Pattern(regexp = "^\\d{2}:\\d{2}$", message = "Session start time must be in format HH:MM")
    private String sessionStartTime;

    @NotBlank(message = "Session end time is required")
    @Pattern(regexp = "^\\d{2}:\\d{2}$", message = "Session end time must be in format HH:MM")
    private String sessionEndTime;

    @NotBlank(message = "Meeting link is required")
    private String meetingLink;

    @NotBlank(message = "Timezone is required")
    private String timezone; // User's timezone (e.g., "America/New_York", "Asia/Kolkata")

    private RecurrenceType recurrenceType; // SINGLE or MULTIPLE
    private List<DayOfWeek> daysOfWeek; // Required only for MULTIPLE recurrence type
}
