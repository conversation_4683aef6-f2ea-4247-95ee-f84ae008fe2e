package com.marglabs.trackmyclass.schedule.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.Instant;
import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
public class TimezoneAwareSchedule {
    
    // Basic schedule information
    private String id;
    private String classroomId;
    private String teacherId;
    private SessionType sessionType;
    private String meetingLink;
    private SessionStatus status;
    private RecurrenceType recurrenceType;
    private List<DayOfWeek> daysOfWeek;
    private String parentScheduleId;
    private long createdDate;
    private long updatedDate;
    private boolean active;
    
    // Transient fields
    private String classroomName;
    private String teacherName;
    
    // UTC time information (source of truth)
    private UtcTimeInfo utcTime;
    
    // Timezone-specific time information
    private Map<String, TimezoneInfo> timezones;
    
    // Common timezone shortcuts for quick access
    private TimezoneInfo teacherTimezone;
    private List<TimezoneInfo> studentTimezones;
    
    @Data
    @Accessors(chain = true)
    public static class UtcTimeInfo {
        private Instant startDateUtc;
        private Instant endDateUtc;
        private Instant sessionStartTimeUtc;
        private Instant sessionEndTimeUtc;
        private String startDateUtcString; // ISO format: 2024-07-17T14:30:00Z
        private String endDateUtcString;
        private String sessionStartTimeUtcString;
        private String sessionEndTimeUtcString;
    }
    
    @Data
    @Accessors(chain = true)
    public static class TimezoneInfo {
        private String timezoneId; // e.g., "America/New_York", "Asia/Kolkata"
        private String timezoneName; // e.g., "Eastern Standard Time", "India Standard Time"
        private String timezoneAbbreviation; // e.g., "EST", "IST"
        private int offsetMinutes; // Offset from UTC in minutes
        private String offsetString; // e.g., "+05:30", "-05:00"
        
        // Local date and time in this timezone
        private String startDate; // Format: YYYY-MM-DD
        private String endDate; // Format: YYYY-MM-DD
        private String sessionStartTime; // Format: HH:MM
        private String sessionEndTime; // Format: HH:MM
        
        // Full datetime strings in this timezone
        private String startDateTime; // Format: 2024-07-17T14:30:00+05:30
        private String endDateTime;
        private String sessionStartDateTime;
        private String sessionEndDateTime;
        
        // Human-readable format
        private String startDateFormatted; // e.g., "July 17, 2024"
        private String startTimeFormatted; // e.g., "2:30 PM"
        private String endTimeFormatted; // e.g., "3:30 PM"
        private String fullScheduleFormatted; // e.g., "July 17, 2024 at 2:30 PM - 3:30 PM EST"
    }
}
