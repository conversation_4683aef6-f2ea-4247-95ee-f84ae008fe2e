package com.marglabs.trackmyclass.schedule.model;

public enum DayOfWeek {
    MONDAY(1),
    TUESDAY(2),
    WEDNESDAY(3),
    THURSDAY(4),
    FRIDAY(5),
    SATURDAY(6),
    SUNDAY(7);
    
    private final int value;
    
    DayOfWeek(int value) {
        this.value = value;
    }
    
    public int getValue() {
        return value;
    }
    
    public static DayOfWeek fromJavaTime(java.time.DayOfWeek dayOfWeek) {
        switch (dayOfWeek) {
            case MONDAY: return MONDAY;
            case TUESDAY: return TUESDAY;
            case WEDNESDAY: return WEDNESDAY;
            case THURSDAY: return THURSDAY;
            case FRIDAY: return FRIDAY;
            case SATURDAY: return SATURDAY;
            case SUNDAY: return SUNDAY;
            default: throw new IllegalArgumentException("Unknown day of week: " + dayOfWeek);
        }
    }
    
    public java.time.DayOfWeek toJavaTime() {
        switch (this) {
            case MONDAY: return java.time.DayOfWeek.MONDAY;
            case TUESDAY: return java.time.DayOfWeek.TUESDAY;
            case WEDNESDAY: return java.time.DayOfWeek.WEDNESDAY;
            case THURSDAY: return java.time.DayOfWeek.THURSDAY;
            case FRIDAY: return java.time.DayOfWeek.FRIDAY;
            case SATURDAY: return java.time.DayOfWeek.SATURDAY;
            case SUNDAY: return java.time.DayOfWeek.SUNDAY;
            default: throw new IllegalArgumentException("Unknown day of week: " + this);
        }
    }
}
