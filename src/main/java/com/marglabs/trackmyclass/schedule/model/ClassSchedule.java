package com.marglabs.trackmyclass.schedule.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.Instant;
import java.util.List;

@Data
@Accessors(chain = true)
public class ClassSchedule {
    private String id;
    private String classroomId;
    private String teacherId;
    private SessionType sessionType;
    
    // Simple string fields for UI (easy to handle)
    private String startDate; // Format: YYYY-MM-DD
    private String endDate; // Format: YYYY-MM-DD (for recurring sessions)
    private String sessionStartTime; // Format: HH:MM
    private String sessionEndTime; // Format: HH:MM
    
    // UTC Instant fields for precise storage and queries (hidden from JSON)
    @JsonIgnore
    private Instant startDateUtc;
    @JsonIgnore
    private Instant endDateUtc;
    @JsonIgnore
    private Instant sessionStartTimeUtc;
    @JsonIgnore
    private Instant sessionEndTimeUtc;
    
    private String meetingLink;
    private SessionStatus status;
    private RecurrenceType recurrenceType; // SINGLE or MULTIPLE
    private List<DayOfWeek> daysOfWeek; // Only for MULTIPLE recurrence type
    private String parentScheduleId; // For individual sessions generated from recurring schedule
    private long createdDate;
    private long updatedDate;
    private boolean active;
    
    // Transient fields
    private String classroomName;
    private String teacherName;
    private String posterFileId;
}
