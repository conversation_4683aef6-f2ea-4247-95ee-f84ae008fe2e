package com.marglabs.trackmyclass.schedule.model;

import jakarta.validation.constraints.Pattern;
import lombok.Data;

import java.util.List;

@Data
public class ScheduleUpdateRequest {
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "Start date must be in format YYYY-MM-DD")
    private String startDate;

    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "End date must be in format YYYY-MM-DD")
    private String endDate;

    @Pattern(regexp = "^\\d{2}:\\d{2}$", message = "Session start time must be in format HH:MM")
    private String sessionStartTime;

    @Pattern(regexp = "^\\d{2}:\\d{2}$", message = "Session end time must be in format HH:MM")
    private String sessionEndTime;

    private String meetingLink;

    private String timezone; // User's timezone for time conversion

    private List<DayOfWeek> daysOfWeek;
}
