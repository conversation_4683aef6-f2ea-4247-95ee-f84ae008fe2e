package com.marglabs.trackmyclass.schedule.mapper;

import com.marglabs.trackmyclass.schedule.entity.ClassScheduleEntity;
import com.marglabs.trackmyclass.schedule.model.ClassSchedule;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface ClassScheduleMapper {

    @Mapping(target = "classroomName", ignore = true)
    @Mapping(target = "teacherName", ignore = true)
    @Mapping(target = "startDate", ignore = true)
    @Mapping(target = "endDate", ignore = true)
    @Mapping(target = "sessionStartTime", ignore = true)
    @Mapping(target = "sessionEndTime", ignore = true)
    ClassSchedule toDto(ClassScheduleEntity entity);

    @Mapping(target = "startDateUtc", ignore = true)
    @Mapping(target = "endDateUtc", ignore = true)
    @Mapping(target = "sessionStartTimeUtc", ignore = true)
    @Mapping(target = "sessionEndTimeUtc", ignore = true)
    ClassScheduleEntity toEntity(ClassSchedule schedule);
}
