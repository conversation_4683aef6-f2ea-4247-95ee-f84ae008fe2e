package com.marglabs.trackmyclass.schedule.dao;

import com.marglabs.common.rest.error.EntityNotFoundException;
import com.marglabs.trackmyclass.schedule.entity.ClassScheduleEntity;
import com.marglabs.trackmyclass.schedule.model.SessionStatus;
import com.marglabs.trackmyclass.schedule.repository.ClassScheduleRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;
import java.util.Optional;

@Component
public class ClassScheduleDao {
    private static final Logger logger = LoggerFactory.getLogger(ClassScheduleDao.class);

    @Autowired
    private ClassScheduleRepository repository;

    public ClassScheduleEntity create(ClassScheduleEntity entity) {
        long now = Instant.now().toEpochMilli();
        entity.setCreatedDate(now);
        entity.setUpdatedDate(now);
        entity.setActive(true);
        if (entity.getStatus() == null) {
            entity.setStatus(SessionStatus.SCHEDULED);
        }
        return repository.save(entity);
    }

    public ClassScheduleEntity update(ClassScheduleEntity entity) {
        ClassScheduleEntity existingEntity = getById(entity.getId());
        entity.setCreatedDate(existingEntity.getCreatedDate());
        entity.setUpdatedDate(Instant.now().toEpochMilli());
        return repository.save(entity);
    }

    public ClassScheduleEntity getById(String id) {
        Optional<ClassScheduleEntity> entity = repository.findById(id);
        if (entity.isPresent()) {
            return entity.get();
        }
        throw new EntityNotFoundException(HttpStatus.NOT_FOUND, "SCHEDULE", "SCHEDULE_NOT_FOUND",
                "schedule_not_found_details", id);
    }

    public Page<ClassScheduleEntity> getByClassroomIdPaginated(String classroomId, Pageable pageable) {
        return repository.findByClassroomIdAndActiveTrue(classroomId, pageable);
    }

    public List<ClassScheduleEntity> getByClassroomIdAndActiveTrue(String classroomId) {
        return repository.findByClassroomIdAndActiveTrue(classroomId);
    }

    public Page<ClassScheduleEntity> getByTeacherIdPaginated(String teacherId, Pageable pageable) {
        return repository.findByTeacherIdAndActiveTrue(teacherId, pageable);
    }

    public List<ClassScheduleEntity> getByTeacherId(String teacherId) {
        return repository.findByTeacherIdAndActiveTrue(teacherId);
    }

    public List<ClassScheduleEntity> getByTeacherIdAndDateRange(String teacherId, Instant startDateUtc, Instant endDateUtc) {
        return repository.findByTeacherIdAndDateRange(teacherId, startDateUtc, endDateUtc);
    }

    public List<ClassScheduleEntity> findConflictingSchedules(String teacherId, Instant startTimeUtc, Instant endTimeUtc) {
        return repository.findConflictingSchedules(teacherId, startTimeUtc, endTimeUtc);
    }

    public List<ClassScheduleEntity> findConflictingSchedulesExcluding(String teacherId, Instant startTimeUtc,
                                                                       Instant endTimeUtc, String excludeId) {
        return repository.findConflictingSchedulesExcluding(teacherId, startTimeUtc, endTimeUtc, excludeId);
    }

    public List<ClassScheduleEntity> getByParentScheduleId(String parentScheduleId) {
        return repository.findByParentScheduleIdAndActiveTrue(parentScheduleId);
    }

    public List<ClassScheduleEntity> getUpcomingSchedulesByTeacher(String teacherId, Instant currentTimeUtc) {
        return repository.findUpcomingSchedulesByTeacher(teacherId, currentTimeUtc);
    }

    public List<ClassScheduleEntity> getTodaySchedulesByTeacher(String teacherId, Instant startOfDayUtc, Instant endOfDayUtc) {
        return repository.findTodaySchedulesByTeacher(teacherId, startOfDayUtc, endOfDayUtc);
    }

    public void deleteById(String id) {
        try {
            repository.deleteById(id);
        } catch (EmptyResultDataAccessException e) {
            throw new EntityNotFoundException(HttpStatus.NOT_FOUND, "SCHEDULE", "SCHEDULE_NOT_FOUND",
                    "schedule_not_found_details", id);
        }
    }

    public ClassScheduleEntity cancelSchedule(String id) {
        ClassScheduleEntity entity = getById(id);
        entity.setStatus(SessionStatus.CANCELLED);
        entity.setUpdatedDate(Instant.now().toEpochMilli());
        return repository.save(entity);
    }

    public List<ClassScheduleEntity> getAll() {
        return repository.findAll();
    }

    public long countByClassroomId(String classroomId) {
        return repository.countByClassroomIdAndActiveTrue(classroomId);
    }

    public long countPendingSchedulesByClassroom(String classroomId) {
        return repository.countPendingSchedulesByClassroom(classroomId);
    }

    /**
     * Get upcoming schedules for teacher (top N by most recent date)
     */
    public List<ClassScheduleEntity> getUpcomingSchedulesByTeacher(String teacherId, Instant currentTime, int limit) {
        logger.info("Getting top {} upcoming schedules for teacher: {}", limit, teacherId);
        return repository.findUpcomingSchedulesByTeacher(teacherId, currentTime,
                org.springframework.data.domain.PageRequest.of(0, limit));
    }

    /**
     * Get upcoming schedules for student (top N by most recent date)
     */
    public List<ClassScheduleEntity> getUpcomingSchedulesByStudent(String studentId, Instant currentTime, int limit) {
        logger.info("Getting top {} upcoming schedules for student: {}", limit, studentId);
        return repository.findUpcomingSchedulesByStudent(studentId, currentTime,
                org.springframework.data.domain.PageRequest.of(0, limit));
    }

    /**
     * Get upcoming schedules for parent (top N by most recent date)
     */
    public List<ClassScheduleEntity> getUpcomingSchedulesByParent(String parentId, Instant currentTime, int limit) {
        logger.info("Getting top {} upcoming schedules for parent: {}", limit, parentId);
        return repository.findUpcomingSchedulesByParent(parentId, currentTime,
                org.springframework.data.domain.PageRequest.of(0, limit));
    }
    
    public List<ClassScheduleEntity> getActiveScheduledSessions() {
        return repository.findByActiveTrueAndStatus(SessionStatus.SCHEDULED);
    }
}
