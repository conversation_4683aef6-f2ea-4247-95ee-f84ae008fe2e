package com.marglabs.trackmyclass.schedule.util;

import com.marglabs.trackmyclass.schedule.model.TimezoneAwareSchedule;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.TextStyle;
import java.util.*;

@Component
public class TimezoneUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(TimezoneUtil.class);
    
    // Common timezone IDs for educational institutions
    private static final List<String> COMMON_TIMEZONES = Arrays.asList(
        "UTC",
        "America/New_York",    // Eastern Time
        "America/Chicago",     // Central Time
        "America/Denver",      // Mountain Time
        "America/Los_Angeles", // Pacific Time
        "Europe/London",       // GMT/BST
        "Europe/Paris",        // CET/CEST
        "Asia/Kolkata",        // IST
        "Asia/Shanghai",       // CST
        "Asia/Tokyo",          // JST
        "Australia/Sydney",    // AEST/AEDT
        "Asia/Dubai",          // GST
        "America/Toronto",     // Eastern Time (Canada)
        "Europe/Berlin",       // CET/CEST
        "Asia/Singapore"       // SGT
    );
    
    /**
     * Convert UTC time to multiple timezones
     */
    public Map<String, TimezoneAwareSchedule.TimezoneInfo> convertToMultipleTimezones(
            Instant startUtc, Instant endUtc, List<String> timezoneIds) {
        
        Map<String, TimezoneAwareSchedule.TimezoneInfo> timezones = new HashMap<>();
        
        for (String timezoneId : timezoneIds) {
            try {
                TimezoneAwareSchedule.TimezoneInfo timezoneInfo = convertToTimezone(startUtc, endUtc, timezoneId);
                timezones.put(timezoneId, timezoneInfo);
            } catch (Exception e) {
                logger.warn("Failed to convert time to timezone {}: {}", timezoneId, e.getMessage());
            }
        }
        
        return timezones;
    }
    
    /**
     * Convert UTC time to a specific timezone
     */
    public TimezoneAwareSchedule.TimezoneInfo convertToTimezone(Instant startUtc, Instant endUtc, String timezoneId) {
        ZoneId zoneId = ZoneId.of(timezoneId);
        ZonedDateTime startLocal = startUtc.atZone(zoneId);
        ZonedDateTime endLocal = endUtc.atZone(zoneId);
        
        TimezoneAwareSchedule.TimezoneInfo timezoneInfo = new TimezoneAwareSchedule.TimezoneInfo();
        
        // Basic timezone information
        timezoneInfo.setTimezoneId(timezoneId);
        timezoneInfo.setTimezoneName(zoneId.getDisplayName(TextStyle.FULL, Locale.ENGLISH));
        timezoneInfo.setTimezoneAbbreviation(startLocal.getZone().getDisplayName(TextStyle.SHORT, Locale.ENGLISH));
        
        // Offset information
        ZoneOffset offset = startLocal.getOffset();
        timezoneInfo.setOffsetMinutes(offset.getTotalSeconds() / 60);
        timezoneInfo.setOffsetString(offset.toString());
        
        // Date and time components
        timezoneInfo.setStartDate(startLocal.toLocalDate().toString());
        timezoneInfo.setEndDate(endLocal.toLocalDate().toString());
        timezoneInfo.setSessionStartTime(startLocal.toLocalTime().format(DateTimeFormatter.ofPattern("HH:mm")));
        timezoneInfo.setSessionEndTime(endLocal.toLocalTime().format(DateTimeFormatter.ofPattern("HH:mm")));
        
        // Full datetime strings
        timezoneInfo.setStartDateTime(startLocal.toString());
        timezoneInfo.setEndDateTime(endLocal.toString());
        timezoneInfo.setSessionStartDateTime(startLocal.toString());
        timezoneInfo.setSessionEndDateTime(endLocal.toString());
        
        // Human-readable formats
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("MMMM d, yyyy");
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("h:mm a");
        
        timezoneInfo.setStartDateFormatted(startLocal.format(dateFormatter));
        timezoneInfo.setStartTimeFormatted(startLocal.format(timeFormatter));
        timezoneInfo.setEndTimeFormatted(endLocal.format(timeFormatter));
        
        // Full schedule format
        String fullSchedule = String.format("%s at %s - %s %s",
            timezoneInfo.getStartDateFormatted(),
            timezoneInfo.getStartTimeFormatted(),
            timezoneInfo.getEndTimeFormatted(),
            timezoneInfo.getTimezoneAbbreviation()
        );
        timezoneInfo.setFullScheduleFormatted(fullSchedule);
        
        return timezoneInfo;
    }
    
    /**
     * Get common timezones for educational institutions
     */
    public List<String> getCommonTimezones() {
        return new ArrayList<>(COMMON_TIMEZONES);
    }
    
    /**
     * Get timezone information for a list of timezone IDs
     */
    public List<TimezoneAwareSchedule.TimezoneInfo> getTimezoneInfoList(
            Instant startUtc, Instant endUtc, List<String> timezoneIds) {
        
        List<TimezoneAwareSchedule.TimezoneInfo> timezoneInfos = new ArrayList<>();
        
        for (String timezoneId : timezoneIds) {
            try {
                TimezoneAwareSchedule.TimezoneInfo info = convertToTimezone(startUtc, endUtc, timezoneId);
                timezoneInfos.add(info);
            } catch (Exception e) {
                logger.warn("Failed to get timezone info for {}: {}", timezoneId, e.getMessage());
            }
        }
        
        return timezoneInfos;
    }
    
    /**
     * Create UTC time information
     */
    public TimezoneAwareSchedule.UtcTimeInfo createUtcTimeInfo(Instant startUtc, Instant endUtc, 
                                                               Instant sessionStartUtc, Instant sessionEndUtc) {
        TimezoneAwareSchedule.UtcTimeInfo utcInfo = new TimezoneAwareSchedule.UtcTimeInfo();
        
        utcInfo.setStartDateUtc(startUtc);
        utcInfo.setEndDateUtc(endUtc);
        utcInfo.setSessionStartTimeUtc(sessionStartUtc);
        utcInfo.setSessionEndTimeUtc(sessionEndUtc);
        
        // ISO format strings
        utcInfo.setStartDateUtcString(startUtc.toString());
        utcInfo.setEndDateUtcString(endUtc.toString());
        utcInfo.setSessionStartTimeUtcString(sessionStartUtc.toString());
        utcInfo.setSessionEndTimeUtcString(sessionEndUtc.toString());
        
        return utcInfo;
    }
    
    /**
     * Detect timezone from user location or preferences
     */
    public String detectUserTimezone(String userLocation, String userPreference) {
        // Priority: user preference > user location > system default
        
        if (userPreference != null && isValidTimezone(userPreference)) {
            return userPreference;
        }
        
        if (userLocation != null) {
            String detectedTimezone = detectTimezoneFromLocation(userLocation);
            if (detectedTimezone != null) {
                return detectedTimezone;
            }
        }
        
        // Default to system timezone
        return ZoneId.systemDefault().getId();
    }
    
    /**
     * Validate if timezone ID is valid
     */
    public boolean isValidTimezone(String timezoneId) {
        try {
            ZoneId.of(timezoneId);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * Detect timezone from location (simplified implementation)
     */
    private String detectTimezoneFromLocation(String location) {
        // This is a simplified implementation
        // In production, you might use a geolocation service or database
        
        String locationLower = location.toLowerCase();
        
        if (locationLower.contains("new york") || locationLower.contains("eastern")) {
            return "America/New_York";
        } else if (locationLower.contains("chicago") || locationLower.contains("central")) {
            return "America/Chicago";
        } else if (locationLower.contains("denver") || locationLower.contains("mountain")) {
            return "America/Denver";
        } else if (locationLower.contains("los angeles") || locationLower.contains("pacific")) {
            return "America/Los_Angeles";
        } else if (locationLower.contains("london") || locationLower.contains("uk")) {
            return "Europe/London";
        } else if (locationLower.contains("india") || locationLower.contains("mumbai") || locationLower.contains("delhi")) {
            return "Asia/Kolkata";
        } else if (locationLower.contains("china") || locationLower.contains("beijing")) {
            return "Asia/Shanghai";
        } else if (locationLower.contains("japan") || locationLower.contains("tokyo")) {
            return "Asia/Tokyo";
        } else if (locationLower.contains("australia") || locationLower.contains("sydney")) {
            return "Australia/Sydney";
        } else if (locationLower.contains("dubai") || locationLower.contains("uae")) {
            return "Asia/Dubai";
        }
        
        return null;
    }
    
    /**
     * Get timezone display name for UI
     */
    public String getTimezoneDisplayName(String timezoneId) {
        try {
            ZoneId zoneId = ZoneId.of(timezoneId);
            ZonedDateTime now = ZonedDateTime.now(zoneId);
            String displayName = zoneId.getDisplayName(TextStyle.FULL, Locale.ENGLISH);
            String abbreviation = now.getZone().getDisplayName(TextStyle.SHORT, Locale.ENGLISH);
            String offset = now.getOffset().toString();
            
            return String.format("%s (%s, %s)", displayName, abbreviation, offset);
        } catch (Exception e) {
            return timezoneId;
        }
    }
}
