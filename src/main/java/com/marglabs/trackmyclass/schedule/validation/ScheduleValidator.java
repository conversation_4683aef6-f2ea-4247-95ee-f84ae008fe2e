package com.marglabs.trackmyclass.schedule.validation;

import com.marglabs.trackmyclass.schedule.model.RecurrenceType;
import com.marglabs.trackmyclass.schedule.model.ScheduleCreationRequest;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;

@Component
public class ScheduleValidator {

    public List<String> validateScheduleCreationRequest(ScheduleCreationRequest request) {
        List<String> errors = new ArrayList<>();

        // Validate time logic
        if (request.getSessionStartTime() != null && request.getSessionEndTime() != null) {
            try {
                LocalTime startTime = LocalTime.parse(request.getSessionStartTime());
                LocalTime endTime = LocalTime.parse(request.getSessionEndTime());
                
                if (!startTime.isBefore(endTime)) {
                    errors.add("Start time must be before end time");
                }
                
                // Validate session duration
                Duration duration = Duration.between(startTime, endTime);
                if (duration.toMinutes() < 15) {
                    errors.add("Session must be at least 15 minutes long");
                }
                if (duration.toHours() > 8) {
                    errors.add("Session cannot be longer than 8 hours");
                }
            } catch (Exception e) {
                errors.add("Invalid time format. Use HH:MM format");
            }
        }

        // Date validations
        if (request.getStartDate() != null) {
            try {
                LocalDate startDate = LocalDate.parse(request.getStartDate());
                if (startDate.isBefore(LocalDate.now())) {
                    errors.add("Start date cannot be in the past");
                }
            } catch (Exception e) {
                errors.add("Invalid start date format. Use YYYY-MM-DD format");
            }
        }

        // Recurring schedule validations
        if (request.getRecurrenceType() == RecurrenceType.MULTIPLE) {
            if (request.getEndDate() == null || request.getEndDate().trim().isEmpty()) {
                errors.add("End date is required for recurring schedules");
            } else {
                try {
                    LocalDate startDate = LocalDate.parse(request.getStartDate());
                    LocalDate endDate = LocalDate.parse(request.getEndDate());
                    
                    if (!endDate.isAfter(startDate)) {
                        errors.add("End date must be after start date");
                    }
                    
                    // Check if the date range is reasonable (not more than 1 year)
                    if (Duration.between(startDate.atStartOfDay(), endDate.atStartOfDay()).toDays() > 365) {
                        errors.add("Recurring schedule cannot span more than 1 year");
                    }
                } catch (Exception e) {
                    errors.add("Invalid end date format. Use YYYY-MM-DD format");
                }
            }

            if (request.getDaysOfWeek() == null || request.getDaysOfWeek().isEmpty()) {
                errors.add("Days of week are required for recurring schedules");
            }
        }

        // Single session validations
        if (request.getRecurrenceType() == RecurrenceType.SINGLE) {
            if (request.getEndDate() != null && !request.getEndDate().trim().isEmpty()) {
                errors.add("End date should not be provided for single sessions");
            }
            if (request.getDaysOfWeek() != null && !request.getDaysOfWeek().isEmpty()) {
                errors.add("Days of week should not be provided for single sessions");
            }
        }

        // Meeting link validation
        if (request.getMeetingLink() != null) {
            if (request.getMeetingLink().length() > 500) {
                errors.add("Meeting link cannot exceed 500 characters");
            }
            // Basic URL validation
            if (!request.getMeetingLink().startsWith("http://") && 
                !request.getMeetingLink().startsWith("https://")) {
                errors.add("Meeting link must be a valid URL starting with http:// or https://");
            }
        }

        return errors;
    }
}
