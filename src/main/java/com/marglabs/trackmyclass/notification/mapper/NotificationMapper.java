package com.marglabs.trackmyclass.notification.mapper;

import com.marglabs.trackmyclass.notification.entity.NotificationEntity;
import com.marglabs.trackmyclass.notification.model.Notification;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Mapper(componentModel = "spring")
public interface NotificationMapper {

    @Mapping(target = "createdDate", expression = "java(entity.getCreatedDate() != null ? entity.getCreatedDate().toEpochMilli() : 0)")
    @Mapping(target = "updatedDate", expression = "java(entity.getUpdatedDate() != null ? entity.getUpdatedDate().toEpochMilli() : 0)")
    @Mapping(target = "attachedFileIds", expression = "java(stringToList(entity.getAttachedFileIds()))")
    @Mapping(target = "attachedFiles", ignore = true) // Will be populated by service
    Notification toDto(NotificationEntity entity);

    @Mapping(target = "createdDate", ignore = true)
    @Mapping(target = "updatedDate", ignore = true)
    @Mapping(target = "attachedFileIds", expression = "java(listToString(dto.getAttachedFileIds()))")
    NotificationEntity toEntity(Notification dto);

    /**
     * Convert comma-separated string to list
     */
    default List<String> stringToList(String str) {
        if (!StringUtils.hasText(str)) {
            return Collections.emptyList();
        }
        return Arrays.asList(str.split(","));
    }

    /**
     * Convert list to comma-separated string
     */
    default String listToString(List<String> list) {
        if (list == null || list.isEmpty()) {
            return null;
        }
        return String.join(",", list);
    }
}