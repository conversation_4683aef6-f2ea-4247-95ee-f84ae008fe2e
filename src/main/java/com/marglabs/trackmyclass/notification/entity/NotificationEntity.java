package com.marglabs.trackmyclass.notification.entity;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.Instant;

@Entity
@Table(name = "notifications")
@Data
public class NotificationEntity {
    @Id
    private String id;

    @Column(name = "classroomid")
    private String classroomId;

    @Column(name = "userid")
    private String userId;

    @Column
    private String title;

    @Column(length = 2000)
    private String content;

    @Column(name = "attached_file_ids", length = 2000)
    private String attachedFileIds; // Comma-separated file IDs

    @CreationTimestamp
    @Column(name = "createddate", nullable = false, updatable = false)
    private Instant createdDate;

    @UpdateTimestamp
    @Column(name = "updateddate", nullable = false)
    private Instant updatedDate;
}