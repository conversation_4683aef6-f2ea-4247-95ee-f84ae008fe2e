package com.marglabs.trackmyclass.notification.service;

import com.marglabs.common.utils.IdGenerator;
import com.marglabs.trackmyclass.notification.dao.NotificationDao;
import com.marglabs.trackmyclass.notification.entity.NotificationEntity;
import com.marglabs.trackmyclass.notification.mapper.NotificationMapper;
import com.marglabs.trackmyclass.notification.model.*;
import com.marglabs.trackmyclass.classroom.service.ClassroomService;
import com.marglabs.trackmyclass.classroom.model.ClassroomSummary;
import com.marglabs.trackmyclass.enrollment.model.Enrollment;
import com.marglabs.trackmyclass.enrollment.service.EnrollmentService;
import com.marglabs.trackmyclass.file.model.FileInfo;
import com.marglabs.trackmyclass.file.service.FileService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class NotificationService {

    private static final Logger logger = LoggerFactory.getLogger(NotificationService.class);

    @Autowired
    private NotificationDao notificationDao;

    @Autowired
    private NotificationMapper mapper;

    @Autowired
    private IdGenerator idGenerator;

    @Autowired
    private ClassroomService classroomService;

    @Autowired
    private EnrollmentService enrollmentService;

    @Autowired
    private FileService fileService;

    public Notification createNotification(Notification notification) {
        if (notification.getUserId() == null) {
            throw new IllegalArgumentException("User ID cannot be null");
        }
        notification.setId(idGenerator.generateId());
        NotificationEntity entity = mapper.toEntity(notification);
        entity = notificationDao.create(entity);
        return mapper.toDto(entity);
    }

    /**
     * Create notification from NotificationRequest
     */
    public Notification createNotificationFromRequest(NotificationRequest request, String classroomId, String teacherId) {
        logger.info("Creating notification for classroom {} by teacher {} with title: {}",
                classroomId, teacherId, request.getTitle());

        // Validate attached files if provided
        List<String> validatedFileIds = validateAndFilterFileIds(request.getAttachedFileIds(), teacherId);

        // Create notification entity
        NotificationEntity notificationEntity = new NotificationEntity();
        notificationEntity.setId(idGenerator.generateId());
        notificationEntity.setUserId(teacherId);
        notificationEntity.setClassroomId(classroomId);
        notificationEntity.setTitle(request.getTitle());
        notificationEntity.setContent(request.getContent());

        // Set file attachments
        if (validatedFileIds != null && !validatedFileIds.isEmpty()) {
            notificationEntity.setAttachedFileIds(String.join(",", validatedFileIds));
        }

        // createdDate and updatedDate are set automatically by @CreationTimestamp and @UpdateTimestamp

        // Save notification
        NotificationEntity savedEntity = notificationDao.create(notificationEntity);

        logger.info("Successfully created notification {} for classroom {} with {} file attachments",
                savedEntity.getId(), classroomId, validatedFileIds.size());

        // Convert to DTO and enrich with file information
        Notification notification = mapper.toDto(savedEntity);
        return enrichNotificationWithFiles(notification);
    }

    public Notification getNotification(String id) {
        return mapper.toDto(notificationDao.getById(id));
    }

    public List<Notification> getNotificationsByClassroom(String classroomId) {
        return notificationDao.getByClassroomId(classroomId).stream()
                .map(mapper::toDto)
                .collect(Collectors.toList());
    }

    public List<Notification> getNotificationsByUser(String userId) {
        List<NotificationEntity> byUserId = notificationDao.getByUserId(userId);
        return byUserId.stream()
                .map(mapper::toDto)
                .collect(Collectors.toList());
    }

    /**
     * Get all notifications for classes a user is enrolled in
     * Includes both specific classroom notifications and bulk notifications (classroomId "100")
     */
    public List<Notification> getNotificationsForUserEnrollments(String userId, List<String> classroomIds) {
        // If no classrooms, return empty list
        if (classroomIds == null || classroomIds.isEmpty()) {
            return List.of();
        }

        List<Notification> allNotifications = new ArrayList<>();

        // Get notifications for all classrooms the user is enrolled in
        for (String classroomId : classroomIds) {
            allNotifications.addAll(getNotificationsByClassroom(classroomId));
        }

        // Also get bulk notifications (classroomId "100") for all teachers of enrolled classrooms
        List<Notification> bulkNotifications = getBulkNotificationsForUserClassrooms(userId, classroomIds);
        allNotifications.addAll(bulkNotifications);

        // Remove duplicates based on ID and sort by creation date (most recent first)
        return allNotifications.stream()
                .collect(Collectors.toMap(
                    Notification::getId, // Key: notification ID
                    notification -> notification, // Value: notification
                    (existing, replacement) -> existing)) // Keep existing if duplicate ID found
                .values()
                .stream()
                .sorted((n1, n2) -> Long.compare(n2.getCreatedDate(), n1.getCreatedDate()))
                .collect(Collectors.toList());
    }

    public Notification updateNotification(String id, NotificationUpdateRequest updateRequest, String userId) {
        logger.info("Updating notification: {} by user: {}", id, userId);

        // Get existing notification
        NotificationEntity existingEntity = notificationDao.getById(id);

        // Update fields if provided
        if (updateRequest.getTitle() != null) {
            existingEntity.setTitle(updateRequest.getTitle());
        }
        if (updateRequest.getContent() != null) {
            existingEntity.setContent(updateRequest.getContent());
        }
        if (updateRequest.getAttachedFileIds() != null) {
            // Validate attached files if provided
            List<String> validatedFileIds = validateAndFilterFileIds(updateRequest.getAttachedFileIds(), userId);
            existingEntity.setAttachedFileIds(validatedFileIds.isEmpty() ? null : String.join(",", validatedFileIds));
        }

        // Save updated entity
        NotificationEntity updatedEntity = notificationDao.update(existingEntity);

        // Convert to DTO and enrich with file information
        Notification notification = mapper.toDto(updatedEntity);
        return enrichNotificationWithFiles(notification);
    }

    public void deleteNotification(String id) {
        notificationDao.deleteById(id);
    }

    /**
     * Get top 5 notifications for user's classrooms
     * Includes both specific classroom notifications and bulk notifications (classroomId "100")
     */
    public List<Notification> getTop5NotificationsForUser(String userId, List<String> classroomIds) {
        if (classroomIds == null || classroomIds.isEmpty()) {
            return List.of();
        }

        List<NotificationEntity> allNotifications = new ArrayList<>();

        // Get notifications for specific classrooms
        List<NotificationEntity> specificNotifications = notificationDao.getTop5ByClassroomIds(classroomIds);
        allNotifications.addAll(specificNotifications);

        // Get bulk notifications (classroomId "100") for teachers of enrolled classrooms
        List<NotificationEntity> bulkNotifications = getBulkNotificationsForUserClassroomsEntities(userId, classroomIds);
        allNotifications.addAll(bulkNotifications);

        // Remove duplicates based on ID, sort by creation date (most recent first), and limit to top 5
        return allNotifications.stream()
                .collect(Collectors.toMap(
                    NotificationEntity::getId, // Key: notification ID
                    notification -> notification, // Value: notification entity
                    (existing, replacement) -> existing)) // Keep existing if duplicate ID found
                .values()
                .stream()
                .sorted((n1, n2) -> n2.getCreatedDate().compareTo(n1.getCreatedDate())) // Most recent first
                .limit(5) // Top 5 notifications
                .map(this::mapToNotificationWithTruncatedContent)
                .collect(Collectors.toList());
    }

    /**
     * Get top 5 notifications for a teacher
     * Includes notifications from their own classrooms and bulk notifications they created
     */
    public List<Notification> getTop5NotificationsForTeacher(String teacherId) {
        logger.info("Getting top 5 notifications for teacher: {}", teacherId);

        try {
            List<NotificationEntity> allNotifications = new ArrayList<>();

            // Get notifications created by the teacher (both specific classroom and bulk notifications)
            List<NotificationEntity> teacherNotifications = notificationDao.getByUserId(teacherId);
            allNotifications.addAll(teacherNotifications);

            // Get notifications from other teachers for classrooms where this teacher is enrolled
            // (in case a teacher is also enrolled as a student/parent in other classrooms)
            List<Enrollment> enrollments = enrollmentService.getEnrollmentsByUserId(teacherId);
            if (!enrollments.isEmpty()) {
                List<String> enrolledClassroomIds = enrollments.stream()
                        .map(Enrollment::getClassroomId)
                        .collect(Collectors.toList());

                // Get notifications for enrolled classrooms (excluding own notifications to avoid duplicates)
                List<NotificationEntity> enrolledClassroomNotifications = notificationDao.getTop5ByClassroomIds(enrolledClassroomIds)
                        .stream()
                        .filter(notification -> !notification.getUserId().equals(teacherId))
                        .collect(Collectors.toList());
                allNotifications.addAll(enrolledClassroomNotifications);

                // Get bulk notifications from teachers of enrolled classrooms (excluding own)
                List<NotificationEntity> bulkNotifications = getBulkNotificationsForUserClassroomsEntities(teacherId, enrolledClassroomIds)
                        .stream()
                        .filter(notification -> !notification.getUserId().equals(teacherId))
                        .collect(Collectors.toList());
                allNotifications.addAll(bulkNotifications);
            }

            // Remove duplicates based on ID, sort by creation date (most recent first), and limit to top 5
            return allNotifications.stream()
                    .collect(Collectors.toMap(
                        NotificationEntity::getId, // Key: notification ID
                        notification -> notification, // Value: notification entity
                        (existing, replacement) -> existing)) // Keep existing if duplicate ID found
                    .values()
                    .stream()
                    .sorted((n1, n2) -> n2.getCreatedDate().compareTo(n1.getCreatedDate())) // Most recent first
                    .limit(5) // Top 5 notifications
                    .map(this::mapToNotificationWithTruncatedContent)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            logger.error("Failed to get top 5 notifications for teacher {}: {}", teacherId, e.getMessage(), e);
            return List.of();
        }
    }

    /**
     * Create notifications for all classrooms created by a teacher
     * Sets classroomId as "100" to indicate bulk notification for all classrooms
     */
    @Transactional
    public BulkNotificationResponse createNotificationForAllTeacherClasses(NotificationRequest request, String teacherId) {
        logger.info("Creating bulk notifications for teacher {} with title: {}", teacherId, request.getTitle());

        // Get all classrooms for the teacher
        List<ClassroomSummary> classrooms = classroomService.getClassroomSummariesByTeacherId(teacherId);

        if (classrooms.isEmpty()) {
            logger.warn("No classrooms found for teacher {}", teacherId);
            return new BulkNotificationResponse()
                    .setTotalClassrooms(0)
                    .setSuccessfulNotifications(0)
                    .setFailedNotifications(0)
                    .setResults(new ArrayList<>())
                    .setMessage("No classrooms found for this teacher");
        }

        List<BulkNotificationResponse.NotificationResult> results = new ArrayList<>();
        int successCount = 0;
        int failCount = 0;

        // Create a single notification with classroomId "100" for all classrooms
        try {
            // Create notification entity with classroomId "100" to indicate bulk notification
            NotificationEntity notificationEntity = new NotificationEntity();
            notificationEntity.setId(idGenerator.generateId());
            notificationEntity.setUserId(teacherId);
            notificationEntity.setClassroomId("100"); // Special ID for bulk notifications
            notificationEntity.setTitle(request.getTitle());
            notificationEntity.setContent(request.getContent());
            // createdDate and updatedDate are set automatically by @CreationTimestamp and @UpdateTimestamp

            // Save notification
            NotificationEntity savedEntity = notificationDao.create(notificationEntity);

            // Add successful result for all classrooms
            for (ClassroomSummary classroom : classrooms) {
                results.add(new BulkNotificationResponse.NotificationResult()
                        .setClassroomId(classroom.getId())
                        .setClassroomName(classroom.getClassName())
                        .setNotificationId(savedEntity.getId())
                        .setSuccess(true)
                        .setErrorMessage(null));
            }

            successCount = classrooms.size();
            logger.info("Successfully created bulk notification {} for {} classrooms",
                    savedEntity.getId(), classrooms.size());

        } catch (Exception e) {
            // Add failed result for all classrooms
            for (ClassroomSummary classroom : classrooms) {
                results.add(new BulkNotificationResponse.NotificationResult()
                        .setClassroomId(classroom.getId())
                        .setClassroomName(classroom.getClassName())
                        .setNotificationId(null)
                        .setSuccess(false)
                        .setErrorMessage(e.getMessage()));
            }

            failCount = classrooms.size();
            logger.error("Failed to create bulk notification for teacher {}: {}", teacherId, e.getMessage(), e);
        }

        String message = String.format("Created bulk notification for %d out of %d classrooms",
                successCount, classrooms.size());

        logger.info("Bulk notification creation completed for teacher {}: {} successful, {} failed",
                teacherId, successCount, failCount);

        return new BulkNotificationResponse()
                .setTotalClassrooms(classrooms.size())
                .setSuccessfulNotifications(successCount)
                .setFailedNotifications(failCount)
                .setResults(results)
                .setMessage(message);
    }

    /**
     * Get bulk notifications (classroomId "100") for teachers of user's enrolled classrooms
     */
    private List<Notification> getBulkNotificationsForUserClassrooms(String userId, List<String> classroomIds) {
        if (classroomIds == null || classroomIds.isEmpty()) {
            return List.of();
        }

        try {
            // Get all teachers for the user's enrolled classrooms
            List<String> teacherIds = new ArrayList<>();
            for (String classroomId : classroomIds) {
                try {
                    var classroom = classroomService.getClassroomById(classroomId);
                    if (classroom != null && classroom.getTeacherId() != null) {
                        teacherIds.add(classroom.getTeacherId());
                    }
                } catch (Exception e) {
                    logger.warn("Failed to get classroom {} for bulk notifications: {}", classroomId, e.getMessage());
                }
            }

            // Remove duplicates
            teacherIds = teacherIds.stream().distinct().collect(Collectors.toList());

            // Get bulk notifications (classroomId "100") from these teachers
            List<Notification> bulkNotifications = new ArrayList<>();
            for (String teacherId : teacherIds) {
                List<NotificationEntity> teacherBulkNotifications = notificationDao.getByUserIdAndClassroomId(teacherId, "100");
                bulkNotifications.addAll(teacherBulkNotifications.stream()
                        .map(mapper::toDto)
                        .collect(Collectors.toList()));
            }

            return bulkNotifications;

        } catch (Exception e) {
            logger.error("Failed to get bulk notifications for user {}: {}", userId, e.getMessage(), e);
            return List.of();
        }
    }

    /**
     * Get bulk notifications (classroomId "100") for teachers of user's enrolled classrooms
     * Returns entities for internal processing
     */
    private List<NotificationEntity> getBulkNotificationsForUserClassroomsEntities(String userId, List<String> classroomIds) {
        if (classroomIds == null || classroomIds.isEmpty()) {
            return List.of();
        }

        try {
            // Get all teachers for the user's enrolled classrooms
            List<String> teacherIds = new ArrayList<>();
            for (String classroomId : classroomIds) {
                try {
                    var classroom = classroomService.getClassroomById(classroomId);
                    if (classroom != null && classroom.getTeacherId() != null) {
                        teacherIds.add(classroom.getTeacherId());
                    }
                } catch (Exception e) {
                    logger.warn("Failed to get classroom {} for bulk notifications: {}", classroomId, e.getMessage());
                }
            }

            // Remove duplicates
            teacherIds = teacherIds.stream().distinct().collect(Collectors.toList());

            // Get bulk notifications (classroomId "100") from these teachers
            List<NotificationEntity> bulkNotifications = new ArrayList<>();
            for (String teacherId : teacherIds) {
                List<NotificationEntity> teacherBulkNotifications = notificationDao.getByUserIdAndClassroomId(teacherId, "100");
                bulkNotifications.addAll(teacherBulkNotifications);
            }

            return bulkNotifications;

        } catch (Exception e) {
            logger.error("Failed to get bulk notification entities for user {}: {}", userId, e.getMessage(), e);
            return List.of();
        }
    }

    /**
     * Get all bulk notifications (classroomId "100") created by a teacher
     */
    public List<Notification> getBulkNotificationsByTeacher(String teacherId) {
        logger.info("Getting bulk notifications for teacher: {}", teacherId);

        List<NotificationEntity> bulkNotifications = notificationDao.getByUserIdAndClassroomId(teacherId, "100");

        return bulkNotifications.stream()
                .map(mapper::toDto)
                .sorted((n1, n2) -> Long.compare(n2.getCreatedDate(), n1.getCreatedDate()))
                .collect(Collectors.toList());
    }

    /**
     * Get notifications by type for a teacher
     * @param teacherId The teacher's ID
     * @param type "bulk" for classroomId "100" notifications, "specific" for individual classroom notifications
     */
    public List<Notification> getNotificationsByType(String teacherId, String type) {
        logger.info("Getting {} notifications for teacher: {}", type, teacherId);

        List<NotificationEntity> notifications;

        if ("bulk".equalsIgnoreCase(type)) {
            // Get bulk notifications (classroomId "100")
            notifications = notificationDao.getByUserIdAndClassroomId(teacherId, "100");
        } else if ("specific".equalsIgnoreCase(type)) {
            // Get all notifications for this teacher and filter out bulk ones
            notifications = notificationDao.getByUserId(teacherId).stream()
                    .filter(notification -> !"100".equals(notification.getClassroomId()))
                    .collect(Collectors.toList());
        } else {
            throw new IllegalArgumentException("Invalid notification type. Must be 'bulk' or 'specific'");
        }

        return notifications.stream()
                .map(mapper::toDto)
                .sorted((n1, n2) -> Long.compare(n2.getCreatedDate(), n1.getCreatedDate()))
                .collect(Collectors.toList());
    }

    /**
     * Helper method to map NotificationEntity to Notification with truncated content (max 150 characters)
     */
    private Notification mapToNotificationWithTruncatedContent(NotificationEntity entity) {
        Notification notification = mapper.toDto(entity);

        // Truncate content to maximum 150 characters
        if (notification.getContent() != null && notification.getContent().length() > 150) {
            String truncatedContent = notification.getContent().substring(0, 147) + "...";
            notification.setContent(truncatedContent);
        }

        return notification;
    }

    /**
     * Validate and filter file IDs to ensure they exist and belong to the user
     */
    private List<String> validateAndFilterFileIds(List<String> fileIds, String userId) {
        if (fileIds == null || fileIds.isEmpty()) {
            return new ArrayList<>();
        }

        List<String> validFileIds = new ArrayList<>();

        for (String fileId : fileIds) {
            if (fileId != null && !fileId.trim().isEmpty()) {
                try {
                    FileInfo fileInfo = fileService.getFileInfo(fileId);

                    // Check if file belongs to the user
                    if (fileInfo.getUploadedBy().equals(userId)) {
                        validFileIds.add(fileId);
                        logger.debug("Validated file attachment: {}", fileId);
                    } else {
                        logger.warn("User {} attempted to attach file {} they don't own", userId, fileId);
                    }
                } catch (Exception e) {
                    logger.warn("Invalid file ID {} provided by user {}: {}", fileId, userId, e.getMessage());
                }
            }
        }

        logger.info("Validated {} out of {} file attachments for user {}",
                   validFileIds.size(), fileIds.size(), userId);

        return validFileIds;
    }

    /**
     * Enrich notification with file information
     */
    private Notification enrichNotificationWithFiles(Notification notification) {
        if (notification.getAttachedFileIds() == null || notification.getAttachedFileIds().isEmpty()) {
            notification.setAttachedFiles(new ArrayList<>());
            return notification;
        }

        List<FileInfo> attachedFiles = new ArrayList<>();

        for (String fileId : notification.getAttachedFileIds()) {
            try {
                FileInfo fileInfo = fileService.getFileInfo(fileId);
                attachedFiles.add(fileInfo);
            } catch (Exception e) {
                logger.warn("Failed to load file info for attached file {}: {}", fileId, e.getMessage());
            }
        }

        notification.setAttachedFiles(attachedFiles);
        return notification;
    }
}