package com.marglabs.trackmyclass.notification.rest.controller;

import com.marglabs.trackmyclass.classroom.dao.ClassroomDao;
import com.marglabs.trackmyclass.enrollment.model.Enrollment;
import com.marglabs.trackmyclass.enrollment.service.EnrollmentService;
import com.marglabs.trackmyclass.notification.model.*;
import com.marglabs.trackmyclass.notification.service.NotificationService;
import com.marglabs.trackmyclass.user.model.User;
import com.marglabs.trackmyclass.user.model.RoleType;
import com.marglabs.trackmyclass.user.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/notificationManagement/v1")
@Tag(name = "Notification Management", description = "API for managing classroom notifications")
public class NotificationController {
    private static final Logger logger = LoggerFactory.getLogger(NotificationController.class);
    @Autowired
    private NotificationService service;

    @Autowired
    private EnrollmentService enrollmentService;

    @Autowired
    private UserService userService;

    @Operation(summary = "Create a new notification", description = "Creates a new notification for a specific classroom. Files should be uploaded separately using the file upload API, then reference the file IDs in the attachedFileIds field.")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Notification created successfully",
                content = @Content(schema = @Schema(implementation = Notification.class))),
        @ApiResponse(responseCode = "400", description = "Invalid input - ensure request body is valid JSON with required fields (title, content)"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Forbidden - only teachers can create notifications")
    })
    @PostMapping(value = "/classroom/{classroomId}/notification", consumes = "application/json", produces = "application/json")
    @ResponseStatus(HttpStatus.CREATED)
    public Notification createNotification(
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal,
            @Parameter(description = "ID of the classroom") @PathVariable String classroomId,
            @Parameter(description = "Notification details") @Valid @RequestBody NotificationRequest request) {

        logger.info("Creating notification for classroom: {} with title: '{}'", classroomId, request.getTitle());

        // Get teacher from Cognito token
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());

        return service.createNotificationFromRequest(request, classroomId, teacher.getId());
    }

    @Operation(summary = "Get notifications by classroom", description = "Returns all notifications for a specific classroom")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successful operation",
                content = @Content(schema = @Schema(implementation = Notification.class)))
    })
    @GetMapping(value = "/classroom/{classroomId}/notifications", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<Notification> getNotificationsByClassroom(
            @Parameter(description = "ID of the classroom") @PathVariable String classroomId) {
        return service.getNotificationsByClassroom(classroomId);
    }

    @GetMapping(value = "/{userId}/notifications", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<Notification> getNotificationsByUser(@PathVariable String userId) {
        return service.getNotificationsByUser(userId);
    }

    @GetMapping(value = "/teacher/notifications", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<Notification> getTeacherNotifications(@AuthenticationPrincipal Jwt userPrincipal) {
        String cognitoId = userPrincipal.getSubject();
        User user = userService.getUserByCognitoId(cognitoId);
        return service.getNotificationsByUser(user.getId());
    }

    @Operation(summary = "Get stp notifications", description = "Returns all notifications for classes the student is enrolled in")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successful operation",
                content = @Content(schema = @Schema(implementation = Notification.class))),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/stp-notifications", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<Notification> getStpNotifications(@AuthenticationPrincipal Jwt userPrincipal) {
        // Get user from Cognito token
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());

        // Get all enrollments for the user
        List<Enrollment> enrollments = enrollmentService.getEnrollmentsByUserId(user.getId());

        // Extract classroom IDs from enrollments
        List<String> classroomIds = enrollments.stream()
                .map(Enrollment::getClassroomId)
                .collect(Collectors.toList());

        // Get notifications for all these classrooms
        return service.getNotificationsForUserEnrollments(user.getId(), classroomIds);
    }

    @Operation(summary = "Update notification", description = "Update an existing notification")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Notification updated successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request data"),
        @ApiResponse(responseCode = "404", description = "Notification not found"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @PutMapping(value = "/notification/{id}", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public Notification updateNotification(
            @Parameter(description = "Notification ID") @PathVariable String id,
            @Valid @RequestBody NotificationUpdateRequest updateRequest,
            @AuthenticationPrincipal Jwt userPrincipal) {

        // Get user from Cognito token
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());

        // For now, allow any authenticated user to update notifications
        // In the future, you might want to add authorization checks
        // (e.g., only the creator or admin can update)
        return service.updateNotification(id, updateRequest, user.getId());
    }

    @Operation(summary = "Delete notification", description = "Delete a notification by ID")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "Notification deleted successfully"),
        @ApiResponse(responseCode = "404", description = "Notification not found"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @DeleteMapping(value = "/notification/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void deleteNotification(
            @Parameter(description = "Notification ID") @PathVariable String id,
            @AuthenticationPrincipal Jwt userPrincipal) {

        // Get user from Cognito token
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());

        // For now, allow any authenticated user to delete notifications
        // In the future, you might want to add authorization checks
        service.deleteNotification(id);
    }

    @Operation(summary = "Get top 5 notifications for user",
               description = "Returns top 5 recent notifications for teachers, students, and parents based on their role")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successful operation"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/top5", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<Notification> getTop5Notifications(@AuthenticationPrincipal Jwt userPrincipal) {
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());

        // Check if user is a teacher
        boolean isTeacher = userService.hasRole(user.getId(), RoleType.TEACHER);

        if (isTeacher) {
            // For teachers, get notifications from their own classrooms and bulk notifications they created
            return service.getTop5NotificationsForTeacher(user.getId());
        } else {
            // For students and parents, get notifications based on enrollments
            List<Enrollment> enrollments = enrollmentService.getEnrollmentsByUserId(user.getId());

            List<String> classroomIds = enrollments.stream()
                    .map(Enrollment::getClassroomId)
                    .collect(Collectors.toList());

            return service.getTop5NotificationsForUser(user.getId(), classroomIds);
        }
    }

    @Operation(summary = "Create notifications for all teacher's classrooms",
               description = "Creates a single notification with classroomId '100' that applies to all classrooms created by the authenticated teacher")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Bulk notification created successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request data"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @PostMapping(value = "/teacher/bulk", produces = "application/json")
    @ResponseStatus(HttpStatus.CREATED)
    public BulkNotificationResponse createBulkNotificationForTeacherClasses(
            @Valid @RequestBody NotificationRequest request,
            @AuthenticationPrincipal Jwt userPrincipal) {

        // Get teacher from Cognito token
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());

        return service.createNotificationForAllTeacherClasses(request, teacher.getId());
    }

    @Operation(summary = "Get bulk notifications",
               description = "Get all bulk notifications (classroomId '100') created by the authenticated teacher")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Bulk notifications retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/teacher/bulk", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<Notification> getTeacherBulkNotifications(@AuthenticationPrincipal Jwt userPrincipal) {
        // Get teacher from Cognito token
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());

        return service.getBulkNotificationsByTeacher(teacher.getId());
    }

    @Operation(summary = "Get notifications by type",
               description = "Get notifications filtered by type: 'bulk' for all-classroom notifications (classroomId '100') or 'specific' for individual classroom notifications")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Notifications retrieved successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid notification type"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/teacher/by-type", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<Notification> getNotificationsByType(
            @Parameter(description = "Notification type: 'bulk' or 'specific'") @RequestParam String type,
            @AuthenticationPrincipal Jwt userPrincipal) {

        // Get teacher from Cognito token
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());

        return service.getNotificationsByType(teacher.getId(), type);
    }
}