package com.marglabs.trackmyclass.notification.model;

import com.marglabs.trackmyclass.file.model.FileInfo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class Notification {
    private String id;
    private String classroomId;
    private String userId;
    private String title;
    private String content;

    // File attachments
    private List<String> attachedFileIds;
    private List<FileInfo> attachedFiles; // Populated by service layer

    private long createdDate;
    private long updatedDate;
}