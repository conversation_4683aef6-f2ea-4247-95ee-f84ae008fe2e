package com.marglabs.trackmyclass.notification.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class BulkNotificationResponse {
    
    private int totalClassrooms;
    private int successfulNotifications;
    private int failedNotifications;
    private List<NotificationResult> results;
    private String message;
    
    @Data
    @Accessors(chain = true)
    public static class NotificationResult {
        private String classroomId;
        private String classroomName;
        private String notificationId;
        private boolean success;
        private String errorMessage;
    }
}
