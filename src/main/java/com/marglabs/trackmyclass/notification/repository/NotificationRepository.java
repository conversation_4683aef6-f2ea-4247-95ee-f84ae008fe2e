package com.marglabs.trackmyclass.notification.repository;

import com.marglabs.trackmyclass.notification.entity.NotificationEntity;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.data.rest.core.annotation.RepositoryRestResource;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@RepositoryRestResource(exported = false)
public interface NotificationRepository extends JpaRepository<NotificationEntity, String> {
    List<NotificationEntity> findByClassroomId(String classroomId);
    List<NotificationEntity> findByUserId(String userId);
    List<NotificationEntity> findByUserIdAndClassroomId(String userId, String classroomId);

    @Query("SELECT n FROM NotificationEntity n WHERE n.classroomId IN :classroomIds ORDER BY n.createdDate DESC")
    List<NotificationEntity> findTop5ByClassroomIdsOrderByCreatedDateDesc(@Param("classroomIds") List<String> classroomIds, Pageable pageable);
}