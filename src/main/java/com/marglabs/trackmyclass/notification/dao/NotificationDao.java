package com.marglabs.trackmyclass.notification.dao;

import com.marglabs.common.rest.error.EntityNotFoundException;
import com.marglabs.trackmyclass.notification.entity.NotificationEntity;
import com.marglabs.trackmyclass.notification.repository.NotificationRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

@Component
public class NotificationDao {
    @Autowired
    private NotificationRepository repository;

    public NotificationEntity create(NotificationEntity entity) {
        // createdDate and updatedDate are set automatically by @CreationTimestamp and @UpdateTimestamp
        return repository.save(entity);
    }

    public NotificationEntity getById(String id) {
        Optional<NotificationEntity> entity = repository.findById(id);
        if (entity.isPresent()) {
            return entity.get();
        }
        throw new EntityNotFoundException(HttpStatus.NOT_FOUND, "NOTIFICATION", "NOTIFICATION_NOT_FOUND", "notification_not_found_details", id);
    }

    public List<NotificationEntity> getByClassroomId(String classroomId) {
        return repository.findByClassroomId(classroomId);
    }

    public List<NotificationEntity> getByUserId(String userId) {
        return repository.findByUserId(userId);
    }

    public List<NotificationEntity> getByUserIdAndClassroomId(String userId, String classroomId) {
        return repository.findByUserIdAndClassroomId(userId, classroomId);
    }

    public NotificationEntity update(NotificationEntity entity) {
        // updatedDate is set automatically by @UpdateTimestamp
        return repository.save(entity);
    }

    public List<NotificationEntity> getTop5ByClassroomIds(List<String> classroomIds) {
        return repository.findTop5ByClassroomIdsOrderByCreatedDateDesc(classroomIds, 
                org.springframework.data.domain.PageRequest.of(0, 5));
    }

    public void deleteById(String id) {
        try {
            repository.deleteById(id);
        } catch (EmptyResultDataAccessException e) {
            throw new EntityNotFoundException(HttpStatus.NOT_FOUND, "NOTIFICATION", "NOTIFICATION_NOT_FOUND", "notification_not_found_details", id);
        }
    }
}