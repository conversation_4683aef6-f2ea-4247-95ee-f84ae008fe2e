package com.marglabs.trackmyclass.common.util;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

public class DateTimeUtil {
    
    public static final String ISO_DATETIME_FORMAT = "yyyy-MM-dd'T'HH:mm:ss";
    public static final String ISO_DATE_FORMAT = "yyyy-MM-dd";
    public static final String ISO_TIME_FORMAT = "HH:mm";
    public static final DateTimeFormatter ISO_DATETIME_FORMATTER = DateTimeFormatter.ofPattern(ISO_DATETIME_FORMAT);
    public static final DateTimeFormatter ISO_DATE_FORMATTER = DateTimeFormatter.ofPattern(ISO_DATE_FORMAT);
    public static final DateTimeFormatter ISO_TIME_FORMATTER = DateTimeFormatter.ofPattern(ISO_TIME_FORMAT);
    
    /**
     * Parse ISO datetime string to UTC Instant
     * Supports formats:
     * - "2024-01-15T10:30:00Z" (UTC)
     * - "2024-01-15T10:30:00+05:30" (with timezone offset)
     * - "2024-01-15T10:30:00" (assumes UTC)
     */
    public static Instant parseToUtcInstant(String dateTimeStr) {
        try {
            if (dateTimeStr == null || dateTimeStr.isEmpty()) {
                return null;
            }
            
            // If timezone is included in the string, parse directly
            if (dateTimeStr.contains("+") || dateTimeStr.contains("Z") || 
                (dateTimeStr.contains("-") && dateTimeStr.lastIndexOf("-") > 10)) {
                return Instant.parse(dateTimeStr.endsWith("Z") ? dateTimeStr : 
                    (dateTimeStr.contains("+") || dateTimeStr.contains("-") ? dateTimeStr : dateTimeStr + "Z"));
            }
            
            // Assume UTC if no timezone info
            LocalDateTime localDateTime = LocalDateTime.parse(dateTimeStr, ISO_DATETIME_FORMATTER);
            return localDateTime.atZone(ZoneOffset.UTC).toInstant();
            
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("Invalid date-time format: " + dateTimeStr + 
                ". Expected format: " + ISO_DATETIME_FORMAT + " or ISO format with timezone", e);
        }
    }
    
    /**
     * Combine LocalDate and LocalTime to UTC Instant
     */
    public static Instant combineToUtcInstant(LocalDate date, LocalTime time) {
        if (date == null || time == null) return null;
        return LocalDateTime.of(date, time).atZone(ZoneOffset.UTC).toInstant();
    }
    
    /**
     * Combine date string (YYYY-MM-DD) and time string (HH:MM) to UTC Instant
     */
    public static Instant combineToUtcInstant(String dateStr, String timeStr) {
        if (dateStr == null || timeStr == null) return null;
        
        try {
            LocalDate date = LocalDate.parse(dateStr, ISO_DATE_FORMATTER);
            LocalTime time = LocalTime.parse(timeStr, ISO_TIME_FORMATTER);
            return LocalDateTime.of(date, time).atZone(ZoneOffset.UTC).toInstant();
        } catch (Exception e) {
            throw new IllegalArgumentException("Invalid date/time format. Expected date: YYYY-MM-DD, time: HH:MM", e);
        }
    }
    
    /**
     * Parse date string (YYYY-MM-DD) to UTC Instant at midnight
     */
    public static Instant parseDateToUtcInstant(String dateStr) {
        if (dateStr == null) return null;
        
        try {
            LocalDate date = LocalDate.parse(dateStr, ISO_DATE_FORMATTER);
            return date.atStartOfDay(ZoneOffset.UTC).toInstant();
        } catch (Exception e) {
            throw new IllegalArgumentException("Invalid date format. Expected: YYYY-MM-DD", e);
        }
    }
    
    /**
     * Format UTC Instant to ISO string
     */
    public static String formatUtcInstant(Instant instant) {
        if (instant == null) return null;
        return instant.toString();
    }
    
    /**
     * Format UTC Instant to date string (YYYY-MM-DD)
     */
    public static String formatUtcInstantToDate(Instant instant) {
        if (instant == null) return null;
        return instant.atZone(ZoneOffset.UTC).toLocalDate().format(ISO_DATE_FORMATTER);
    }
    
    /**
     * Format UTC Instant to time string (HH:MM)
     */
    public static String formatUtcInstantToTime(Instant instant) {
        if (instant == null) return null;
        return instant.atZone(ZoneOffset.UTC).toLocalTime().format(ISO_TIME_FORMATTER);
    }
    
    /**
     * Get current UTC instant
     */
    public static Instant nowUtc() {
        return Instant.now();
    }
    
    /**
     * Extract LocalDate from UTC Instant
     */
    public static LocalDate extractDateFromUtc(Instant instant) {
        if (instant == null) return null;
        return instant.atZone(ZoneOffset.UTC).toLocalDate();
    }
    
    /**
     * Extract LocalTime from UTC Instant
     */
    public static LocalTime extractTimeFromUtc(Instant instant) {
        if (instant == null) return null;
        return instant.atZone(ZoneOffset.UTC).toLocalTime();
    }
    
    /**
     * Extract LocalDateTime from UTC Instant
     */
    public static LocalDateTime extractDateTimeFromUtc(Instant instant) {
        if (instant == null) return null;
        return instant.atZone(ZoneOffset.UTC).toLocalDateTime();
    }
    
    /**
     * Check if an instant is in the future
     */
    public static boolean isFuture(Instant instant) {
        if (instant == null) return false;
        return instant.isAfter(Instant.now());
    }
    
    /**
     * Check if an instant is in the past
     */
    public static boolean isPast(Instant instant) {
        if (instant == null) return false;
        return instant.isBefore(Instant.now());
    }
    
    /**
     * Check if an instant is today (in UTC)
     */
    public static boolean isToday(Instant instant) {
        if (instant == null) return false;
        LocalDate instantDate = extractDateFromUtc(instant);
        LocalDate today = LocalDate.now(ZoneOffset.UTC);
        return instantDate.equals(today);
    }
    
    /**
     * Add days to an instant
     */
    public static Instant addDays(Instant instant, long days) {
        if (instant == null) return null;
        return instant.plus(Duration.ofDays(days));
    }
    
    /**
     * Add hours to an instant
     */
    public static Instant addHours(Instant instant, long hours) {
        if (instant == null) return null;
        return instant.plus(Duration.ofHours(hours));
    }
    
    /**
     * Add minutes to an instant
     */
    public static Instant addMinutes(Instant instant, long minutes) {
        if (instant == null) return null;
        return instant.plus(Duration.ofMinutes(minutes));
    }

    /**
     * Combine date and time strings with timezone to UTC Instant
     * @param dateStr Date in format "YYYY-MM-DD"
     * @param timeStr Time in format "HH:MM"
     * @param timezoneId Timezone ID (e.g., "America/New_York", "Asia/Kolkata")
     * @return UTC Instant
     */
    public static Instant combineToUtcInstantWithTimezone(String dateStr, String timeStr, String timezoneId) {
        if (dateStr == null || timeStr == null || timezoneId == null) {
            throw new IllegalArgumentException("Date, time, and timezone are required");
        }

        try {
            LocalDate date = LocalDate.parse(dateStr, ISO_DATE_FORMATTER);
            LocalTime time = LocalTime.parse(timeStr, ISO_TIME_FORMATTER);
            ZoneId zoneId = ZoneId.of(timezoneId);

            ZonedDateTime zonedDateTime = ZonedDateTime.of(date, time, zoneId);
            return zonedDateTime.toInstant();

        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("Invalid date/time format. Expected date: " + ISO_DATE_FORMAT +
                ", time: " + ISO_TIME_FORMAT + ", timezone: valid timezone ID", e);
        } catch (Exception e) {
            throw new IllegalArgumentException("Invalid timezone: " + timezoneId, e);
        }
    }

    /**
     * Validate timezone ID
     * @param timezoneId Timezone ID to validate
     * @return true if valid, false otherwise
     */
    public static boolean isValidTimezone(String timezoneId) {
        if (timezoneId == null || timezoneId.trim().isEmpty()) {
            return false;
        }

        try {
            ZoneId.of(timezoneId);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Get common timezone IDs for educational institutions
     * @return List of common timezone IDs
     */
    public static java.util.List<String> getCommonTimezones() {
        return java.util.Arrays.asList(
            "UTC",
            "America/New_York",    // Eastern Time
            "America/Chicago",     // Central Time
            "America/Denver",      // Mountain Time
            "America/Los_Angeles", // Pacific Time
            "Europe/London",       // GMT/BST
            "Europe/Paris",        // CET/CEST
            "Europe/Berlin",       // CET/CEST
            "Asia/Kolkata",        // IST
            "Asia/Shanghai",       // CST
            "Asia/Tokyo",          // JST
            "Asia/Dubai",          // GST
            "Asia/Singapore",      // SGT
            "Australia/Sydney",    // AEST/AEDT
            "America/Toronto",     // Eastern Time (Canada)
            "America/Sao_Paulo"    // BRT
        );
    }

    /**
     * Convert UTC Instant to local time in specified timezone
     * @param utcInstant UTC Instant
     * @param timezoneId Target timezone ID
     * @return ZonedDateTime in the specified timezone
     */
    public static ZonedDateTime convertUtcToTimezone(Instant utcInstant, String timezoneId) {
        if (utcInstant == null || timezoneId == null) {
            throw new IllegalArgumentException("UTC instant and timezone are required");
        }

        try {
            ZoneId zoneId = ZoneId.of(timezoneId);
            return utcInstant.atZone(zoneId);
        } catch (Exception e) {
            throw new IllegalArgumentException("Invalid timezone: " + timezoneId, e);
        }
    }
}
