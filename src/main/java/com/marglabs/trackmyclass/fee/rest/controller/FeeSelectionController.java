package com.marglabs.trackmyclass.fee.rest.controller;

import com.marglabs.common.rest.error.GeneralException;
import com.marglabs.trackmyclass.fee.model.StudentFeeSelection;
import com.marglabs.trackmyclass.fee.model.StudentFeeSelectionRequest;
import com.marglabs.trackmyclass.fee.service.StudentFeeSelectionService;
import com.marglabs.trackmyclass.user.model.RoleType;
import com.marglabs.trackmyclass.user.model.User;
import com.marglabs.trackmyclass.user.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/feeSelection/v1")
@Tag(name = "Fee Selection Management", description = "API for managing class fee selection")
public class FeeSelectionController {


    @Autowired
    private StudentFeeSelectionService studentFeeSelectionService;

    @Autowired
    private UserService userService;
    

    @Operation(summary = "Select fee structure",
               description = "Student selects a fee structure from available options for a classroom")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Fee structure selected successfully",
                content = @Content(schema = @Schema(implementation = StudentFeeSelection.class))),
        @ApiResponse(responseCode = "400", description = "Invalid input or fee structure not for classroom"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Student not enrolled in classroom"),
        @ApiResponse(responseCode = "404", description = "Fee structure or classroom not found")
    })
    @PostMapping(value = "/student/fee-selection", consumes = "application/json", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public StudentFeeSelection selectFeeStructure(
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal,
            @Parameter(description = "Fee structure selection details") @Valid @RequestBody StudentFeeSelectionRequest request) {

        User student = userService.getUserByCognitoId(userPrincipal.getSubject());
        return studentFeeSelectionService.selectFeeStructure(request, student.getId());
    }

    @Operation(summary = "Get student's fee selection for classroom",
               description = "Get the fee structure selected by the authenticated student for a specific classroom")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Fee selection retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "404", description = "No fee selection found")
    })
    @GetMapping(value = "/student/fee-selection/classroom/{classroomId}", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public StudentFeeSelection getStudentFeeSelectionForClassroom(
            @Parameter(description = "Classroom ID") @PathVariable String classroomId,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {

        User student = userService.getUserByCognitoId(userPrincipal.getSubject());
        return studentFeeSelectionService.getStudentFeeSelection(student.getId(), classroomId)
                .orElseThrow(() -> new GeneralException(HttpStatus.NOT_FOUND, "FEE_SELECTION", "SELECTION_NOT_FOUND",
                        "no_fee_selection_found", student.getId(), classroomId));
    }

    @Operation(summary = "Get all student's fee selections",
               description = "Get all fee structure selections made by the authenticated student")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Fee selections retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/student/fee-selections", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<StudentFeeSelection> getStudentFeeSelections(@Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        User student = userService.getUserByCognitoId(userPrincipal.getSubject());
        return studentFeeSelectionService.getStudentFeeSelections(student.getId());
    }

    @Operation(summary = "Remove fee selection",
               description = "Remove student's fee structure selection for a classroom")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "Fee selection removed successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "404", description = "Fee selection not found")
    })
    @DeleteMapping(value = "/student/fee-selection/classroom/{classroomId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void removeFeeSelection(
            @Parameter(description = "Classroom ID") @PathVariable String classroomId,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {

        User student = userService.getUserByCognitoId(userPrincipal.getSubject());
        studentFeeSelectionService.removeFeeSelection(student.getId(), classroomId);
    }

    @Operation(summary = "Get classroom fee selections (Teacher)",
               description = "Get all student fee selections for a classroom - accessible by teachers only")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Classroom fee selections retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Only teachers can access this endpoint")
    })
    @GetMapping(value = "/teacher/classroom/{classroomId}/fee-selections", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<StudentFeeSelection> getClassroomFeeSelections(
            @Parameter(description = "Classroom ID") @PathVariable String classroomId,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {

        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());

        // Verify user is a teacher
        boolean isTeacher = teacher.getRoles().stream()
                .anyMatch(role -> role.getRole() == RoleType.TEACHER);

        if (!isTeacher) {
            throw new AccessDeniedException("Only teachers can access classroom fee selections");
        }

        return studentFeeSelectionService.getClassroomFeeSelections(classroomId);
    }
    
    @Operation(summary = "Remove student fee selection (Teacher)",
               description = "Remove a student's fee selection from classroom - accessible by teachers only")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "Fee selection removed successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Only teachers can remove fee selections"),
        @ApiResponse(responseCode = "404", description = "Fee selection not found")
    })
    @DeleteMapping(value = "/teacher/classroom/{classroomId}/fee-selection/{feeSelectionId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void removeStudentFeeSelection(
            @Parameter(description = "Classroom ID") @PathVariable String classroomId,
            @Parameter(description = "Fee Selection ID") @PathVariable String feeSelectionId,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {

        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());

        // Verify user is a teacher
        boolean isTeacher = teacher.getRoles().stream()
                .anyMatch(role -> role.getRole() == RoleType.TEACHER);

        if (!isTeacher) {
            throw new AccessDeniedException("Only teachers can remove fee selections");
        }

        studentFeeSelectionService.removeStudentFeeSelection(classroomId, feeSelectionId, teacher.getId());
    }
}
