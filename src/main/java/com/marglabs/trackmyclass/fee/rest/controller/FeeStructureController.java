package com.marglabs.trackmyclass.fee.rest.controller;

import com.marglabs.trackmyclass.fee.model.*;
import com.marglabs.trackmyclass.fee.service.FeeStructureService;
import com.marglabs.trackmyclass.fee.service.StudentFeeSelectionService;
import com.marglabs.trackmyclass.user.model.RoleType;
import com.marglabs.trackmyclass.user.model.User;
import com.marglabs.trackmyclass.user.model.RoleType;
import com.marglabs.trackmyclass.user.service.UserService;
import org.springframework.security.access.AccessDeniedException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.HashMap;

@RestController
@RequestMapping("/api/feeManagement/v1")
@Tag(name = "Fee Structure Management", description = "API for managing class fee structures")
public class FeeStructureController {
    
    @Autowired
    private FeeStructureService feeStructureService;

    @Autowired
    private StudentFeeSelectionService studentFeeSelectionService;

    @Autowired
    private UserService userService;
    
    @Operation(summary = "Create a new fee structure", description = "Creates a new fee structure for a class")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Fee structure created successfully",
                content = @Content(schema = @Schema(implementation = FeeStructure.class))),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Forbidden - only teachers can create fee structures"),
        @ApiResponse(responseCode = "409", description = "Fee structure already exists for this combination")
    })
    @PostMapping(value = "/fee-structures", consumes = "application/json", produces = "application/json")
    @ResponseStatus(HttpStatus.CREATED)
    public FeeStructure createFeeStructure(
            @AuthenticationPrincipal Jwt userPrincipal,
            @Parameter(description = "Fee structure details") @Valid @RequestBody FeeStructureCreationRequest request) {
        
        // Get user from Cognito token
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());
        
        // Verify user is a teacher
        boolean isTeacher = user.getRoles().stream()
                .anyMatch(role -> role.getRole() == RoleType.TEACHER);
        
        if (!isTeacher) {
            throw new AccessDeniedException("Only teachers can create fee structures");
        }
        
        return feeStructureService.createFeeStructure(request, user.getId());
    }
    
    @Operation(summary = "Get fee structure by ID", description = "Returns a fee structure by its ID")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successful operation",
                content = @Content(schema = @Schema(implementation = FeeStructure.class))),
        @ApiResponse(responseCode = "404", description = "Fee structure not found")
    })
    @GetMapping(value = "/fee-structures/{id}", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public FeeStructure getFeeStructureById(
            @Parameter(description = "Fee structure ID") @PathVariable String id) {
        return feeStructureService.getFeeStructureById(id);
    }
    
    @Operation(summary = "Update fee structure", description = "Updates an existing fee structure")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Fee structure updated successfully",
                content = @Content(schema = @Schema(implementation = FeeStructure.class))),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Forbidden - only the teacher who created the fee structure can update it"),
        @ApiResponse(responseCode = "404", description = "Fee structure not found"),
        @ApiResponse(responseCode = "409", description = "Fee structure already exists for this combination")
    })
    @PutMapping(value = "/fee-structures/{id}", consumes = "application/json", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public FeeStructure updateFeeStructure(
            @AuthenticationPrincipal Jwt userPrincipal,
            @Parameter(description = "Fee structure ID") @PathVariable String id,
            @Parameter(description = "Updated fee structure details") @Valid @RequestBody FeeStructureUpdateRequest request) {
        
        // Get user from Cognito token
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());
        
        // Verify user is a teacher
        boolean isTeacher = user.getRoles().stream()
                .anyMatch(role -> role.getRole() == RoleType.TEACHER);
        
        if (!isTeacher) {
            throw new AccessDeniedException("Only teachers can update fee structures");
        }
        
        return feeStructureService.updateFeeStructure(id, request, user.getId());
    }
    
    @Operation(summary = "Deactivate fee structure", description = "Deactivates an existing fee structure")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Fee structure deactivated successfully",
                content = @Content(schema = @Schema(implementation = FeeStructure.class))),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Forbidden - only the teacher who created the fee structure can deactivate it"),
        @ApiResponse(responseCode = "404", description = "Fee structure not found")
    })
    @PatchMapping(value = "/fee-structures/{id}/deactivate", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public FeeStructure deactivateFeeStructure(
            @AuthenticationPrincipal Jwt userPrincipal,
            @Parameter(description = "Fee structure ID") @PathVariable String id) {
        
        // Get user from Cognito token
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());
        
        // Verify user is a teacher
        boolean isTeacher = user.getRoles().stream()
                .anyMatch(role -> role.getRole() == RoleType.TEACHER);
        
        if (!isTeacher) {
            throw new AccessDeniedException("Only teachers can deactivate fee structures");
        }
        
        return feeStructureService.deactivateFeeStructure(id, user.getId());
    }
    

    
    @Operation(summary = "Get classroom fee structures", description = "Returns fee structures for a specific classroom")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successful operation"),
        @ApiResponse(responseCode = "404", description = "Classroom not found")
    })
    @GetMapping(value = "/classrooms/{classroomId}/fee-structures", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<FeeStructure> getClassroomFeeStructures(
            @Parameter(description = "Classroom ID") @PathVariable String classroomId) {
        
        return feeStructureService.getFeeStructuresByClassroom(classroomId);
    }
    
    @Operation(summary = "Get classroom fee structures paginated", description = "Returns paginated fee structures for a specific classroom")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successful operation"),
        @ApiResponse(responseCode = "404", description = "Classroom not found")
    })
    @GetMapping(value = "/classrooms/{classroomId}/fee-structures/paginated", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public Page<FeeStructure> getClassroomFeeStructuresPaginated(
            @Parameter(description = "Classroom ID") @PathVariable String classroomId,
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size) {
        
        return feeStructureService.getFeeStructuresByClassroomPaginated(
                classroomId, 
                PageRequest.of(page, size, Sort.by(Sort.Direction.ASC, "countryCode", "paymentType")));
    }


    
    @Operation(summary = "Search fee structures", description = "Search fee structures with multiple criteria")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successful operation")
    })
    @GetMapping(value = "/fee-structures/search", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public Page<FeeStructure> searchFeeStructures(
            @Parameter(description = "Classroom ID") @RequestParam(required = false) String classroomId,
            @Parameter(description = "Country code") @RequestParam(required = false) String countryCode,
            @Parameter(description = "Payment type") @RequestParam(required = false) PaymentType paymentType,
            @Parameter(description = "Fee status") @RequestParam(required = false) FeeStatus status,
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size) {
        
        return feeStructureService.searchFeeStructures(
                classroomId, countryCode, paymentType, status,
                PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdDate")));
    }
    
    @Operation(summary = "Get metadata", description = "Returns metadata for fee structures (countries, currencies, etc.)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successful operation")
    })
    @GetMapping(value = "/fee-structures/metadata", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public Map<String, Object> getFeeStructureMetadata() {
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("countries", feeStructureService.getDistinctCountries());
        metadata.put("countryCodes", feeStructureService.getDistinctCountryCodes());
        metadata.put("currencies", feeStructureService.getDistinctCurrencies());
        metadata.put("paymentTypes", PaymentType.values());
        metadata.put("feeStatuses", FeeStatus.values());
        return metadata;
    }
}
