package com.marglabs.trackmyclass.fee.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class StudentFeeSelection {
    
    private String id;
    private String studentId;
    private String studentName;
    private String classroomId;
    private String className;
    private String feeStructureId;
    private String feeStructureName;
    private String paymentType;
    private String currency;
    private String countryCode;
    private String country;
    private BigDecimal amount;
    private BigDecimal discountValue;
    private BigDecimal finalAmount;
    private boolean isActive;
    private long createdDate;
    private long updatedDate;
}
