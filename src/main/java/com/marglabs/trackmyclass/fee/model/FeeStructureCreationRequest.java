package com.marglabs.trackmyclass.fee.model;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class FeeStructureCreationRequest {
    @NotBlank(message = "Classroom ID is required")
    private String classroomId;

    @NotNull(message = "Fee amount is required")
    @DecimalMin(value = "0.01", message = "Fee amount must be greater than 0")
    private BigDecimal feeAmount;

    @NotNull(message = "Payment type is required")
    private PaymentType paymentType;

    @NotBlank(message = "Country code is required")
    @Size(min = 2, max = 2, message = "Country code must be a 2-letter ISO code (e.g., US, IN)")
    private String countryCode;

    @DecimalMin(value = "0.00", message = "Discount percentage must be 0 or greater")
    @DecimalMax(value = "100.00", message = "Discount percentage cannot exceed 100")
    private BigDecimal discountPercentage;

    private String description;
}
