package com.marglabs.trackmyclass.fee.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class FeeStructure {
    private String id;
    private String classroomId;
    private String teacherId;
    private BigDecimal feeAmount;
    private String currency;
    private PaymentType paymentType;
    private String country;
    private String countryCode; // ISO 3166-1 alpha-2 country code
    private FeeStatus status;
    private BigDecimal discountPercentage; // Discount percentage (0-100)
    private BigDecimal discountedAmount; // Calculated discounted amount
    private String description;
    private long createdDate;
    private long updatedDate;
    private boolean active;

    // Transient fields
    private String classroomName;
    private String teacherName;
}
