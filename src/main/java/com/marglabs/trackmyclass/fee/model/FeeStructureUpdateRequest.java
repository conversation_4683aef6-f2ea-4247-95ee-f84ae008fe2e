package com.marglabs.trackmyclass.fee.model;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class FeeStructureUpdateRequest {
    @DecimalMin(value = "0.01", message = "Fee amount must be greater than 0")
    private BigDecimal feeAmount;

    private PaymentType paymentType;

    @Size(min = 2, max = 2, message = "Country code must be a 2-letter ISO code (e.g., US, IN)")
    private String countryCode;

    @DecimalMin(value = "0.00", message = "Discount percentage must be 0 or greater")
    @DecimalMax(value = "100.00", message = "Discount percentage cannot exceed 100")
    private BigDecimal discountPercentage;

    private FeeStatus status; // TODO do not ask for this from UI

    private String description;
}
