package com.marglabs.trackmyclass.fee.model;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class StudentFeeSelectionRequest {
    
    @NotBlank(message = "Classroom ID is required")
    private String classroomId;
    
    @NotBlank(message = "Fee structure ID is required")
    private String feeStructureId;
}
