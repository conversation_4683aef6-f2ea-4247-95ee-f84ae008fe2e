package com.marglabs.trackmyclass.fee.dao;

import com.marglabs.common.rest.error.GeneralException;
import com.marglabs.trackmyclass.fee.entity.StudentFeeSelectionEntity;
import com.marglabs.trackmyclass.fee.repository.StudentFeeSelectionRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

@Component
public class StudentFeeSelectionDao {
    
    @Autowired
    private StudentFeeSelectionRepository repository;
    
    public StudentFeeSelectionEntity save(StudentFeeSelectionEntity entity) {
        return repository.save(entity);
    }
    
    public StudentFeeSelectionEntity getById(String id) {
        return repository.findById(id)
                .orElseThrow(() -> new GeneralException(HttpStatus.NOT_FOUND, "FEE_SELECTION", "SELECTION_NOT_FOUND",
                        "student_fee_selection_not_found", id));
    }
    
    public Optional<StudentFeeSelectionEntity> findById(String id) {
        return repository.findById(id);
    }
    
    public Optional<StudentFeeSelectionEntity> findByStudentAndClassroom(String studentId, String classroomId) {
        return repository.findByStudentIdAndClassroomIdAndIsActiveTrue(studentId, classroomId);
    }
    
    public List<StudentFeeSelectionEntity> getByStudentId(String studentId) {
        return repository.findByStudentIdAndIsActiveTrue(studentId);
    }
    
    public List<StudentFeeSelectionEntity> getByClassroomId(String classroomId) {
        return repository.findByClassroomIdAndIsActiveTrue(classroomId);
    }
    
    public List<StudentFeeSelectionEntity> getByFeeStructureId(String feeStructureId) {
        return repository.findByFeeStructureIdAndIsActiveTrue(feeStructureId);
    }
    
    public boolean existsByStudentAndClassroom(String studentId, String classroomId) {
        return repository.existsByStudentIdAndClassroomIdAndIsActiveTrue(studentId, classroomId);
    }
    
    public long countByFeeStructureId(String feeStructureId) {
        return repository.countByFeeStructureIdAndIsActiveTrue(feeStructureId);
    }
    
    public List<StudentFeeSelectionEntity> getActiveSelectionsByStudent(String studentId) {
        return repository.findActiveSelectionsByStudent(studentId);
    }
    
    public List<StudentFeeSelectionEntity> getActiveSelectionsByClassroom(String classroomId) {
        return repository.findActiveSelectionsByClassroom(classroomId);
    }
    
    public StudentFeeSelectionEntity update(StudentFeeSelectionEntity entity) {
        return repository.save(entity);
    }
    
    public void deleteById(String id) {
        repository.deleteById(id);
    }
    
    public boolean existsById(String id) {
        return repository.existsById(id);
    }
}
