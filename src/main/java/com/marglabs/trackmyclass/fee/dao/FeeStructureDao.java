package com.marglabs.trackmyclass.fee.dao;

import com.marglabs.common.rest.error.EntityNotFoundException;
import com.marglabs.trackmyclass.fee.entity.FeeStructureEntity;
import com.marglabs.trackmyclass.fee.model.FeeStatus;
import com.marglabs.trackmyclass.fee.model.PaymentType;
import com.marglabs.trackmyclass.fee.repository.FeeStructureRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

@Component
public class FeeStructureDao {
    private static final Logger logger = LoggerFactory.getLogger(FeeStructureDao.class);
    
    @Autowired
    private FeeStructureRepository repository;
    
    public FeeStructureEntity create(FeeStructureEntity entity) {
        long now = Instant.now().toEpochMilli();
        entity.setCreatedDate(now);
        entity.setUpdatedDate(now);
        if (entity.getStatus() == null) {
            entity.setStatus(FeeStatus.ACTIVE);
        }
        return repository.save(entity);
    }
    
    public FeeStructureEntity update(FeeStructureEntity entity) {
        FeeStructureEntity existingEntity = getById(entity.getId());
        entity.setCreatedDate(existingEntity.getCreatedDate());
        entity.setUpdatedDate(Instant.now().toEpochMilli());
        return repository.save(entity);
    }
    
    public FeeStructureEntity getById(String id) {
        Optional<FeeStructureEntity> entity = repository.findById(id);
        if (entity.isPresent()) {
            return entity.get();
        }
        throw new EntityNotFoundException(HttpStatus.NOT_FOUND, "FEE_STRUCTURE", "FEE_STRUCTURE_NOT_FOUND", 
                "fee_structure_not_found_details", id);
    }
    
    public Page<FeeStructureEntity> getByClassroomIdPaginated(String classroomId, Pageable pageable) {
        return repository.findByClassroomIdAndStatus(classroomId, FeeStatus.ACTIVE, pageable);
    }
    
    public List<FeeStructureEntity> getByClassroomId(String classroomId) {
        return repository.findByClassroomIdAndStatus(classroomId, FeeStatus.ACTIVE);
    }
    
    public Page<FeeStructureEntity> getByTeacherIdPaginated(String teacherId, Pageable pageable) {
        return repository.findByTeacherIdAndStatus(teacherId, FeeStatus.ACTIVE, pageable);
    }
    
    public List<FeeStructureEntity> getByTeacherId(String teacherId) {
        return repository.findByTeacherIdAndStatus(teacherId, FeeStatus.ACTIVE);
    }
    
    public List<FeeStructureEntity> getByCountryCode(String countryCode) {
        return repository.findByCountryCodeAndStatus(countryCode, FeeStatus.ACTIVE);
    }
    
    public Page<FeeStructureEntity> getByCountryCodePaginated(String countryCode, Pageable pageable) {
        return repository.findByCountryCodeAndStatus(countryCode, FeeStatus.ACTIVE, pageable);
    }
    
    public List<FeeStructureEntity> getByClassroomIdAndCountryCode(String classroomId, String countryCode) {
        return repository.findByClassroomIdAndCountryCodeAndStatus(classroomId, countryCode, FeeStatus.ACTIVE);
    }
    
    public Optional<FeeStructureEntity> findByClassroomIdAndCountryCodeAndPaymentType(
            String classroomId, String countryCode, PaymentType paymentType) {
        return repository.findByClassroomIdAndCountryCodeAndPaymentTypeAndStatus(
                classroomId, countryCode, paymentType, FeeStatus.ACTIVE);
    }
    
    public boolean existsByClassroomIdAndCountryCodeAndPaymentType(
            String classroomId, String countryCode, PaymentType paymentType) {
        return repository.existsByClassroomIdAndCountryCodeAndPaymentTypeAndStatus(
                classroomId, countryCode, paymentType, FeeStatus.ACTIVE);
    }
    
    public boolean existsByClassroomIdAndCountryCodeAndPaymentTypeExcluding(
            String classroomId, String countryCode, PaymentType paymentType, String excludeId) {
        return repository.existsByClassroomIdAndCountryCodeAndPaymentTypeAndStatusExcluding(
                classroomId, countryCode, paymentType, FeeStatus.ACTIVE, excludeId);
    }
    
    public List<FeeStructureEntity> getByStatus(FeeStatus status) {
        return repository.findByStatus(status);
    }
    
    public List<FeeStructureEntity> getAll() {
        return repository.findByStatus(FeeStatus.ACTIVE);
    }
    
    public Page<FeeStructureEntity> getAllPaginated(Pageable pageable) {
        return repository.findByStatus(FeeStatus.ACTIVE, pageable);
    }
    
    public Page<FeeStructureEntity> findByCriteria(String classroomId, String countryCode, 
                                                  PaymentType paymentType, FeeStatus status, 
                                                  Pageable pageable) {
        return repository.findByCriteria(classroomId, countryCode, paymentType, status, pageable);
    }
    
    public List<String> getDistinctCountries() {
        return repository.findDistinctCountries();
    }
    
    public List<String> getDistinctCountryCodes() {
        return repository.findDistinctCountryCodes();
    }
    
    public List<String> getDistinctCurrencies() {
        return repository.findDistinctCurrencies();
    }
    
    public void deleteById(String id) {
        try {
            repository.deleteById(id);
        } catch (EmptyResultDataAccessException e) {
            throw new EntityNotFoundException(HttpStatus.NOT_FOUND, "FEE_STRUCTURE", "FEE_STRUCTURE_NOT_FOUND", 
                    "fee_structure_not_found_details", id);
        }
    }
    
    public FeeStructureEntity deactivate(String id) {
        FeeStructureEntity entity = getById(id);
        entity.setStatus(FeeStatus.INACTIVE);
        entity.setUpdatedDate(Instant.now().toEpochMilli());
        return repository.save(entity);
    }
}
