package com.marglabs.trackmyclass.fee.validation;

import com.marglabs.trackmyclass.fee.model.FeeStructureCreationRequest;
import com.marglabs.trackmyclass.fee.model.PaymentType;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Component
public class FeeStructureValidator {

    // Common currency codes
    private static final List<String> VALID_CURRENCIES = Arrays.asList(
            "USD", "EUR", "GBP", "JPY", "AUD", "CAD", "CHF", "CNY", "SEK", "NZD",
            "MXN", "SGD", "HKD", "NOK", "TRY", "ZAR", "BRL", "INR", "KRW", "PLN",
            "THB", "IDR", "HUF", "CZK", "ILS", "CLP", "PHP", "AED", "COP", "SAR",
            "MYR", "RON", "BGN", "HRK", "RUB", "DKK", "EGP", "PKR", "LKR", "BDT"
    );

    // Common country codes
    private static final List<String> VALID_COUNTRY_CODES = Arrays.asList(
            "US", "CA", "GB", "DE", "FR", "IT", "ES", "NL", "BE", "CH", "AT", "SE",
            "NO", "DK", "FI", "IE", "PT", "GR", "PL", "CZ", "HU", "SK", "SI", "EE",
            "LV", "LT", "BG", "RO", "HR", "MT", "CY", "LU", "AU", "NZ", "JP", "KR",
            "CN", "HK", "SG", "MY", "TH", "ID", "PH", "VN", "IN", "PK", "BD", "LK",
            "AE", "SA", "IL", "TR", "EG", "ZA", "NG", "KE", "GH", "BR", "AR", "CL",
            "CO", "PE", "MX", "RU", "UA", "BY", "KZ", "UZ"
    );

    public List<String> validateFeeStructureCreationRequest(FeeStructureCreationRequest request) {
        List<String> errors = new ArrayList<>();

        // Validate fee amount
        if (request.getFeeAmount() != null) {
            if (request.getFeeAmount().compareTo(BigDecimal.ZERO) <= 0) {
                errors.add("Fee amount must be greater than 0");
            }

            if (request.getFeeAmount().scale() > 2) {
                errors.add("Fee amount cannot have more than 2 decimal places");
            }

            // Check for reasonable maximum amount (e.g., 1 million)
            if (request.getFeeAmount().compareTo(new BigDecimal("1000000")) > 0) {
                errors.add("Fee amount cannot exceed 1,000,000");
            }
        }

        // Currency will be auto-populated from country code, so no validation needed

        // Validate country code
        if (request.getCountryCode() != null) {
            String countryCode = request.getCountryCode().toUpperCase();
            if (!countryCode.matches("^[A-Z]{2}$")) {
                errors.add("Country code must be a 2-letter code (e.g., US, IN)");
            } else if (!VALID_COUNTRY_CODES.contains(countryCode)) {
                errors.add("Country code '" + countryCode + "' is not supported");
            }
        }

        // Country name will be auto-populated from country code, so no validation needed

        // Validate description
        if (request.getDescription() != null && request.getDescription().length() > 500) {
            errors.add("Description cannot exceed 500 characters");
        }

        // Validate payment type specific rules
        if (request.getPaymentType() != null) {
            validatePaymentTypeSpecificRules(request, errors);
        }

        return errors;
    }

    private void validatePaymentTypeSpecificRules(FeeStructureCreationRequest request, List<String> errors) {
        BigDecimal amount = request.getFeeAmount();
        PaymentType paymentType = request.getPaymentType();

        if (amount == null) return;

        switch (paymentType) {
            case ONE_TIME:
                // One-time payments can be any reasonable amount
                if (amount.compareTo(new BigDecimal("10")) < 0) {
                    errors.add("One-time fee should be at least 10");
                }
                break;

            case MONTHLY:
                // Monthly payments should be reasonable for monthly billing
                if (amount.compareTo(new BigDecimal("1")) < 0) {
                    errors.add("Monthly fee should be at least 1");
                }
                if (amount.compareTo(new BigDecimal("10000")) > 0) {
                    errors.add("Monthly fee seems too high (max 10,000)");
                }
                break;

            case QUARTERLY:
                // Quarterly payments should be reasonable for 3-month billing
                if (amount.compareTo(new BigDecimal("3")) < 0) {
                    errors.add("Quarterly fee should be at least 3");
                }
                if (amount.compareTo(new BigDecimal("30000")) > 0) {
                    errors.add("Quarterly fee seems too high (max 30,000)");
                }
                break;

            case YEARLY:
                // Yearly payments should be reasonable for annual billing
                if (amount.compareTo(new BigDecimal("10")) < 0) {
                    errors.add("Yearly fee should be at least 10");
                }
                if (amount.compareTo(new BigDecimal("100000")) > 0) {
                    errors.add("Yearly fee seems too high (max 100,000)");
                }
                break;
        }
    }

    public boolean isValidCurrency(String currency) {
        return currency != null && VALID_CURRENCIES.contains(currency.toUpperCase());
    }

    public boolean isValidCountryCode(String countryCode) {
        return countryCode != null && VALID_COUNTRY_CODES.contains(countryCode.toUpperCase());
    }

    public List<String> getSupportedCurrencies() {
        return new ArrayList<>(VALID_CURRENCIES);
    }

    public List<String> getSupportedCountryCodes() {
        return new ArrayList<>(VALID_COUNTRY_CODES);
    }
}
