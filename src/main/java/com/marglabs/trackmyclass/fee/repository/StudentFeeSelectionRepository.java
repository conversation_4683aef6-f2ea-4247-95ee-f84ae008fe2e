package com.marglabs.trackmyclass.fee.repository;

import com.marglabs.trackmyclass.fee.entity.StudentFeeSelectionEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface StudentFeeSelectionRepository extends JpaRepository<StudentFeeSelectionEntity, String> {
    
    // Find active selection by student and classroom
    Optional<StudentFeeSelectionEntity> findByStudentIdAndClassroomIdAndIsActiveTrue(String studentId, String classroomId);
    
    // Find all selections by student
    List<StudentFeeSelectionEntity> findByStudentIdAndIsActiveTrue(String studentId);
    
    // Find all selections by classroom
    List<StudentFeeSelectionEntity> findByClassroomIdAndIsActiveTrue(String classroomId);
    
    // Find all selections by fee structure
    List<StudentFeeSelectionEntity> findByFeeStructureIdAndIsActiveTrue(String feeStructureId);
    
    // Check if student has already selected a fee structure for a classroom
    boolean existsByStudentIdAndClassroomIdAndIsActiveTrue(String studentId, String classroomId);
    
    // Count students who selected a specific fee structure
    long countByFeeStructureIdAndIsActiveTrue(String feeStructureId);
    
    // Get all active selections for a student across all classrooms
    @Query("SELECT s FROM StudentFeeSelectionEntity s WHERE s.studentId = :studentId AND s.isActive = true ORDER BY s.createdDate DESC")
    List<StudentFeeSelectionEntity> findActiveSelectionsByStudent(@Param("studentId") String studentId);
    
    // Get all active selections for a classroom
    @Query("SELECT s FROM StudentFeeSelectionEntity s WHERE s.classroomId = :classroomId AND s.isActive = true ORDER BY s.createdDate DESC")
    List<StudentFeeSelectionEntity> findActiveSelectionsByClassroom(@Param("classroomId") String classroomId);
}
