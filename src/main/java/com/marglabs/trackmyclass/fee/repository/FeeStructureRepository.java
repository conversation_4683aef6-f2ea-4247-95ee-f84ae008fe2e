package com.marglabs.trackmyclass.fee.repository;

import com.marglabs.trackmyclass.fee.entity.FeeStructureEntity;
import com.marglabs.trackmyclass.fee.model.FeeStatus;
import com.marglabs.trackmyclass.fee.model.PaymentType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.data.rest.core.annotation.RepositoryRestResource;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
@RepositoryRestResource(exported = false)
public interface FeeStructureRepository extends JpaRepository<FeeStructureEntity, String> {

    // Find by classroom
    Page<FeeStructureEntity> findByClassroomIdAndStatus(String classroomId, FeeStatus status, Pageable pageable);
    List<FeeStructureEntity> findByClassroomIdAndStatus(String classroomId, FeeStatus status);

    // Find by teacher
    Page<FeeStructureEntity> findByTeacherIdAndStatus(String teacherId, FeeStatus status, Pageable pageable);
    List<FeeStructureEntity> findByTeacherIdAndStatus(String teacherId, FeeStatus status);

    // Find by country
    List<FeeStructureEntity> findByCountryCodeAndStatus(String countryCode, FeeStatus status);
    Page<FeeStructureEntity> findByCountryCodeAndStatus(String countryCode, FeeStatus status, Pageable pageable);

    // Find by classroom and country
    List<FeeStructureEntity> findByClassroomIdAndCountryCodeAndStatus(String classroomId, String countryCode, FeeStatus status);

    // Find by classroom, country and payment type
    Optional<FeeStructureEntity> findByClassroomIdAndCountryCodeAndPaymentTypeAndStatus(
            String classroomId, String countryCode, PaymentType paymentType, FeeStatus status);

    // Check if fee structure exists for classroom, country and payment type
    @Query("SELECT COUNT(f) > 0 FROM FeeStructureEntity f WHERE f.classroomId = :classroomId " +
           "AND f.countryCode = :countryCode AND f.paymentType = :paymentType " +
           "AND f.status = :status")
    boolean existsByClassroomIdAndCountryCodeAndPaymentTypeAndStatus(
            @Param("classroomId") String classroomId,
            @Param("countryCode") String countryCode,
            @Param("paymentType") PaymentType paymentType,
            @Param("status") FeeStatus status);

    // Check if fee structure exists excluding a specific ID (for updates)
    @Query("SELECT COUNT(f) > 0 FROM FeeStructureEntity f WHERE f.classroomId = :classroomId " +
           "AND f.countryCode = :countryCode AND f.paymentType = :paymentType " +
           "AND f.status = :status AND f.id != :excludeId")
    boolean existsByClassroomIdAndCountryCodeAndPaymentTypeAndStatusExcluding(
            @Param("classroomId") String classroomId,
            @Param("countryCode") String countryCode,
            @Param("paymentType") PaymentType paymentType,
            @Param("status") FeeStatus status,
            @Param("excludeId") String excludeId);

    // Find by status
    List<FeeStructureEntity> findByStatus(FeeStatus status);
    Page<FeeStructureEntity> findByStatus(FeeStatus status, Pageable pageable);

    // Get distinct countries
    @Query("SELECT DISTINCT f.country FROM FeeStructureEntity f WHERE f.status = 'ACTIVE' ORDER BY f.country")
    List<String> findDistinctCountries();

    // Get distinct country codes
    @Query("SELECT DISTINCT f.countryCode FROM FeeStructureEntity f WHERE f.status = 'ACTIVE' ORDER BY f.countryCode")
    List<String> findDistinctCountryCodes();

    // Get distinct currencies
    @Query("SELECT DISTINCT f.currency FROM FeeStructureEntity f WHERE f.status = 'ACTIVE' ORDER BY f.currency")
    List<String> findDistinctCurrencies();

    // Find fee structures by multiple criteria
    @Query("SELECT f FROM FeeStructureEntity f WHERE " +
           "(:classroomId IS NULL OR f.classroomId = :classroomId) " +
           "AND (:countryCode IS NULL OR f.countryCode = :countryCode) " +
           "AND (:paymentType IS NULL OR f.paymentType = :paymentType) " +
           "AND (:status IS NULL OR f.status = :status)")
    Page<FeeStructureEntity> findByCriteria(@Param("classroomId") String classroomId,
                                           @Param("countryCode") String countryCode,
                                           @Param("paymentType") PaymentType paymentType,
                                           @Param("status") FeeStatus status,
                                           Pageable pageable);
}
