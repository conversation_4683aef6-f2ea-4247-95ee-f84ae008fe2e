package com.marglabs.trackmyclass.fee.mapper;

import com.marglabs.trackmyclass.fee.entity.StudentFeeSelectionEntity;
import com.marglabs.trackmyclass.fee.model.StudentFeeSelection;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface StudentFeeSelectionMapper {
    
    @Mapping(target = "studentName", ignore = true) // Will be set by service
    @Mapping(target = "className", ignore = true) // Will be set by service
    @Mapping(target = "feeStructureName", ignore = true) // Will be set by service
    @Mapping(target = "paymentType", ignore = true) // Will be set by service
    @Mapping(target = "currency", ignore = true) // Will be set by service
    @Mapping(target = "countryCode", ignore = true) // Will be set by service
    @Mapping(target = "country", ignore = true) // Will be set by service
    @Mapping(target = "amount", ignore = true) // Will be set by service
    @Mapping(target = "discountValue", ignore = true) // Will be set by service
    @Mapping(target = "finalAmount", ignore = true) // Will be set by service
    @Mapping(target = "createdDate", expression = "java(entity.getCreatedDate() != null ? entity.getCreatedDate().toEpochMilli() : 0)")
    @Mapping(target = "updatedDate", expression = "java(entity.getUpdatedDate() != null ? entity.getUpdatedDate().toEpochMilli() : 0)")
    StudentFeeSelection toDto(StudentFeeSelectionEntity entity);
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdDate", ignore = true)
    @Mapping(target = "updatedDate", ignore = true)
    StudentFeeSelectionEntity toEntity(StudentFeeSelection dto);
}
