package com.marglabs.trackmyclass.fee.mapper;

import com.marglabs.trackmyclass.fee.entity.FeeStructureEntity;
import com.marglabs.trackmyclass.fee.model.FeeStructure;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface FeeStructureMapper {

    @Mapping(target = "classroomName", ignore = true)
    @Mapping(target = "teacherName", ignore = true)
    @Mapping(target = "discountedAmount", ignore = true)
    FeeStructure toDto(FeeStructureEntity entity);

    FeeStructureEntity toEntity(FeeStructure feeStructure);
}
