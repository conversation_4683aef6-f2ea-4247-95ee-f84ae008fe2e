package com.marglabs.trackmyclass.fee.service;

import com.marglabs.common.rest.error.GeneralException;
import com.marglabs.common.utils.IdGenerator;
import com.marglabs.trackmyclass.classroom.service.ClassroomService;
import com.marglabs.trackmyclass.enrollment.service.EnrollmentService;
import com.marglabs.trackmyclass.fee.dao.StudentFeeSelectionDao;
import com.marglabs.trackmyclass.fee.entity.StudentFeeSelectionEntity;
import com.marglabs.trackmyclass.fee.mapper.StudentFeeSelectionMapper;
import com.marglabs.trackmyclass.fee.model.FeeStructure;
import com.marglabs.trackmyclass.fee.model.StudentFeeSelection;
import com.marglabs.trackmyclass.fee.model.StudentFeeSelectionRequest;
import com.marglabs.trackmyclass.fee.service.FeeStructureService;
import com.marglabs.trackmyclass.user.model.RoleType;
import com.marglabs.trackmyclass.user.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class StudentFeeSelectionService {
    
    private static final Logger logger = LoggerFactory.getLogger(StudentFeeSelectionService.class);
    
    @Autowired
    private StudentFeeSelectionDao studentFeeSelectionDao;
    
    @Autowired
    private StudentFeeSelectionMapper mapper;
    
    @Autowired
    private FeeStructureService feeStructureService;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private ClassroomService classroomService;
    
    @Autowired
    private EnrollmentService enrollmentService;
    
    @Autowired
    private IdGenerator idGenerator;
    
    /**
     * Student selects a fee structure for a classroom
     */
    @Transactional
    public StudentFeeSelection selectFeeStructure(StudentFeeSelectionRequest request, String studentId) {
        logger.info("Student {} selecting fee structure {} for classroom {}", 
                   studentId, request.getFeeStructureId(), request.getClassroomId());
        
        // Verify student exists
        var student = userService.getUserById(studentId);
        
        // Verify classroom exists
        var classroom = classroomService.getClassroomById(request.getClassroomId());
        
        // Verify fee structure exists and belongs to the classroom
        FeeStructure feeStructure = feeStructureService.getFeeStructureById(request.getFeeStructureId());
        if (!feeStructure.getClassroomId().equals(request.getClassroomId())) {
            throw new GeneralException(HttpStatus.BAD_REQUEST, "FEE_SELECTION", "INVALID_FEE_STRUCTURE",
                    "fee_structure_not_for_classroom", request.getFeeStructureId(), request.getClassroomId());
        }
        
        // Verify student is enrolled in the classroom
        if (!enrollmentService.isUserEnrolled( request.getClassroomId(),studentId, RoleType.STUDENT)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "FEE_SELECTION", "STUDENT_NOT_ENROLLED",
                    "student_not_enrolled_in_classroom", studentId, request.getClassroomId());
        }
        
        // Check if student has already selected a fee structure for this classroom
        Optional<StudentFeeSelectionEntity> existingSelection = 
                studentFeeSelectionDao.findByStudentAndClassroom(studentId, request.getClassroomId());
        
        if (existingSelection.isPresent()) {
            // Update existing selection
            StudentFeeSelectionEntity entity = existingSelection.get();
            entity.setFeeStructureId(request.getFeeStructureId());
            entity = studentFeeSelectionDao.update(entity);
            
            logger.info("Updated fee structure selection for student {} in classroom {}", studentId, request.getClassroomId());
            return enrichSelectionWithDetails(mapper.toDto(entity));
        } else {
            // Create new selection
            StudentFeeSelectionEntity entity = new StudentFeeSelectionEntity()
                    .setId(idGenerator.generateId())
                    .setStudentId(studentId)
                    .setClassroomId(request.getClassroomId())
                    .setFeeStructureId(request.getFeeStructureId())
                    .setActive(true);
            
            entity = studentFeeSelectionDao.save(entity);
            
            logger.info("Created new fee structure selection for student {} in classroom {}", studentId, request.getClassroomId());
            return enrichSelectionWithDetails(mapper.toDto(entity));
        }
    }
    
    /**
     * Get student's fee structure selection for a specific classroom
     */
    public Optional<StudentFeeSelection> getStudentFeeSelection(String studentId, String classroomId) {
        logger.info("Getting fee structure selection for student {} in classroom {}", studentId, classroomId);
        
        Optional<StudentFeeSelectionEntity> entity = studentFeeSelectionDao.findByStudentAndClassroom(studentId, classroomId);
        
        return entity.map(e -> enrichSelectionWithDetails(mapper.toDto(e)));
    }
    
    /**
     * Get all fee structure selections for a student
     */
    public List<StudentFeeSelection> getStudentFeeSelections(String studentId) {
        logger.info("Getting all fee structure selections for student {}", studentId);
        
        List<StudentFeeSelectionEntity> entities = studentFeeSelectionDao.getByStudentId(studentId);
        
        return entities.stream()
                .map(entity -> enrichSelectionWithDetails(mapper.toDto(entity)))
                .collect(Collectors.toList());
    }
    
    /**
     * Get all fee structure selections for a classroom
     */
    public List<StudentFeeSelection> getClassroomFeeSelections(String classroomId) {
        logger.info("Getting all fee structure selections for classroom {}", classroomId);
        
        List<StudentFeeSelectionEntity> entities = studentFeeSelectionDao.getByClassroomId(classroomId);
        
        return entities.stream()
                .map(entity -> enrichSelectionWithDetails(mapper.toDto(entity)))
                .collect(Collectors.toList());
    }
    
    /**
     * Remove student's fee structure selection
     */
    @Transactional
    public void removeFeeSelection(String studentId, String classroomId) {
        logger.info("Removing fee structure selection for student {} in classroom {}", studentId, classroomId);
        
        Optional<StudentFeeSelectionEntity> entity = studentFeeSelectionDao.findByStudentAndClassroom(studentId, classroomId);
        
        if (entity.isPresent()) {
            StudentFeeSelectionEntity selection = entity.get();
            selection.setActive(false);
            studentFeeSelectionDao.update(selection);
            
            logger.info("Removed fee structure selection for student {} in classroom {}", studentId, classroomId);
        } else {
            throw new GeneralException(HttpStatus.NOT_FOUND, "FEE_SELECTION", "SELECTION_NOT_FOUND",
                    "no_fee_selection_found", studentId, classroomId);
        }
    }
    
    /**
     * Teacher removes student's fee selection by fee selection ID
     */
    @Transactional
    public void removeStudentFeeSelection(String classroomId, String feeSelectionId, String teacherId) {
        logger.info("Teacher {} removing fee selection {} from classroom {}", teacherId, feeSelectionId, classroomId);
        
        // Verify teacher owns the classroom
        var classroom = classroomService.getClassroomById(classroomId);
        if (!classroom.getTeacherId().equals(teacherId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "FEE_SELECTION", "UNAUTHORIZED_ACCESS",
                    "teacher_not_authorized_for_classroom", teacherId, classroomId);
        }
        
        // Get and validate fee selection
        StudentFeeSelectionEntity entity = studentFeeSelectionDao.getById(feeSelectionId);
        if (!entity.getClassroomId().equals(classroomId)) {
            throw new GeneralException(HttpStatus.BAD_REQUEST, "FEE_SELECTION", "INVALID_SELECTION",
                    "fee_selection_not_for_classroom", feeSelectionId, classroomId);
        }
        
        entity.setActive(false);
        studentFeeSelectionDao.update(entity);
        
        logger.info("Teacher {} removed fee selection {} from classroom {}", teacherId, feeSelectionId, classroomId);
    }
    
    private StudentFeeSelection enrichSelectionWithDetails(StudentFeeSelection selection) {
        try {
            // Get student details
            var student = userService.getUserById(selection.getStudentId());
            selection.setStudentName(student.getName());
            
            // Get classroom details
            var classroom = classroomService.getClassroomById(selection.getClassroomId());
            selection.setClassName(classroom.getClassName());
            
            // Get fee structure details
            FeeStructure feeStructure = feeStructureService.getFeeStructureById(selection.getFeeStructureId());

            selection.setFeeStructureName(feeStructure.getDescription() != null ? feeStructure.getDescription() : "Fee Structure")
                     .setPaymentType(feeStructure.getPaymentType().name())
                     .setCurrency(feeStructure.getCurrency())
                     .setCountryCode(feeStructure.getCountryCode())
                     .setCountry(feeStructure.getCountry())
                     .setAmount(feeStructure.getFeeAmount())
                     .setDiscountValue(feeStructure.getDiscountPercentage() != null ? feeStructure.getDiscountPercentage() : BigDecimal.ZERO);

            // Calculate final amount after discount
            double finalAmount = feeStructure.getFeeAmount().doubleValue();
            if (feeStructure.getDiscountedAmount() != null) {
                finalAmount = feeStructure.getDiscountedAmount().doubleValue();
            }
            selection.setFinalAmount(BigDecimal.valueOf(finalAmount));
            
        } catch (Exception e) {
            logger.warn("Failed to enrich selection details for selection {}: {}", selection.getId(), e.getMessage());
            selection.setStudentName("Unknown Student")
                     .setClassName("Unknown Classroom")
                     .setFeeStructureName("Unknown Fee Structure");
        }
        
        return selection;
    }
}
