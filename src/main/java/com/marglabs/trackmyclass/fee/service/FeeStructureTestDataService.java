package com.marglabs.trackmyclass.fee.service;

import com.marglabs.trackmyclass.fee.model.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * Service for creating test fee structure data - remove in production
 */
@Service
public class FeeStructureTestDataService {

    @Autowired
    private FeeStructureService feeStructureService;

    public FeeStructure createSampleUSFeeStructure(String teacherId, String classroomId) {
        FeeStructureCreationRequest request = new FeeStructureCreationRequest();
        request.setClassroomId(classroomId);
        request.setFeeAmount(new BigDecimal("99.99"));
        request.setPaymentType(PaymentType.MONTHLY);
        request.setCountryCode("US");
        request.setDiscountPercentage(new BigDecimal("10.00")); // 10% discount
        request.setDescription("Monthly fee for US students with 10% discount");

        return feeStructureService.createFeeStructure(request, teacherId);
    }

    public FeeStructure createSampleIndiaFeeStructure(String teacherId, String classroomId) {
        FeeStructureCreationRequest request = new FeeStructureCreationRequest();
        request.setClassroomId(classroomId);
        request.setFeeAmount(new BigDecimal("2999.00"));
        request.setPaymentType(PaymentType.MONTHLY);
        request.setCountryCode("IN");
        request.setDiscountPercentage(new BigDecimal("15.00")); // 15% discount
        request.setDescription("Monthly fee for Indian students with 15% discount");

        return feeStructureService.createFeeStructure(request, teacherId);
    }

    public FeeStructure createSampleUKQuarterlyFeeStructure(String teacherId, String classroomId) {
        FeeStructureCreationRequest request = new FeeStructureCreationRequest();
        request.setClassroomId(classroomId);
        request.setFeeAmount(new BigDecimal("249.99"));
        request.setPaymentType(PaymentType.QUARTERLY);
        request.setCountryCode("GB");
        request.setDiscountPercentage(new BigDecimal("5.00")); // 5% discount
        request.setDescription("Quarterly fee for UK students with 5% discount");

        return feeStructureService.createFeeStructure(request, teacherId);
    }

    public FeeStructure createSampleOneTimeFeeStructure(String teacherId, String classroomId) {
        FeeStructureCreationRequest request = new FeeStructureCreationRequest();
        request.setClassroomId(classroomId);
        request.setFeeAmount(new BigDecimal("499.00"));
        request.setPaymentType(PaymentType.ONE_TIME);
        request.setCountryCode("US");
        // No discount for one-time fee
        request.setDescription("One-time enrollment fee");

        return feeStructureService.createFeeStructure(request, teacherId);
    }

    public FeeStructure createSampleYearlyFeeStructure(String teacherId, String classroomId) {
        FeeStructureCreationRequest request = new FeeStructureCreationRequest();
        request.setClassroomId(classroomId);
        request.setFeeAmount(new BigDecimal("999.99"));
        request.setPaymentType(PaymentType.YEARLY);
        request.setCountryCode("DE");
        request.setDiscountPercentage(new BigDecimal("20.00")); // 20% discount for yearly payment
        request.setDescription("Annual fee for European students with 20% yearly discount");

        return feeStructureService.createFeeStructure(request, teacherId);
    }
}
