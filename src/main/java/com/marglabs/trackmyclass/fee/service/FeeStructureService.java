package com.marglabs.trackmyclass.fee.service;

import com.marglabs.common.rest.error.EntityNotFoundException;
import com.marglabs.common.rest.error.GeneralException;
import com.marglabs.common.utils.IdGenerator;
import com.marglabs.trackmyclass.classroom.service.ClassroomService;
import com.marglabs.trackmyclass.country.service.CountryService;
import com.marglabs.trackmyclass.fee.dao.FeeStructureDao;
import com.marglabs.trackmyclass.fee.entity.FeeStructureEntity;
import com.marglabs.trackmyclass.fee.mapper.FeeStructureMapper;
import com.marglabs.trackmyclass.fee.model.*;
import com.marglabs.trackmyclass.user.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class FeeStructureService {
    private static final Logger logger = LoggerFactory.getLogger(FeeStructureService.class);

    @Autowired
    private FeeStructureDao feeStructureDao;

    @Autowired
    private FeeStructureMapper mapper;

    @Autowired
    private IdGenerator idGenerator;

    @Autowired
    private ClassroomService classroomService;

    @Autowired
    private UserService userService;

    @Autowired
    private CountryService countryService;

    /**
     * Create a new fee structure
     */
    @Transactional
    public FeeStructure createFeeStructure(FeeStructureCreationRequest request, String teacherId) {
        logger.info("Creating fee structure for teacher: {}, classroom: {}, country: {}",
                   teacherId, request.getClassroomId(), request.getCountryCode());

        // Validate the request
        validateFeeStructureRequest(request, teacherId);

        // Verify classroom exists and teacher has access
        var classroom = classroomService.getClassroomById(request.getClassroomId());
        if (!classroom.getTeacherId().equals(teacherId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "FEE_STRUCTURE", "UNAUTHORIZED_CLASSROOM_ACCESS",
                    "teacher_not_authorized_for_classroom", request.getClassroomId());
        }

        // Get country information from countryCode
        var countryOptional = countryService.getCountryByIsoCode(request.getCountryCode());
        if (countryOptional.isEmpty()) {
            throw new GeneralException(HttpStatus.BAD_REQUEST, "FEE_STRUCTURE", "INVALID_COUNTRY_CODE",
                    "invalid_country_code", request.getCountryCode());
        }
        var country = countryOptional.get();

        // Check if fee structure already exists for this combination
        if (feeStructureDao.existsByClassroomIdAndCountryCodeAndPaymentType(
                request.getClassroomId(), request.getCountryCode().toUpperCase(), request.getPaymentType())) {
            throw new GeneralException(HttpStatus.CONFLICT, "FEE_STRUCTURE", "DUPLICATE_FEE_STRUCTURE",
                    "fee_structure_already_exists", request.getClassroomId(),
                    request.getCountryCode(), request.getPaymentType().name());
        }

        // Calculate discounted amount if discount is provided
        BigDecimal discountPercentage = request.getDiscountPercentage() != null ? request.getDiscountPercentage() : BigDecimal.ZERO;
        BigDecimal discountedAmount = calculateDiscountedAmount(request.getFeeAmount(), discountPercentage);

        FeeStructure feeStructure = new FeeStructure()
                .setId(idGenerator.generateId())
                .setClassroomId(request.getClassroomId())
                .setTeacherId(teacherId)
                .setFeeAmount(request.getFeeAmount())
                .setCurrency(country.getCurrencyCode())
                .setPaymentType(request.getPaymentType())
                .setCountry(country.getName())
                .setCountryCode(country.getIsoCode().toUpperCase())
                .setStatus(FeeStatus.ACTIVE)
                .setDiscountPercentage(discountPercentage)
                .setDiscountedAmount(discountedAmount)
                .setDescription(request.getDescription())
                .setActive(true);

        FeeStructureEntity entity = mapper.toEntity(feeStructure);
        entity = feeStructureDao.create(entity);

        return enrichFeeStructureWithNames(mapper.toDto(entity));
    }

    /**
     * Update an existing fee structure
     */
    @Transactional
    public FeeStructure updateFeeStructure(String feeStructureId, FeeStructureUpdateRequest request, String teacherId) {
        FeeStructureEntity existingFeeStructure = feeStructureDao.getById(feeStructureId);

        // Verify teacher has access
        if (!existingFeeStructure.getTeacherId().equals(teacherId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "FEE_STRUCTURE", "UNAUTHORIZED_FEE_STRUCTURE_ACCESS",
                    "teacher_not_authorized_for_fee_structure", feeStructureId);
        }

        // Handle country code change - get new country information if countryCode is being updated
        String newCountryCode = request.getCountryCode() != null ? request.getCountryCode().toUpperCase() : existingFeeStructure.getCountryCode();
        String newCountryName = existingFeeStructure.getCountry();
        String newCurrency = existingFeeStructure.getCurrency();

        if (request.getCountryCode() != null && !request.getCountryCode().equalsIgnoreCase(existingFeeStructure.getCountryCode())) {
            var countryOptional = countryService.getCountryByIsoCode(request.getCountryCode());
            if (countryOptional.isEmpty()) {
                throw new GeneralException(HttpStatus.BAD_REQUEST, "FEE_STRUCTURE", "INVALID_COUNTRY_CODE",
                        "invalid_country_code", request.getCountryCode());
            }
            var country = countryOptional.get();
            newCountryName = country.getName();
            newCurrency = country.getCurrencyCode();
        }

        // Check for duplicate if payment type or country code is being changed
        if ((request.getPaymentType() != null && !request.getPaymentType().equals(existingFeeStructure.getPaymentType())) ||
            (request.getCountryCode() != null && !request.getCountryCode().equalsIgnoreCase(existingFeeStructure.getCountryCode()))) {

            PaymentType newPaymentType = request.getPaymentType() != null ? request.getPaymentType() : existingFeeStructure.getPaymentType();

            if (feeStructureDao.existsByClassroomIdAndCountryCodeAndPaymentTypeExcluding(
                    existingFeeStructure.getClassroomId(), newCountryCode, newPaymentType, feeStructureId)) {
                throw new GeneralException(HttpStatus.CONFLICT, "FEE_STRUCTURE", "DUPLICATE_FEE_STRUCTURE",
                        "fee_structure_already_exists", existingFeeStructure.getClassroomId(),
                        newCountryCode, newPaymentType.name());
            }
        }

        // Update fields
        if (request.getFeeAmount() != null) {
            existingFeeStructure.setFeeAmount(request.getFeeAmount());
        }
        if (request.getPaymentType() != null) {
            existingFeeStructure.setPaymentType(request.getPaymentType());
        }
        if (request.getCountryCode() != null) {
            existingFeeStructure.setCountryCode(newCountryCode);
            existingFeeStructure.setCountry(newCountryName);
            existingFeeStructure.setCurrency(newCurrency);
        }
        if (request.getDiscountPercentage() != null) {
            existingFeeStructure.setDiscountPercentage(request.getDiscountPercentage());
        }

        // Recalculate discounted amount if fee amount or discount changed
        if (request.getFeeAmount() != null || request.getDiscountPercentage() != null) {
            BigDecimal currentDiscount = existingFeeStructure.getDiscountPercentage() != null ?
                existingFeeStructure.getDiscountPercentage() : BigDecimal.ZERO;
            BigDecimal discountedAmount = calculateDiscountedAmount(existingFeeStructure.getFeeAmount(), currentDiscount);
            existingFeeStructure.setDiscountPercentage(discountedAmount);
        }
        if (request.getStatus() != null) {
            existingFeeStructure.setStatus(request.getStatus());
        }
        if (request.getDescription() != null) {
            existingFeeStructure.setDescription(request.getDescription());
        }

        FeeStructureEntity updatedEntity = feeStructureDao.update(existingFeeStructure);
        return enrichFeeStructureWithNames(mapper.toDto(updatedEntity));
    }

    /**
     * Get fee structure by ID
     */
    public FeeStructure getFeeStructureById(String feeStructureId) {
        FeeStructureEntity entity = feeStructureDao.getById(feeStructureId);
        return enrichFeeStructureWithNames(mapper.toDto(entity));
    }

    /**
     * Get fee structures by classroom with pagination
     */
    public Page<FeeStructure> getFeeStructuresByClassroomPaginated(String classroomId, Pageable pageable) {
        Page<FeeStructureEntity> entities = feeStructureDao.getByClassroomIdPaginated(classroomId, pageable);
        return entities.map(entity -> enrichFeeStructureWithNames(mapper.toDto(entity)));
    }

    /**
     * Get fee structures by classroom
     */
    public List<FeeStructure> getFeeStructuresByClassroom(String classroomId) {
        List<FeeStructureEntity> entities = feeStructureDao.getByClassroomId(classroomId);
        return entities.stream()
                .map(entity -> enrichFeeStructureWithNames(mapper.toDto(entity)))
                .collect(Collectors.toList());
    }

    /**
     * Get fee structures by teacher with pagination
     */
    public Page<FeeStructure> getFeeStructuresByTeacherPaginated(String teacherId, Pageable pageable) {
        Page<FeeStructureEntity> entities = feeStructureDao.getByTeacherIdPaginated(teacherId, pageable);
        return entities.map(entity -> enrichFeeStructureWithNames(mapper.toDto(entity)));
    }

    /**
     * Get fee structures by country
     */
    public List<FeeStructure> getFeeStructuresByCountry(String countryCode) {
        List<FeeStructureEntity> entities = feeStructureDao.getByCountryCode(countryCode.toUpperCase());
        return entities.stream()
                .map(entity -> enrichFeeStructureWithNames(mapper.toDto(entity)))
                .collect(Collectors.toList());
    }

    /**
     * Get fee structures by classroom and country
     */
    public List<FeeStructure> getFeeStructuresByClassroomAndCountry(String classroomId, String countryCode) {
        List<FeeStructureEntity> entities = feeStructureDao.getByClassroomIdAndCountryCode(classroomId, countryCode.toUpperCase());
        return entities.stream()
                .map(entity -> enrichFeeStructureWithNames(mapper.toDto(entity)))
                .collect(Collectors.toList());
    }

    /**
     * Search fee structures with multiple criteria
     */
    public Page<FeeStructure> searchFeeStructures(String classroomId, String countryCode,
                                                 PaymentType paymentType, FeeStatus status,
                                                 Pageable pageable) {
        String normalizedCountryCode = countryCode != null ? countryCode.toUpperCase() : null;
        Page<FeeStructureEntity> entities = feeStructureDao.findByCriteria(
                classroomId, normalizedCountryCode, paymentType, status, pageable);
        return entities.map(entity -> enrichFeeStructureWithNames(mapper.toDto(entity)));
    }

    /**
     * Get distinct countries
     */
    public List<String> getDistinctCountries() {
        return feeStructureDao.getDistinctCountries();
    }

    /**
     * Get distinct country codes
     */
    public List<String> getDistinctCountryCodes() {
        return feeStructureDao.getDistinctCountryCodes();
    }

    /**
     * Get distinct currencies
     */
    public List<String> getDistinctCurrencies() {
        return feeStructureDao.getDistinctCurrencies();
    }

    /**
     * Deactivate fee structure
     */
    @Transactional
    public FeeStructure deactivateFeeStructure(String feeStructureId, String teacherId) {
        FeeStructureEntity existingFeeStructure = feeStructureDao.getById(feeStructureId);

        // Verify teacher has access
        if (!existingFeeStructure.getTeacherId().equals(teacherId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "FEE_STRUCTURE", "UNAUTHORIZED_FEE_STRUCTURE_ACCESS",
                    "teacher_not_authorized_for_fee_structure", feeStructureId);
        }

        FeeStructureEntity deactivatedEntity = feeStructureDao.deactivate(feeStructureId);
        return enrichFeeStructureWithNames(mapper.toDto(deactivatedEntity));
    }

    /**
     * Validate fee structure request
     */
    private void validateFeeStructureRequest(FeeStructureCreationRequest request, String teacherId) {
        // Validate country code format
        if (!request.getCountryCode().matches("^[A-Za-z]{2}$")) {
            throw new GeneralException(HttpStatus.BAD_REQUEST, "FEE_STRUCTURE", "INVALID_COUNTRY_CODE",
                    "invalid_country_code_format", request.getCountryCode());
        }

        // Validate fee amount
        if (request.getFeeAmount().compareTo(java.math.BigDecimal.ZERO) <= 0) {
            throw new GeneralException(HttpStatus.BAD_REQUEST, "FEE_STRUCTURE", "INVALID_FEE_AMOUNT",
                    "fee_amount_must_be_positive");
        }

        // Validate fee amount scale (max 2 decimal places)
        if (request.getFeeAmount().scale() > 2) {
            throw new GeneralException(HttpStatus.BAD_REQUEST, "FEE_STRUCTURE", "INVALID_FEE_AMOUNT_SCALE",
                    "fee_amount_max_two_decimal_places");
        }
    }

    /**
     * Calculate discounted amount based on original amount and discount percentage
     */
    private BigDecimal calculateDiscountedAmount(BigDecimal originalAmount, BigDecimal discountPercentage) {
        if (originalAmount == null || discountPercentage == null || discountPercentage.compareTo(BigDecimal.ZERO) == 0) {
            return originalAmount;
        }

        BigDecimal discountAmount = originalAmount.multiply(discountPercentage).divide(new BigDecimal("100"), 2, java.math.RoundingMode.HALF_UP);
        return originalAmount.subtract(discountAmount);
    }

    /**
     * Enrich fee structure with classroom and teacher names
     */
    private FeeStructure enrichFeeStructureWithNames(FeeStructure feeStructure) {
        try {
            var classroom = classroomService.getClassroomById(feeStructure.getClassroomId());
            feeStructure.setClassroomName(classroom.getClassName());
            feeStructure.setTeacherName(classroom.getTeacherName());
        } catch (Exception e) {
            logger.warn("Failed to enrich fee structure with names: {}", e.getMessage());
            feeStructure.setClassroomName("Unknown Classroom");
            feeStructure.setTeacherName("Unknown Teacher");
        }

        // Ensure discounted amount is calculated if not already set
        if (feeStructure.getDiscountedAmount() == null) {
            BigDecimal discountPercentage = feeStructure.getDiscountPercentage() != null ?
                feeStructure.getDiscountPercentage() : BigDecimal.ZERO;
            feeStructure.setDiscountedAmount(calculateDiscountedAmount(feeStructure.getFeeAmount(), discountPercentage));
        }

        return feeStructure;
    }
}
