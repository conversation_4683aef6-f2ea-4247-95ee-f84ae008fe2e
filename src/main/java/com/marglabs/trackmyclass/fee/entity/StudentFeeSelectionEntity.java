package com.marglabs.trackmyclass.fee.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.Instant;

@Entity
@Table(name = "student_fee_selections", 
       uniqueConstraints = @UniqueConstraint(columnNames = {"student_id", "classroom_id"}))
@Data
@Accessors(chain = true)
public class StudentFeeSelectionEntity {
    
    @Id
    @Column(name = "id")
    private String id;
    
    @Column(name = "student_id", nullable = false)
    private String studentId;
    
    @Column(name = "classroom_id", nullable = false)
    private String classroomId;
    
    @Column(name = "fee_structure_id", nullable = false)
    private String feeStructureId;
    
    @Column(name = "is_active", nullable = false)
    private boolean isActive = true;
    
    @CreationTimestamp
    @Column(name = "created_date", nullable = false, updatable = false)
    private Instant createdDate;
    
    @UpdateTimestamp
    @Column(name = "updated_date", nullable = false)
    private Instant updatedDate;
}
