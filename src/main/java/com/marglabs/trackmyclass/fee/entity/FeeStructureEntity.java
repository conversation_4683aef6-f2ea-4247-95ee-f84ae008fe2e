package com.marglabs.trackmyclass.fee.entity;

import com.marglabs.trackmyclass.fee.model.FeeStatus;
import com.marglabs.trackmyclass.fee.model.PaymentType;
import jakarta.persistence.*;
import lombok.Data;

import java.math.BigDecimal;

@Entity
@Table(name = "fee_structures",
       uniqueConstraints = {
           @UniqueConstraint(columnNames = {"classroom_id", "country_code", "payment_type","status"})
       },
       indexes = {
           @Index(name = "idx_fee_classroom", columnList = "classroom_id"),
           @Index(name = "idx_fee_teacher", columnList = "teacher_id"),
           @Index(name = "idx_fee_country", columnList = "country_code"),
           @Index(name = "idx_fee_payment_type", columnList = "payment_type")
       })
@Data
public class FeeStructureEntity {
    @Id
    private String id;

    @Column(name = "classroom_id", nullable = false)
    private String classroomId;

    @Column(name = "teacher_id", nullable = false)
    private String teacherId;

    @Column(name = "fee_amount", nullable = false, precision = 19, scale = 2)
    private BigDecimal feeAmount;

    @Column(name = "currency", nullable = false, length = 3)
    private String currency;

    @Enumerated(EnumType.STRING)
    @Column(name = "payment_type", nullable = false)
    private PaymentType paymentType;

    @Column(name = "country", nullable = false, length = 100)
    private String country;

    @Column(name = "country_code", nullable = false, length = 2)
    private String countryCode;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private FeeStatus status;

    @Column(name = "discount_percentage", precision = 5, scale = 2)
    private BigDecimal discountPercentage;

    @Column(name = "description", length = 500)
    private String description;

    @Column(name = "created_date")
    private long createdDate;

    @Column(name = "updated_date")
    private long updatedDate;
}
