package com.marglabs.trackmyclass.address.model;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class Address {
    private String id;
    private String userId; // Reference to User entity (for teachers) or Student entity (for students)
    private String address;
    private String city;
    private String state;
    private String country;
    private String zipCode;
    private long createdDate;
    private long updatedDate;
}
