package com.marglabs.trackmyclass.address.mapper;

import com.marglabs.trackmyclass.address.entity.AddressEntity;
import com.marglabs.trackmyclass.address.model.Address;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface AddressMapper {
    
    @Mapping(target = "createdDate", expression = "java(entity.getCreatedDate() != null ? entity.getCreatedDate().toEpochMilli() : 0)")
    @Mapping(target = "updatedDate", expression = "java(entity.getUpdatedDate() != null ? entity.getUpdatedDate().toEpochMilli() : 0)")
    Address toDto(AddressEntity entity);
    
    @Mapping(target = "createdDate", ignore = true) // Handled by @CreationTimestamp
    @Mapping(target = "updatedDate", ignore = true) // Handled by @UpdateTimestamp
    AddressEntity toEntity(Address dto);
}
