package com.marglabs.trackmyclass.address.service;

import com.marglabs.common.rest.error.GeneralException;
import com.marglabs.trackmyclass.address.dao.AddressDao;
import com.marglabs.trackmyclass.address.entity.AddressEntity;
import com.marglabs.trackmyclass.address.mapper.AddressMapper;
import com.marglabs.trackmyclass.address.model.Address;
import com.marglabs.trackmyclass.address.model.AddressCreationRequest;
import com.marglabs.trackmyclass.address.model.AddressUpdateRequest;
import com.marglabs.common.utils.IdGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class AddressService {
    private static final Logger logger = LoggerFactory.getLogger(AddressService.class);

    @Autowired
    private AddressDao addressDao;

    @Autowired
    private AddressMapper mapper;

    @Autowired
    private IdGenerator idGenerator;

    /**
     * Create a new address
     */
    @Transactional
    public Address createAddress(AddressCreationRequest request, String userId) {
        logger.info("Creating address for user {}", userId);

        // Create address entity
        AddressEntity addressEntity = new AddressEntity()
                .setId(idGenerator.generateId())
                .setUserId(userId)
                .setAddress(request.getAddress())
                .setCity(request.getCity())
                .setState(request.getState())
                .setCountry(request.getCountry())
                .setZipCode(request.getZipCode());

        AddressEntity savedEntity = addressDao.create(addressEntity);
        logger.info("Successfully created address {} for user {}", savedEntity.getId(), userId);

        return mapper.toDto(savedEntity);
    }

    /**
     * Update an existing address
     */
    @Transactional
    public Address updateAddress(String addressId, AddressUpdateRequest request, String userId) {
        logger.info("Updating address {} for user {}", addressId, userId);

        AddressEntity existingAddress = addressDao.getById(addressId);

        // Verify the address belongs to the user
        if (!existingAddress.getUserId().equals(userId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "ADDRESS", "UNAUTHORIZED_ADDRESS_ACCESS",
                    "user_not_authorized_for_address", addressId);
        }

        // Update fields
        updateAddressFields(existingAddress, request);

        AddressEntity updatedEntity = addressDao.update(existingAddress);
        logger.info("Successfully updated address {}", addressId);

        return mapper.toDto(updatedEntity);
    }

    /**
     * Get all addresses for a user
     */
    public List<Address> getAllAddressesForUser(String userId) {
        List<AddressEntity> addresses = addressDao.getByUserId(userId);
        return addresses.stream()
                .map(mapper::toDto)
                .collect(Collectors.toList());
    }

    /**
     * Delete an address
     */
    @Transactional
    public void deleteAddress(String addressId, String userId) {
        logger.info("Deleting address {} for user {}", addressId, userId);

        AddressEntity address = addressDao.getById(addressId);

        // Verify the address belongs to the user
        if (!address.getUserId().equals(userId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "ADDRESS", "UNAUTHORIZED_ADDRESS_ACCESS",
                    "user_not_authorized_for_address", addressId);
        }

        addressDao.deleteById(addressId);
        logger.info("Successfully deleted address {}", addressId);
    }

    // Private helper methods

    private void updateAddressFields(AddressEntity address, AddressUpdateRequest request) {
        if (request.getAddress() != null) {
            address.setAddress(request.getAddress());
        }
        if (request.getCity() != null) {
            address.setCity(request.getCity());
        }
        if (request.getState() != null) {
            address.setState(request.getState());
        }
        if (request.getCountry() != null) {
            address.setCountry(request.getCountry());
        }
        if (request.getZipCode() != null) {
            address.setZipCode(request.getZipCode());
        }
    }
}
