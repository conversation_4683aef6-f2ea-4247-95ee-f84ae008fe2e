package com.marglabs.trackmyclass.address.dao;

import com.marglabs.common.rest.error.EntityNotFoundException;
import com.marglabs.trackmyclass.address.entity.AddressEntity;
import com.marglabs.trackmyclass.address.repository.AddressRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Optional;

@Component
public class AddressDao {
    private static final Logger logger = LoggerFactory.getLogger(AddressDao.class);

    @Autowired
    private AddressRepository repository;

    public AddressEntity create(AddressEntity entity) {
        return repository.save(entity);
    }

    public AddressEntity update(AddressEntity entity) {
        return repository.save(entity);
    }

    public AddressEntity getById(String id) {
        Optional<AddressEntity> entity = repository.findById(id);
        if (entity.isPresent()) {
            return entity.get();
        }
        throw new EntityNotFoundException(HttpStatus.NOT_FOUND, "ADDRESS", "ADDRESS_NOT_FOUND",
                "address_not_found_details", id);
    }

    public List<AddressEntity> getByUserId(String userId) {
        return repository.findByUserId(userId);
    }

    public long countByUserId(String userId) {
        return repository.countByUserId(userId);
    }

    public List<AddressEntity> getByCountry(String country) {
        return repository.findByCountry(country);
    }

    public List<AddressEntity> getByCityAndCountry(String city, String country) {
        return repository.findByCityAndCountry(city, country);
    }

    public void deleteById(String id) {
        repository.deleteById(id);
    }

    public List<AddressEntity> getAll() {
        return repository.findAll();
    }
}
