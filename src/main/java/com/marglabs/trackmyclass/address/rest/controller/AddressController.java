package com.marglabs.trackmyclass.address.rest.controller;

import com.marglabs.trackmyclass.address.model.Address;
import com.marglabs.trackmyclass.address.model.AddressCreationRequest;
import com.marglabs.trackmyclass.address.model.AddressUpdateRequest;
import com.marglabs.trackmyclass.address.service.AddressService;
import com.marglabs.trackmyclass.user.model.User;
import com.marglabs.trackmyclass.user.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api/addressManagement/v1")
@Tag(name = "Address Management", description = "APIs for managing student and teacher addresses")
public class AddressController {

    @Autowired
    private AddressService addressService;

    @Autowired
    private UserService userService;

    @Operation(summary = "Create address", description = "Create a new address for the authenticated user")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Address created successfully",
                content = @Content(schema = @Schema(implementation = Address.class))),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @PostMapping(value = "/addresses", consumes = "application/json", produces = "application/json")
    @ResponseStatus(HttpStatus.CREATED)
    public Address createAddress(
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal,
            @Parameter(description = "Address details") @Valid @RequestBody AddressCreationRequest request) {

        // Get user from Cognito token
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());

        return addressService.createAddress(request, user.getId());
    }

    @Operation(summary = "Update address", description = "Update an existing address")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Address updated successfully",
                content = @Content(schema = @Schema(implementation = Address.class))),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Not authorized to update this address"),
        @ApiResponse(responseCode = "404", description = "Address not found")
    })
    @PutMapping(value = "/addresses/{addressId}", consumes = "application/json", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public Address updateAddress(
            @Parameter(description = "Address ID") @PathVariable String addressId,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal,
            @Parameter(description = "Address update details") @Valid @RequestBody AddressUpdateRequest request) {

        // Get user from Cognito token
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());

        return addressService.updateAddress(addressId, request, user.getId());
    }



    @Operation(summary = "Get all addresses", description = "Get all addresses for the authenticated user")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Addresses retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/addresses", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<Address> getAllAddresses(
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {

        // Get user from Cognito token
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());

        return addressService.getAllAddressesForUser(user.getId());
    }



    @Operation(summary = "Delete address", description = "Delete an address")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "Address deleted successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Not authorized to delete this address"),
        @ApiResponse(responseCode = "404", description = "Address not found")
    })
    @DeleteMapping(value = "/addresses/{addressId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void deleteAddress(
            @Parameter(description = "Address ID") @PathVariable String addressId,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {

        // Get user from Cognito token
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());

        addressService.deleteAddress(addressId, user.getId());
    }
}
