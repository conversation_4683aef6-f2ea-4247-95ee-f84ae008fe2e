package com.marglabs.trackmyclass.address.repository;

import com.marglabs.trackmyclass.address.entity.AddressEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.data.rest.core.annotation.RepositoryRestResource;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
@RepositoryRestResource(exported = false)
public interface AddressRepository extends JpaRepository<AddressEntity, String> {

    // Find all addresses for a user
    List<AddressEntity> findByUserId(String userId);

    // Count addresses for a user
    long countByUserId(String userId);

    // Find addresses by country
    List<AddressEntity> findByCountry(String country);

    // Find addresses by city and country
    List<AddressEntity> findByCityAndCountry(String city, String country);
}
