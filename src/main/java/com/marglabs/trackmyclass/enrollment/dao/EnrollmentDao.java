package com.marglabs.trackmyclass.enrollment.dao;

import com.marglabs.common.rest.error.EntityNotFoundException;
import com.marglabs.trackmyclass.enrollment.entity.EnrollmentEntity;
import com.marglabs.trackmyclass.enrollment.repository.EnrollmentRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

@Component
public class EnrollmentDao {
    @Autowired
    private EnrollmentRepository repository;

    public EnrollmentEntity create(EnrollmentEntity entity) {
        entity.setCreatedDate(Instant.now().toEpochMilli());
        entity.setStatus("ACTIVE");
        return repository.save(entity);
    }

    public EnrollmentEntity getById(String id) {
        Optional<EnrollmentEntity> entity = repository.findById(id);
        if (entity.isPresent()) {
            return entity.get();
        }
        throw new EntityNotFoundException(HttpStatus.NOT_FOUND, "ENROLLMENT", "ENROLLMENT_NOT_FOUND", "enrollment_not_found_details", id);
    }

    public List<EnrollmentEntity> getByClassroomId(String classroomId) {
        return repository.findByClassroomId(classroomId);
    }

    public List<EnrollmentEntity> getByUserId(String userId) {
        return repository.findByUserId(userId);
    }

    public List<EnrollmentEntity> getByUserIdAndRole(String userId, String role) {
        return repository.findByUserIdAndRole(userId, role);
    }

    public List<EnrollmentEntity> getByClassroomIdAndRole(String classroomId, String role) {
        return repository.findByClassroomIdAndRole(classroomId, role);
    }

    public List<EnrollmentEntity> getByClassroomIdAndRoleAndStatus(String classroomId, String role, String status) {
        return repository.findByClassroomIdAndRoleAndStatus(classroomId, role, status);
    }

    public Optional<EnrollmentEntity> findByClassroomIdAndUserIdAndRole(String classroomId, String userId, String role) {
        return repository.findByClassroomIdAndUserIdAndRole(classroomId, userId, role);
    }

    public Optional<EnrollmentEntity> findByClassroomIdAndUserIdAndRoleAndStatus(String classroomId, String userId, String role, String status) {
        return repository.findByClassroomIdAndUserIdAndRoleAndStatus(classroomId, userId, role, status);
    }

    public boolean existsByClassroomIdAndUserIdAndRole(String classroomId, String userId, String role) {
        return repository.existsByClassroomIdAndUserIdAndRoleAndStatus(classroomId, userId, role, "ACTIVE");
    }

    public boolean existsByClassroomIdAndUserIdAndRoleAndStatus(String classroomId, String userId, String role, String status) {
        return repository.existsByClassroomIdAndUserIdAndRoleAndStatus(classroomId, userId, role, status);
    }

    public int countByClassroomId(String classroomId) {
        return repository.countByClassroomId(classroomId);
    }
    
    public long countByClassroomIdLong(String classroomId) {
        return (long) repository.countByClassroomId(classroomId);
    }

    public EnrollmentEntity update(EnrollmentEntity entity) {
        EnrollmentEntity existingEntity = getById(entity.getId());
        entity.setCreatedDate(existingEntity.getCreatedDate());
        return repository.save(entity);
    }

    public void deleteById(String id) {
        try {
            repository.deleteById(id);
        } catch (EmptyResultDataAccessException e) {
            throw new EntityNotFoundException(HttpStatus.NOT_FOUND, "ENROLLMENT", "ENROLLMENT_NOT_FOUND", "enrollment_not_found_details", id);
        }
    }
    
    public long countByTeacherId(String teacherId) {
        return repository.countByTeacherId(teacherId);
    }
    
    public long countByStudentId(String studentId) {
        return repository.countByUserIdAndRole(studentId, "STUDENT");
    }
}
