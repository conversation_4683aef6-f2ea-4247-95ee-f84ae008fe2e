package com.marglabs.trackmyclass.enrollment.model;

import com.marglabs.trackmyclass.user.model.RoleType;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class TeacherEnrollmentRequest {

    @NotBlank(message = "User ID is required")
    private String userId;

    // Either classroomId or joinCode must be provided
    private String classroomId;
    private String joinCode;

    @NotNull(message = "Role is required")
    private RoleType role; // STUDENT or PARENT
}
