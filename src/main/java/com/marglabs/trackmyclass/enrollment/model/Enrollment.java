package com.marglabs.trackmyclass.enrollment.model;

import com.marglabs.trackmyclass.user.model.RoleType;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class Enrollment {
    private String id;
    private String classroomId;
    private String userId;
    private RoleType role; // STUDENT or PARENT
    private long createdDate;
    private EnrollmentStatus status;
    
    // Transient fields for display
    private String classroomName;
    private String userName;
    private String phoneNumber;
}
