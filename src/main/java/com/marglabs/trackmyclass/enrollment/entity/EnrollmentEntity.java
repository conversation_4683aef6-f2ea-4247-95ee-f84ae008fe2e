package com.marglabs.trackmyclass.enrollment.entity;

import jakarta.persistence.*;
import lombok.Data;

@Entity
@Table(name = "enrollments", uniqueConstraints = {
    @UniqueConstraint(columnNames = {"classroom_id", "user_id", "role"})
})
@Data
public class EnrollmentEntity {
    @Id
    private String id;
    
    @Column(name = "classroom_id", nullable = false)
    private String classroomId;
    
    @Column(name = "user_id", nullable = false)
    private String userId;
    
    @Column(nullable = false)
    private String role; // STUDENT or PARENT
    
    @Column(name = "created_date")
    private long createdDate;
    
    @Column(name = "status")
    private String status; // ACTIVE, INACTIVE
}
