package com.marglabs.trackmyclass.enrollment.rest.controller;

import com.marglabs.trackmyclass.enrollment.model.Enrollment;
import com.marglabs.trackmyclass.enrollment.model.EnrollmentStatus;
import com.marglabs.trackmyclass.enrollment.model.TeacherEnrollmentRequest;
import com.marglabs.trackmyclass.enrollment.service.EnrollmentService;
import com.marglabs.trackmyclass.user.model.RoleType;
import com.marglabs.trackmyclass.user.model.User;
import com.marglabs.trackmyclass.user.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/api/enrollmentManagement/v1")
@Tag(name = "Enrollment Management", description = "API for managing classroom enrollments")
public class EnrollmentController {
    @Autowired
    private EnrollmentService enrollmentService;

    @Autowired
    private UserService userService;

    @Operation(summary = "Join a classroom with code", description = "Enrolls the current user in a classroom using a join code")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Successfully enrolled in classroom",
                content = @Content(schema = @Schema(implementation = Enrollment.class))),
        @ApiResponse(responseCode = "400", description = "Invalid input or classroom is full"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "404", description = "Classroom not found")
    })
    @PostMapping(value = "/join/{joinCode}", produces = "application/json")
    @ResponseStatus(HttpStatus.CREATED)
    public Enrollment joinClassroom(
            @AuthenticationPrincipal Jwt userPrincipal,
            @Parameter(description = "Classroom join code") @PathVariable String joinCode,
            @Parameter(description = "Role to join as (STUDENT or PARENT)") @RequestParam RoleType role) {

        // Get user from Cognito token
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());

        // Verify user has the role they're trying to enroll as
        boolean hasRole = user.getRoles().stream()
                .anyMatch(userRole -> userRole.getRole() == role);

        if (!hasRole) {
            throw new AccessDeniedException("You must have the " + role + " role to join as a " + role);
        }

        return enrollmentService.enrollUserWithJoinCode(joinCode, user.getId(), role);
    }

    @Operation(summary = "Leave a classroom", description = "Removes the current user from a classroom")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "Successfully left classroom"),
        @ApiResponse(responseCode = "400", description = "User not enrolled in classroom"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "404", description = "Classroom not found")
    })
    @DeleteMapping(value = "/classrooms/{classroomId}/leave")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void leaveClassroom(
            @AuthenticationPrincipal Jwt userPrincipal,
            @Parameter(description = "Classroom ID") @PathVariable String classroomId,
            @Parameter(description = "Role to leave as (STUDENT or PARENT)") @RequestParam RoleType role) {

        // Get user from Cognito token
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());

        enrollmentService.unenrollUser(classroomId, user.getId(), role);
    }

    @Operation(summary = "Get my enrollments", description = "Returns all classrooms the current user is enrolled in")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successful operation"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/my-enrollments", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<Enrollment> getMyEnrollments(
            @AuthenticationPrincipal Jwt userPrincipal,
            @Parameter(description = "Filter by role (optional)") @RequestParam(required = false) RoleType role) {

        // Get user from Cognito token
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());

        if (role != null) {
            return enrollmentService.getEnrollmentsByUserIdAndRole(user.getId(), role);
        } else {
            return enrollmentService.getEnrollmentsByUserId(user.getId());
        }
    }

    @Operation(summary = "Get classroom enrollments", description = "Returns all enrollments for a classroom")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successful operation"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Forbidden - only the teacher of the classroom can view enrollments"),
        @ApiResponse(responseCode = "404", description = "Classroom not found")
    })
    @GetMapping(value = "/classrooms/{classroomId}/enrollments", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<Enrollment> getClassroomEnrollments(
            @AuthenticationPrincipal Jwt userPrincipal,
            @Parameter(description = "Classroom ID") @PathVariable String classroomId) {

        // Get user from Cognito token
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());

        // TODO: Verify user is the teacher of the classroom or an admin

        return enrollmentService.getEnrollmentsByClassroomId(classroomId);
    }

    @Operation(summary = "Update enrollment status", description = "Updates the status of an enrollment (teacher only)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Status updated successfully",
                content = @Content(schema = @Schema(implementation = Enrollment.class))),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Forbidden - only the teacher of the classroom can update enrollment status"),
        @ApiResponse(responseCode = "404", description = "Enrollment not found")
    })
    @PatchMapping(value = "/enrollments/{id}/status/{status}", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public Enrollment updateEnrollmentStatus(
            @AuthenticationPrincipal Jwt userPrincipal,
            @Parameter(description = "Enrollment ID") @PathVariable String id,
            @Parameter(description = "New status") @PathVariable EnrollmentStatus status) {

        // Get user from Cognito token
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());

        // TODO: Verify user is the teacher of the classroom or an admin

        return enrollmentService.updateEnrollmentStatus(id, status);
    }

    @Operation(summary = "Teacher enrolls a student", description = "Allows teachers to directly enroll students in their classrooms")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Student enrolled successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request data"),
        @ApiResponse(responseCode = "403", description = "Not authorized to enroll in this classroom"),
        @ApiResponse(responseCode = "409", description = "Student already enrolled or classroom full"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @PostMapping(value = "/teacher/enroll", produces = "application/json")
    @ResponseStatus(HttpStatus.CREATED)
    public Enrollment teacherEnrollStudent(
            @Valid @RequestBody TeacherEnrollmentRequest request,
            @AuthenticationPrincipal Jwt userPrincipal) {

        // Get teacher from Cognito token
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());

        return enrollmentService.teacherEnrollStudent(request, teacher.getId());
    }

    @Operation(summary = "Teacher enrolls student with join code", description = "Simplified endpoint for teachers to enroll students using join code")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Student enrolled successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request data"),
        @ApiResponse(responseCode = "403", description = "Not authorized to enroll in this classroom"),
        @ApiResponse(responseCode = "404", description = "Classroom or student not found"),
        @ApiResponse(responseCode = "409", description = "Student already enrolled or classroom full"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @PostMapping(value = "/teacher/enroll/join-code/{joinCode}/user/{userId}", produces = "application/json")
    @ResponseStatus(HttpStatus.CREATED)
    public Enrollment teacherEnrollUserWithJoinCode(
            @Parameter(description = "Classroom join code") @PathVariable String joinCode,
            @Parameter(description = "User ID") @PathVariable String userId,
            @Parameter(description = "Role to enroll as (STUDENT or PARENT)") @RequestParam(defaultValue = "STUDENT") RoleType role,
            @AuthenticationPrincipal Jwt userPrincipal) {

        // Get teacher from Cognito token
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());

        return enrollmentService.teacherEnrollUserWithJoinCode(joinCode, userId, role, teacher.getId());
    }

    @Operation(summary = "Teacher enrolls student with classroom ID", description = "Simplified endpoint for teachers to enroll students using classroom ID")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Student enrolled successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request data"),
        @ApiResponse(responseCode = "403", description = "Not authorized to enroll in this classroom"),
        @ApiResponse(responseCode = "404", description = "Classroom or student not found"),
        @ApiResponse(responseCode = "409", description = "Student already enrolled or classroom full"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @PostMapping(value = "/teacher/enroll/classroom/{classroomId}/user/{userId}", produces = "application/json")
    @ResponseStatus(HttpStatus.CREATED)
    public Enrollment teacherEnrollUserWithClassroomId(
            @Parameter(description = "Classroom ID") @PathVariable String classroomId,
            @Parameter(description = "User ID") @PathVariable String userId,
            @Parameter(description = "Role to enroll as (STUDENT or PARENT)") @RequestParam(defaultValue = "STUDENT") RoleType role,
            @AuthenticationPrincipal Jwt userPrincipal) {

        // Get teacher from Cognito token
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());

        return enrollmentService.teacherEnrollUserWithClassroomId(classroomId, userId, role, teacher.getId());
    }

    @Operation(summary = "Un-enroll student from classroom", description = "Teacher un-enrolls a student from their classroom (sets status to inactive)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "Student un-enrolled successfully"),
        @ApiResponse(responseCode = "404", description = "Student not found or not enrolled"),
        @ApiResponse(responseCode = "403", description = "Not authorized to manage this classroom"),
        @ApiResponse(responseCode = "400", description = "Student already un-enrolled"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @DeleteMapping("/teacher/unenroll/classroom/{classroomId}/user/{userId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void unenrollUser(
            @Parameter(description = "Classroom ID") @PathVariable String classroomId,
            @Parameter(description = "User ID") @PathVariable String userId,
            @AuthenticationPrincipal Jwt userPrincipal) {

        // Get teacher from Cognito token
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());

        enrollmentService.teacherUnenrollUser(classroomId, userId, RoleType.STUDENT, teacher.getId());
    }

    @Operation(summary = "Permanently remove student from classroom", description = "Teacher permanently removes a student from their classroom (deletes enrollment record)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "Student removed successfully"),
        @ApiResponse(responseCode = "404", description = "Student not found or not enrolled"),
        @ApiResponse(responseCode = "403", description = "Not authorized to manage this classroom"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @DeleteMapping("/teacher/remove/classroom/{classroomId}/student/{studentId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void removeStudentPermanently(
            @Parameter(description = "Classroom ID") @PathVariable String classroomId,
            @Parameter(description = "Student ID") @PathVariable String studentId,
            @AuthenticationPrincipal Jwt userPrincipal) {

        // Get teacher from Cognito token
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());

        enrollmentService.teacherRemoveUser(classroomId, studentId, RoleType.STUDENT, teacher.getId());
    }

    @Operation(summary = "Re-enroll student in classroom", description = "Teacher re-enrolls a previously un-enrolled student")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Student re-enrolled successfully"),
        @ApiResponse(responseCode = "404", description = "Student not found"),
        @ApiResponse(responseCode = "403", description = "Not authorized to manage this classroom"),
        @ApiResponse(responseCode = "400", description = "Student already enrolled or classroom full"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @PostMapping("/teacher/reenroll/classroom/{classroomId}/student/{studentId}")
    @ResponseStatus(HttpStatus.OK)
    public Enrollment reenrollStudent(
            @Parameter(description = "Classroom ID") @PathVariable String classroomId,
            @Parameter(description = "Student ID") @PathVariable String studentId,
            @AuthenticationPrincipal Jwt userPrincipal) {

        // Get teacher from Cognito token
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());

        return enrollmentService.teacherReenrollUser(classroomId, studentId, RoleType.STUDENT, teacher.getId());
    }
}
