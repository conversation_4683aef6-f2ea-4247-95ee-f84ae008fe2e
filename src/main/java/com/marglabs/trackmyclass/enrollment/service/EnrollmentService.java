package com.marglabs.trackmyclass.enrollment.service;

import com.marglabs.common.rest.error.GeneralException;
import com.marglabs.common.utils.IdGenerator;
import com.marglabs.trackmyclass.classroom.model.Classroom;
import com.marglabs.trackmyclass.classroom.service.ClassroomService;
import com.marglabs.trackmyclass.enrollment.dao.EnrollmentDao;
import com.marglabs.trackmyclass.enrollment.entity.EnrollmentEntity;
import com.marglabs.trackmyclass.enrollment.mapper.EnrollmentMapper;
import com.marglabs.trackmyclass.enrollment.model.Enrollment;
import com.marglabs.trackmyclass.enrollment.model.EnrollmentStatus;
import com.marglabs.trackmyclass.enrollment.model.TeacherEnrollmentRequest;
import com.marglabs.trackmyclass.user.model.RoleType;
import com.marglabs.trackmyclass.user.model.User;
import com.marglabs.trackmyclass.user.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
public class EnrollmentService {

    private static final Logger logger = LoggerFactory.getLogger(EnrollmentService.class);

    @Autowired
    private EnrollmentDao enrollmentDao;

    @Autowired
    private EnrollmentMapper mapper;

    @Autowired
    private IdGenerator idGenerator;

    @Autowired
    private ClassroomService classroomService;

    @Autowired
    private UserService userService;

    /**
     * Enroll a user in a classroom
     */
    @Transactional
    public Enrollment enrollUserInClassroom(String classroomId, String userId, RoleType roleType) {
        // Validate role type
        if (roleType != RoleType.STUDENT && roleType != RoleType.PARENT) {
            throw new GeneralException(HttpStatus.BAD_REQUEST, "ENROLLMENT", "INVALID_ROLE_TYPE",
                    "invalid_role_type_for_enrollment", roleType.name());
        }

        // Check if user is already enrolled
        if (enrollmentDao.existsByClassroomIdAndUserIdAndRole(classroomId, userId, roleType.name())) {
            throw new GeneralException(HttpStatus.BAD_REQUEST, "ENROLLMENT", "ALREADY_ENROLLED",
                    "user_already_enrolled", userId, classroomId);
        }

        // Get classroom to check capacity and increment enrollment count
        Classroom classroom = classroomService.getClassroomById(classroomId);

        if (classroom.isClassFull() && roleType == RoleType.STUDENT) {
            throw new GeneralException(HttpStatus.BAD_REQUEST, "ENROLLMENT", "CLASSROOM_FULL",
                    "classroom_at_capacity", classroomId);
        }

        // Create enrollment
        Enrollment enrollment = new Enrollment()
                .setId(idGenerator.generateId())
                .setClassroomId(classroomId)
                .setUserId(userId)
                .setRole(roleType)
                .setStatus(EnrollmentStatus.ACTIVE);

        EnrollmentEntity entity = mapper.toEntity(enrollment);
        entity = enrollmentDao.create(entity);

        // Increment enrollment count for students only
        if (roleType == RoleType.STUDENT) {
            classroomService.incrementEnrollment(classroomId);
        }

        // Get classroom and user names for the response
        User user = userService.getUserById(userId);

        Enrollment result = mapper.toDto(entity);
        result.setClassroomName(classroom.getClassName());
        result.setUserName(user.getName());
        result.setPhoneNumber(user.getPhoneNumber());

        return result;
    }

    /**
     * Enroll a user in a classroom using join code
     */
    @Transactional
    public Enrollment enrollUserWithJoinCode(String joinCode, String userId, RoleType roleType) {
        // Get classroom by join code
        Classroom classroom = classroomService.getClassroomByJoinCode(joinCode);

        // Enroll user
        return enrollUserInClassroom(classroom.getId(), userId, roleType);
    }

    /**
     * Get enrollment by ID
     */
    public Enrollment getEnrollmentById(String id) {
        EnrollmentEntity entity = enrollmentDao.getById(id);
        Enrollment enrollment = mapper.toDto(entity);

        // Get classroom and user names
        try {
            Classroom classroom = classroomService.getClassroomById(entity.getClassroomId());
            User user = userService.getUserById(entity.getUserId());

            enrollment.setClassroomName(classroom.getClassName());
            enrollment.setUserName(user.getName());
            enrollment.setPhoneNumber(user.getPhoneNumber());
        } catch (Exception e) {
            // If classroom or user not found, just leave the names blank
            enrollment.setClassroomName("Unknown Classroom");
            enrollment.setUserName("Unknown User");
            enrollment.setPhoneNumber("Unknown Phone");
        }

        return enrollment;
    }

    /**
     * Get enrollments by classroom ID
     */
    public List<Enrollment> getEnrollmentsByClassroomId(String classroomId) {
        List<EnrollmentEntity> entities = enrollmentDao.getByClassroomId(classroomId);

        // Get classroom name once for all enrollments
        String classroomName = "Unknown Classroom";
        try {
            Classroom classroom = classroomService.getClassroomById(classroomId);
            classroomName = classroom.getClassName();
        } catch (Exception e) {
            // If classroom not found, just leave the default name
        }

        final String finalClassroomName = classroomName;

        return entities.stream()
                .map(entity -> {
                    Enrollment enrollment = mapper.toDto(entity);
                    enrollment.setClassroomName(finalClassroomName);

                    // Get user name and phone number
                    try {
                        User user = userService.getUserById(entity.getUserId());
                        enrollment.setUserName(user.getName());
                        enrollment.setPhoneNumber(user.getPhoneNumber());
                    } catch (Exception e) {
                        enrollment.setUserName("Unknown User");
                        enrollment.setPhoneNumber("Unknown Phone");
                    }

                    return enrollment;
                })
                .collect(Collectors.toList());
    }

    /**
     * Get enrollments by user ID
     */
    public List<Enrollment> getEnrollmentsByUserId(String userId) {
        List<EnrollmentEntity> entities = enrollmentDao.getByUserId(userId);

        // Get user name and phone number once for all enrollments
        String userName = "Unknown User";
        String phoneNumber = "Unknown Phone";
        try {
            User user = userService.getUserById(userId);
            userName = user.getName();
            phoneNumber = user.getPhoneNumber();
        } catch (Exception e) {
            // If user not found, just leave the default values
        }

        final String finalUserName = userName;
        final String finalPhoneNumber = phoneNumber;

        return entities.stream()
                .map(entity -> {
                    Enrollment enrollment = mapper.toDto(entity);
                    enrollment.setUserName(finalUserName);
                    enrollment.setPhoneNumber(finalPhoneNumber);

                    // Get classroom name
                    try {
                        Classroom classroom = classroomService.getClassroomById(entity.getClassroomId());
                        enrollment.setClassroomName(classroom.getClassName());
                    } catch (Exception e) {
                        enrollment.setClassroomName("Unknown Classroom");
                    }

                    return enrollment;
                })
                .collect(Collectors.toList());
    }

    /**
     * Get enrollments by user ID and role
     */
    public List<Enrollment> getEnrollmentsByUserIdAndRole(String userId, RoleType roleType) {
        List<EnrollmentEntity> entities = enrollmentDao.getByUserIdAndRole(userId, roleType.name());

        // Get user name and phone number once for all enrollments
        String userName = "Unknown User";
        String phoneNumber = "Unknown Phone";
        try {
            User user = userService.getUserById(userId);
            userName = user.getName();
            phoneNumber = user.getPhoneNumber();
        } catch (Exception e) {
            // If user not found, just leave the default values
        }

        final String finalUserName = userName;
        final String finalPhoneNumber = phoneNumber;

        return entities.stream()
                .map(entity -> {
                    Enrollment enrollment = mapper.toDto(entity);
                    enrollment.setUserName(finalUserName);
                    enrollment.setPhoneNumber(finalPhoneNumber);

                    // Get classroom name
                    try {
                        Classroom classroom = classroomService.getClassroomById(entity.getClassroomId());
                        enrollment.setClassroomName(classroom.getClassName());
                    } catch (Exception e) {
                        enrollment.setClassroomName("Unknown Classroom");
                    }

                    return enrollment;
                })
                .collect(Collectors.toList());
    }

    /**
     * Check if a user is enrolled in a classroom with a specific role
     */
    public boolean isUserEnrolled(String classroomId, String userId, RoleType roleType) {
        return enrollmentDao.existsByClassroomIdAndUserIdAndRole(classroomId, userId, roleType.name());
    }

    /**
     * Unenroll a user from a classroom
     */
    @Transactional
    public void unenrollUser(String classroomId, String userId, RoleType roleType) {
        Optional<EnrollmentEntity> enrollmentOpt = enrollmentDao.findByClassroomIdAndUserIdAndRole(classroomId, userId, roleType.name());

        if (enrollmentOpt.isPresent()) {
            EnrollmentEntity entity = enrollmentOpt.get();
            enrollmentDao.deleteById(entity.getId());

            // Decrement enrollment count for students only
            if (roleType == RoleType.STUDENT) {
                classroomService.decrementEnrollment(classroomId);
            }
        } else {
            throw new GeneralException(HttpStatus.BAD_REQUEST, "ENROLLMENT", "NOT_ENROLLED",
                    "user_not_enrolled", userId, classroomId);
        }
    }

    /**
     * Update enrollment status
     */
    @Transactional
    public Enrollment updateEnrollmentStatus(String id, EnrollmentStatus status) {
        EnrollmentEntity entity = enrollmentDao.getById(id);
        entity.setStatus(status.name());
        entity = enrollmentDao.update(entity);

        Enrollment enrollment = mapper.toDto(entity);

        // Get classroom and user names
        try {
            Classroom classroom = classroomService.getClassroomById(entity.getClassroomId());
            User user = userService.getUserById(entity.getUserId());

            enrollment.setClassroomName(classroom.getClassName());
            enrollment.setUserName(user.getName());
        } catch (Exception e) {
            // If classroom or user not found, just leave the names blank
            enrollment.setClassroomName("Unknown Classroom");
            enrollment.setUserName("Unknown User");
        }

        return enrollment;
    }

    /**
     * Teacher enrolls a student in their classroom
     */
    @Transactional
    public Enrollment teacherEnrollStudent(TeacherEnrollmentRequest request, String teacherId) {
        // Validate request
        validateTeacherEnrollmentRequest(request);

        // Get classroom ID (either from direct ID or join code)
        String classroomId = getClassroomIdFromRequest(request);

        // Verify teacher owns the classroom
        Classroom classroom = classroomService.getClassroomById(classroomId);
        if (!classroom.getTeacherId().equals(teacherId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "ENROLLMENT", "UNAUTHORIZED_CLASSROOM_ACCESS",
                    "teacher_not_authorized_for_classroom", classroomId);
        }

        // Verify user exists
        User user = userService.getUserById(request.getUserId());

        // Enroll the user
        return enrollUserInClassroom(classroomId, user.getId(), request.getRole());
    }

    /**
     * Teacher enrolls a user using join code
     */
    @Transactional
    public Enrollment teacherEnrollUserWithJoinCode(String joinCode, String userId, RoleType role, String teacherId) {
        TeacherEnrollmentRequest request = new TeacherEnrollmentRequest();
        request.setJoinCode(joinCode);
        request.setUserId(userId);
        request.setRole(role);

        return teacherEnrollStudent(request, teacherId);
    }

    /**
     * Teacher enrolls a user using classroom ID
     */
    @Transactional
    public Enrollment teacherEnrollUserWithClassroomId(String classroomId, String userId, RoleType role, String teacherId) {
        TeacherEnrollmentRequest request = new TeacherEnrollmentRequest();
        request.setClassroomId(classroomId);
        request.setUserId(userId);
        request.setRole(role);

        return teacherEnrollStudent(request, teacherId);
    }

    /**
     * Teacher un-enrolls a user from their classroom
     */
    @Transactional
    public void teacherUnenrollUser(String classroomId, String userId, RoleType role, String teacherId) {
        // Verify teacher owns the classroom
        Classroom classroom = classroomService.getClassroomById(classroomId);
        if (!classroom.getTeacherId().equals(teacherId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "ENROLLMENT", "UNAUTHORIZED_CLASSROOM_ACCESS",
                    "teacher_not_authorized_for_classroom", classroomId);
        }

        // Verify user exists
        User user = userService.getUserById(userId);

        // Find the enrollment
        Optional<EnrollmentEntity> enrollmentOpt = enrollmentDao.findByClassroomIdAndUserIdAndRole(
                classroomId, userId, role.name());

        if (enrollmentOpt.isEmpty()) {
            throw new GeneralException(HttpStatus.NOT_FOUND, "ENROLLMENT", "ENROLLMENT_NOT_FOUND",
                    "user_not_enrolled", userId, classroomId);
        }

        EnrollmentEntity enrollment = enrollmentOpt.get();

        // Check if enrollment is already inactive
        if ("INACTIVE".equals(enrollment.getStatus())) {
            throw new GeneralException(HttpStatus.BAD_REQUEST, "ENROLLMENT", "ALREADY_UNENROLLED",
                    "user_already_unenrolled", userId, classroomId);
        }

        // Set enrollment status to inactive instead of deleting
        enrollment.setStatus("INACTIVE");
        enrollmentDao.update(enrollment);

        // Decrement enrollment count for students only
        if (role == RoleType.STUDENT) {
            classroomService.decrementEnrollment(classroomId);
        }

        logger.info("User {} un-enrolled from classroom {} by teacher {}", userId, classroomId, teacherId);
    }

    /**
     * Teacher permanently removes a user from their classroom
     */
    @Transactional
    public void teacherRemoveUser(String classroomId, String userId, RoleType role, String teacherId) {
        // Verify teacher owns the classroom
        Classroom classroom = classroomService.getClassroomById(classroomId);
        if (!classroom.getTeacherId().equals(teacherId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "ENROLLMENT", "UNAUTHORIZED_CLASSROOM_ACCESS",
                    "teacher_not_authorized_for_classroom", classroomId);
        }

        // Verify user exists
        User user = userService.getUserById(userId);

        // Find the enrollment
        Optional<EnrollmentEntity> enrollmentOpt = enrollmentDao.findByClassroomIdAndUserIdAndRole(
                classroomId, userId, role.name());

        if (enrollmentOpt.isEmpty()) {
            throw new GeneralException(HttpStatus.NOT_FOUND, "ENROLLMENT", "ENROLLMENT_NOT_FOUND",
                    "user_not_enrolled", userId, classroomId);
        }

        EnrollmentEntity enrollment = enrollmentOpt.get();

        // Delete the enrollment permanently
        enrollmentDao.deleteById(enrollment.getId());

        // Decrement enrollment count for students only (if it was active)
        if (role == RoleType.STUDENT && "ACTIVE".equals(enrollment.getStatus())) {
            classroomService.decrementEnrollment(classroomId);
        }

        logger.info("User {} permanently removed from classroom {} by teacher {}", userId, classroomId, teacherId);
    }

    /**
     * Teacher re-enrolls a previously un-enrolled user
     */
    @Transactional
    public Enrollment teacherReenrollUser(String classroomId, String userId, RoleType role, String teacherId) {
        // Verify teacher owns the classroom
        Classroom classroom = classroomService.getClassroomById(classroomId);
        if (!classroom.getTeacherId().equals(teacherId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "ENROLLMENT", "UNAUTHORIZED_CLASSROOM_ACCESS",
                    "teacher_not_authorized_for_classroom", classroomId);
        }

        // Verify user exists
        User user = userService.getUserById(userId);

        // Find the enrollment
        Optional<EnrollmentEntity> enrollmentOpt = enrollmentDao.findByClassroomIdAndUserIdAndRole(
                classroomId, userId, role.name());

        if (enrollmentOpt.isEmpty()) {
            // No previous enrollment found, create new one
            return enrollUserInClassroom(classroomId, userId, role);
        }

        EnrollmentEntity enrollment = enrollmentOpt.get();

        // Check if enrollment is already active
        if ("ACTIVE".equals(enrollment.getStatus())) {
            throw new GeneralException(HttpStatus.BAD_REQUEST, "ENROLLMENT", "ALREADY_ENROLLED",
                    "user_already_enrolled", userId, classroomId);
        }

        // Check classroom capacity for students
        if (classroom.isClassFull() && role == RoleType.STUDENT) {
            throw new GeneralException(HttpStatus.BAD_REQUEST, "ENROLLMENT", "CLASSROOM_FULL",
                    "classroom_at_capacity", classroomId);
        }

        // Reactivate the enrollment
        enrollment.setStatus("ACTIVE");
        enrollment = enrollmentDao.update(enrollment);

        // Increment enrollment count for students only
        if (role == RoleType.STUDENT) {
            classroomService.incrementEnrollment(classroomId);
        }

        // User already retrieved earlier in the method

        Enrollment result = mapper.toDto(enrollment);
        result.setClassroomName(classroom.getClassName());
        result.setUserName(user.getName());

        logger.info("User {} re-enrolled in classroom {} by teacher {}", userId, classroomId, teacherId);

        return result;
    }

    // Private helper methods

    private void validateTeacherEnrollmentRequest(TeacherEnrollmentRequest request) {
        // Either classroomId or joinCode must be provided
        if ((request.getClassroomId() == null || request.getClassroomId().trim().isEmpty()) &&
            (request.getJoinCode() == null || request.getJoinCode().trim().isEmpty())) {
            throw new GeneralException(HttpStatus.BAD_REQUEST, "ENROLLMENT", "MISSING_CLASSROOM_IDENTIFIER",
                    "either_classroom_id_or_join_code_required");
        }

        // Both classroomId and joinCode cannot be provided
        if (request.getClassroomId() != null && !request.getClassroomId().trim().isEmpty() &&
            request.getJoinCode() != null && !request.getJoinCode().trim().isEmpty()) {
            throw new GeneralException(HttpStatus.BAD_REQUEST, "ENROLLMENT", "MULTIPLE_CLASSROOM_IDENTIFIERS",
                    "provide_either_classroom_id_or_join_code_not_both");
        }

        // Validate role
        if (request.getRole() != RoleType.STUDENT && request.getRole() != RoleType.PARENT) {
            throw new GeneralException(HttpStatus.BAD_REQUEST, "ENROLLMENT", "INVALID_ROLE_TYPE",
                    "invalid_role_type_for_enrollment", request.getRole().name());
        }

        // Parent enrollment is allowed but will use student's user ID as placeholder
    }

    private String getClassroomIdFromRequest(TeacherEnrollmentRequest request) {
        if (request.getClassroomId() != null && !request.getClassroomId().trim().isEmpty()) {
            return request.getClassroomId();
        } else if (request.getJoinCode() != null && !request.getJoinCode().trim().isEmpty()) {
            Classroom classroom = classroomService.getClassroomByJoinCode(request.getJoinCode());
            return classroom.getId();
        } else {
            throw new GeneralException(HttpStatus.BAD_REQUEST, "ENROLLMENT", "MISSING_CLASSROOM_IDENTIFIER",
                    "either_classroom_id_or_join_code_required");
        }
    }

}
