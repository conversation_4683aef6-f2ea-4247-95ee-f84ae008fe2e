package com.marglabs.trackmyclass.enrollment.repository;

import com.marglabs.trackmyclass.enrollment.entity.EnrollmentEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.data.rest.core.annotation.RepositoryRestResource;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
@RepositoryRestResource(exported = false)
public interface EnrollmentRepository extends JpaRepository<EnrollmentEntity, String> {
    List<EnrollmentEntity> findByClassroomId(String classroomId);
    List<EnrollmentEntity> findByUserId(String userId);
    List<EnrollmentEntity> findByUserIdAndRole(String userId, String role);
    List<EnrollmentEntity> findByClassroomIdAndRole(String classroomId, String role);
    List<EnrollmentEntity> findByClassroomIdAndRoleAndStatus(String classroomId, String role, String status);
    Optional<EnrollmentEntity> findByClassroomIdAndUserIdAndRole(String classroomId, String userId, String role);
    Optional<EnrollmentEntity> findByClassroomIdAndUserIdAndRoleAndStatus(String classroomId, String userId, String role, String status);
    boolean existsByClassroomIdAndUserIdAndRoleAndStatus(String classroomId, String userId, String role, String status);
    int countByClassroomId(String classroomId);
    
    @Query("SELECT COUNT(e) FROM EnrollmentEntity e JOIN ClassroomEntity c ON e.classroomId = c.id WHERE c.teacherId = :teacherId")
    long countByTeacherId(@Param("teacherId") String teacherId);
    
    long countByUserIdAndRole(String userId, String role);
}
