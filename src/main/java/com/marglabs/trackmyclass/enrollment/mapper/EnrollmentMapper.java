package com.marglabs.trackmyclass.enrollment.mapper;

import com.marglabs.trackmyclass.enrollment.entity.EnrollmentEntity;
import com.marglabs.trackmyclass.enrollment.model.Enrollment;
import com.marglabs.trackmyclass.enrollment.model.EnrollmentStatus;
import com.marglabs.trackmyclass.user.model.RoleType;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

@Mapper(componentModel = "spring")
public interface EnrollmentMapper {
    
    @Mapping(target = "role", expression = "java(mapToRoleType(entity.getRole()))")
    @Mapping(target = "status", expression = "java(mapToEnrollmentStatus(entity.getStatus()))")
    @Mapping(target = "classroomName", ignore = true)
    @Mapping(target = "userName", ignore = true)
    @Mapping(target = "phoneNumber", ignore = true)
    Enrollment toDto(EnrollmentEntity entity);
    
    @Mapping(target = "role", expression = "java(enrollment.getRole().name())")
    @Mapping(target = "status", expression = "java(enrollment.getStatus().name())")
    EnrollmentEntity toEntity(Enrollment enrollment);
    
    @Named("mapToRoleType")
    default RoleType mapToRoleType(String role) {
        return RoleType.valueOf(role);
    }
    
    @Named("mapToEnrollmentStatus")
    default EnrollmentStatus mapToEnrollmentStatus(String status) {
        return EnrollmentStatus.valueOf(status);
    }
}
