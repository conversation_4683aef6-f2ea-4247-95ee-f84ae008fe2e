package com.marglabs.trackmyclass.email.model;

public enum EmailTemplate {
    WELCOME("Welcome to TrackMyClass", "welcome-template"),
    ENROLLMENT_CONFIRMATION("Enrollment Confirmation", "enrollment-template"),
    PAYMENT_CONFIRMATION("Payment Confirmation", "payment-template"),
    LEAVE_APPLICATION("Leave Application", "leave-application-template"),
    LEAVE_APPROVAL("Leave Approved", "leave-approval-template"),
    LEAVE_REJECTION("Leave Rejected", "leave-rejection-template"),
    CLASS_CREATION("New Class Created", "class-creation-template"),
    CLASS_REMINDER("Class Reminder", "class-reminder-template"),
    ASSIGNMENT_DUE("Assignment Due", "assignment-due-template"),
    GENERAL_NOTIFICATION("Notification", "general-template");

    private final String subject;
    private final String templateName;

    EmailTemplate(String subject, String templateName) {
        this.subject = subject;
        this.templateName = templateName;
    }

    public String getSubject() {
        return subject;
    }

    public String getTemplateName() {
        return templateName;
    }
}