package com.marglabs.trackmyclass.email.service;

import com.marglabs.trackmyclass.email.model.EmailRequest;
import com.marglabs.trackmyclass.email.model.EmailTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import java.util.Map;

@Service
public class EmailService {
    
    private static final Logger logger = LoggerFactory.getLogger(EmailService.class);
    
    @Autowired
    private JavaMailSender mailSender;
    
    @Autowired
    private TemplateEngine templateEngine;
    
    @Value("${app.email.from:<EMAIL>}")
    private String fromEmail;

    public void sendEmail(EmailRequest emailRequest) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
            
            helper.setFrom(fromEmail);
            helper.setTo(emailRequest.getTo());
            helper.setSubject(emailRequest.getSubject() != null ? 
                emailRequest.getSubject() : emailRequest.getTemplate().getSubject());
            
            String htmlContent = processTemplate(emailRequest.getTemplate(), emailRequest.getTemplateData());
            helper.setText(htmlContent, true);
            
            mailSender.send(message);
            logger.info("Email sent successfully to: {}", emailRequest.getTo());
            
        } catch (MessagingException e) {
            logger.error("Failed to send email to: {}", emailRequest.getTo(), e);
            throw new RuntimeException("Failed to send email", e);
        }catch (Exception e) {
            logger.error("Failed to send email to: {}", emailRequest.getTo(), e);
            throw new RuntimeException("Failed to send email", e);
        }
    }

    public void sendEmail(String to, EmailTemplate template, Map<String, Object> templateData) {
        EmailRequest request = new EmailRequest()
            .setTo(to)
            .setTemplate(template)
            .setTemplateData(templateData);
        sendEmail(request);
    }

    public void sendEmail(String to, String subject, EmailTemplate template, Map<String, Object> templateData) {
        EmailRequest request = new EmailRequest()
            .setTo(to)
            .setSubject(subject)
            .setTemplate(template)
            .setTemplateData(templateData);
        sendEmail(request);
    }

    private String processTemplate(EmailTemplate template, Map<String, Object> templateData) {
        Context context = new Context();
        if (templateData != null) {
            templateData.forEach(context::setVariable);
        }
        return templateEngine.process(template.getTemplateName(), context);
    }
}