package com.marglabs.trackmyclass.syllabus.entity;

import jakarta.persistence.*;
import lombok.Data;
import com.marglabs.trackmyclass.syllabus.model.SyllabusStatus;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.Instant;

@Entity
@Table(name = "syllabus")
@Data
public class SyllabusEntity {
    @Id
    private String id;

    @Column(name = "classroomid")
    private String classroomId;

    @Column
    private String title;

    @Column(length = 2000)
    private String content;

    @Column(name = "attached_file_ids", length = 2000)
    private String attachedFileIds; // Comma-separated file IDs
    
    @Column(name = "reference_urls", length = 2000)
    private String referenceUrls; // Comma-separated URLs

    @CreationTimestamp
    @Column(name = "createddate", nullable = false, updatable = false)
    private Instant createdDate;

    @UpdateTimestamp
    @Column(name = "updateddate", nullable = false)
    private Instant updatedDate;

    @Column(name = "userid")
    private String userId;

    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private SyllabusStatus status;
}
