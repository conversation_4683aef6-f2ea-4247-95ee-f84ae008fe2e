package com.marglabs.trackmyclass.syllabus.repository;

import com.marglabs.trackmyclass.syllabus.entity.SyllabusEntity;
import com.marglabs.trackmyclass.syllabus.model.SyllabusStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.rest.core.annotation.RepositoryRestResource;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@RepositoryRestResource(exported = false)
public interface SyllabusRepository extends JpaRepository<SyllabusEntity, String> {
    List<SyllabusEntity> findByClassroomId(String classroomId);
    List<SyllabusEntity> findByUserId(String userId);
    List<SyllabusEntity> findByStatus(SyllabusStatus status);
    List<SyllabusEntity> findByClassroomIdAndStatus(String classroomId, SyllabusStatus status);
}
