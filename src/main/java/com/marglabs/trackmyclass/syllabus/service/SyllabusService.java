package com.marglabs.trackmyclass.syllabus.service;

import com.marglabs.common.utils.IdGenerator;
import com.marglabs.trackmyclass.file.model.FileInfo;
import com.marglabs.trackmyclass.file.service.FileService;
import com.marglabs.trackmyclass.syllabus.dao.SyllabusDao;
import com.marglabs.trackmyclass.syllabus.entity.SyllabusEntity;
import com.marglabs.trackmyclass.syllabus.mapper.SyllabusMapper;
import com.marglabs.trackmyclass.syllabus.model.*;
import com.marglabs.trackmyclass.assignment.service.LLMService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class SyllabusService {

    private static final Logger logger = LoggerFactory.getLogger(SyllabusService.class);

    @Autowired
    private SyllabusDao syllabusDao;

    @Autowired
    private SyllabusMapper mapper;

    @Autowired
    private IdGenerator idGenerator;

    @Autowired
    private FileService fileService;

    @Autowired
    private LLMService llmService;

    public Syllabus createSyllabus(SyllabusCreationRequest request, String classroomId, String userId) {
        logger.info("Creating syllabus for classroom: {} by user: {}", classroomId, userId);

        // Validate attached files if provided
        List<String> validatedFileIds = validateAndFilterFileIds(request.getAttachedFileIds(), userId);

        // Create Syllabus object from request
        Syllabus syllabus = new Syllabus()
                .setId(idGenerator.generateId())
                .setClassroomId(classroomId)
                .setUserId(userId)
                .setTitle(request.getTitle())
                .setContent(request.getContent())
                .setAttachedFileIds(validatedFileIds)
                .setStatus(SyllabusStatus.OPEN);

        SyllabusEntity entity = mapper.toEntity(syllabus);
        entity = syllabusDao.create(entity);

        Syllabus createdSyllabus = mapper.toDto(entity);

        // Enrich with file information
        return enrichSyllabusWithFiles(createdSyllabus);
    }

    public Syllabus updateSyllabus(String id, SyllabusUpdateRequest request, String userId) {
        logger.info("Updating syllabus: {} by user: {}", id, userId);

        // Get existing syllabus
        Syllabus existingSyllabus = getSyllabus(id);

        // Update only provided fields
        if (request.getTitle() != null) {
            existingSyllabus.setTitle(request.getTitle());
        }
        if (request.getContent() != null) {
            existingSyllabus.setContent(request.getContent());
        }
        if (request.getAttachedFileIds() != null) {
            // Validate attached files if provided
            List<String> validatedFileIds = validateAndFilterFileIds(request.getAttachedFileIds(), userId);
            existingSyllabus.setAttachedFileIds(validatedFileIds);
        }
        if (request.getStatus() != null) {
            existingSyllabus.setStatus(request.getStatus());
        }

        // Update userId (for audit trail)
        existingSyllabus.setUserId(userId);

        SyllabusEntity entity = mapper.toEntity(existingSyllabus);
        entity = syllabusDao.update(entity);

        Syllabus updatedSyllabus = mapper.toDto(entity);

        // Enrich with file information
        return enrichSyllabusWithFiles(updatedSyllabus);
    }

    public Syllabus getSyllabus(String id) {
        SyllabusEntity entity = syllabusDao.getById(id);
        Syllabus syllabus = mapper.toDto(entity);
        return enrichSyllabusWithFiles(syllabus);
    }

    public List<Syllabus> getSyllabusByClassroom(String classroomId) {
        return syllabusDao.getByClassroomId(classroomId).stream()
                .map(mapper::toDto)
                .map(this::enrichSyllabusWithFiles)
                .collect(Collectors.toList());
    }

    public List<Syllabus> getSyllabusByUser(String userId) {
        return syllabusDao.getByUserId(userId).stream()
                .map(mapper::toDto)
                .map(this::enrichSyllabusWithFiles)
                .collect(Collectors.toList());
    }

    public List<Syllabus> getSyllabusByStatus(SyllabusStatus status) {
        return syllabusDao.getByStatus(status).stream()
                .map(mapper::toDto)
                .map(this::enrichSyllabusWithFiles)
                .collect(Collectors.toList());
    }

    public List<Syllabus> getSyllabusByClassroomAndStatus(String classroomId, SyllabusStatus status) {
        return syllabusDao.getByClassroomIdAndStatus(classroomId, status).stream()
                .map(mapper::toDto)
                .map(this::enrichSyllabusWithFiles)
                .collect(Collectors.toList());
    }

    public void deleteSyllabus(String id) {
        syllabusDao.deleteById(id);
    }

    /**
     * Generate syllabus topics using LLM
     */
    public List<SyllabusTopic> generateSyllabusTopics(SyllabusGenerationRequest request) {
        logger.info("Generating syllabus topics for subject: {} grade: {}", request.getSubject(), request.getGradeLevel());
        
        String prompt = buildSyllabusPrompt(request);
        
        try {
            String response = llmService.callPrimaryLLM(prompt);
            return parseSyllabusResponse(response);
        } catch (Exception e) {
            logger.error("Failed to generate syllabus topics: {}", e.getMessage(), e);
            return generateFallbackTopics(request);
        }
    }
    
    private String buildSyllabusPrompt(SyllabusGenerationRequest request) {
        StringBuilder prompt = new StringBuilder();
        
        prompt.append("Generate a comprehensive syllabus for ").append(request.getSubject())
              .append(" for grade ").append(request.getGradeLevel()).append(".\n\n");
        
        if (request.getAdditionalRequirements() != null) {
            prompt.append("Additional requirements: ").append(request.getAdditionalRequirements()).append("\n");
        }
        
        prompt.append("\nFormat each topic as follows:\n");
        prompt.append("TOPIC: [Topic name]\n");
        prompt.append("DESCRIPTION: [Brief description of what will be covered]\n");
        prompt.append("REFERENCE: [Educational reference URL or resource]\n");
        prompt.append("---\n");
        
        prompt.append("\nGenerate 8-12 topics covering the essential curriculum.");
        
        return prompt.toString();
    }
    
    private List<SyllabusTopic> parseSyllabusResponse(String response) {
        List<SyllabusTopic> topics = new ArrayList<>();
        String[] topicBlocks = response.split("---");
        
        for (String block : topicBlocks) {
            if (block.trim().isEmpty()) continue;
            
            try {
                SyllabusTopic topic = parseTopicBlock(block.trim());
                if (topic != null) {
                    topics.add(topic);
                }
            } catch (Exception e) {
                logger.warn("Failed to parse topic block: {}", e.getMessage());
            }
        }
        
        return topics;
    }
    
    private SyllabusTopic parseTopicBlock(String block) {
        String[] lines = block.split("\n");
        
        SyllabusTopic topic = new SyllabusTopic();
        
        for (String line : lines) {
            line = line.trim();
            if (line.startsWith("TOPIC:")) {
                topic.setTopic(line.substring("TOPIC:".length()).trim());
            } else if (line.startsWith("DESCRIPTION:")) {
                topic.setDescription(line.substring("DESCRIPTION:".length()).trim());
            } else if (line.startsWith("REFERENCE:")) {
                topic.setReferenceUrl(line.substring("REFERENCE:".length()).trim());
            }
        }
        
        if (topic.getTopic() == null || topic.getDescription() == null) {
            return null;
        }
        
        return topic;
    }
    
    private List<SyllabusTopic> generateFallbackTopics(SyllabusGenerationRequest request) {
        logger.info("Generating fallback topics for subject: {}", request.getSubject());
        
        List<SyllabusTopic> fallbackTopics = new ArrayList<>();
        
        fallbackTopics.add(new SyllabusTopic()
            .setTopic("Introduction to " + request.getSubject())
            .setDescription("Basic concepts and fundamentals")
            .setReferenceUrl("https://www.khanacademy.org"));
            
        fallbackTopics.add(new SyllabusTopic()
            .setTopic("Core Principles")
            .setDescription("Essential principles and theories")
            .setReferenceUrl("https://www.coursera.org"));
            
        fallbackTopics.add(new SyllabusTopic()
            .setTopic("Practical Applications")
            .setDescription("Real-world applications and examples")
            .setReferenceUrl("https://www.edx.org"));
            
        return fallbackTopics;
    }

    /**
     * Validate and filter file IDs to ensure they exist and belong to the user
     */
    private List<String> validateAndFilterFileIds(List<String> fileIds, String userId) {
        if (fileIds == null || fileIds.isEmpty()) {
            return new ArrayList<>();
        }

        List<String> validFileIds = new ArrayList<>();

        for (String fileId : fileIds) {
            if (StringUtils.hasText(fileId)) {
                try {
                    FileInfo fileInfo = fileService.getFileInfo(fileId);

                    // Check if file belongs to the user
                    if (fileInfo.getUploadedBy().equals(userId)) {
                        validFileIds.add(fileId);
                        logger.debug("Validated file attachment: {}", fileId);
                    } else {
                        logger.warn("User {} attempted to attach file {} they don't own", userId, fileId);
                    }
                } catch (Exception e) {
                    logger.warn("Invalid file ID {} provided by user {}: {}", fileId, userId, e.getMessage());
                }
            }
        }

        logger.info("Validated {} out of {} file attachments for user {}",
                   validFileIds.size(), fileIds.size(), userId);

        return validFileIds;
    }

    /**
     * Enrich syllabus with file information
     */
    private Syllabus enrichSyllabusWithFiles(Syllabus syllabus) {
        if (syllabus.getAttachedFileIds() == null || syllabus.getAttachedFileIds().isEmpty()) {
            syllabus.setAttachedFiles(new ArrayList<>());
            return syllabus;
        }

        List<FileInfo> attachedFiles = new ArrayList<>();

        for (String fileId : syllabus.getAttachedFileIds()) {
            try {
                FileInfo fileInfo = fileService.getFileInfo(fileId);
                attachedFiles.add(fileInfo);
            } catch (Exception e) {
                logger.warn("Failed to load file info for attached file {}: {}", fileId, e.getMessage());
            }
        }

        syllabus.setAttachedFiles(attachedFiles);
        return syllabus;
    }
}
