package com.marglabs.trackmyclass.syllabus.dao;

import com.marglabs.common.rest.error.EntityNotFoundException;
import com.marglabs.trackmyclass.syllabus.entity.SyllabusEntity;
import com.marglabs.trackmyclass.syllabus.model.SyllabusStatus;
import com.marglabs.trackmyclass.syllabus.repository.SyllabusRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

@Component
public class SyllabusDao {
    @Autowired
    private SyllabusRepository repository;

    public SyllabusEntity create(SyllabusEntity entity) {
        // Timestamps are set automatically by @CreationTimestamp and @UpdateTimestamp
        return repository.save(entity);
    }

    public SyllabusEntity update(SyllabusEntity entity) {
        // UpdateTimestamp is set automatically by @UpdateTimestamp
        return repository.save(entity);
    }

    public SyllabusEntity getById(String id) {
        Optional<SyllabusEntity> entity = repository.findById(id);
        if (entity.isPresent()) {
            return entity.get();
        }
        throw new EntityNotFoundException(HttpStatus.NOT_FOUND, "SYLLABUS", "SYLLABUS_NOT_FOUND", "syllabus_not_found_details", id);
    }

    public List<SyllabusEntity> getByClassroomId(String classroomId) {
        return repository.findByClassroomId(classroomId);
    }

    public List<SyllabusEntity> getByUserId(String userId) {
        return repository.findByUserId(userId);
    }

    public List<SyllabusEntity> getByStatus(SyllabusStatus status) {
        return repository.findByStatus(status);
    }

    public List<SyllabusEntity> getByClassroomIdAndStatus(String classroomId, SyllabusStatus status) {
        return repository.findByClassroomIdAndStatus(classroomId, status);
    }

    public void deleteById(String id) {
        try {
            repository.deleteById(id);
        } catch (EmptyResultDataAccessException e) {
            throw new EntityNotFoundException(HttpStatus.NOT_FOUND, "SYLLABUS", "SYLLABUS_NOT_FOUND", "syllabus_not_found_details", id);
        }
    }
}
