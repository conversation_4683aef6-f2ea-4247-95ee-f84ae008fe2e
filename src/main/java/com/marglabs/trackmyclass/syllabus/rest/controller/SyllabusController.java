package com.marglabs.trackmyclass.syllabus.rest.controller;

import com.marglabs.trackmyclass.syllabus.model.*;
import com.marglabs.trackmyclass.syllabus.service.SyllabusService;
import com.marglabs.trackmyclass.user.model.User;
import com.marglabs.trackmyclass.user.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/syllabusManagement/v1")
@Tag(name = "Syllabus Management", description = "API for managing syllabus content")
public class SyllabusController {

    @Autowired
    private SyllabusService service;

    @Autowired
    private UserService userService;

    @Operation(summary = "Create a new syllabus", description = "Creates a new syllabus for a specific classroom. Files should be uploaded separately using the file upload API, then reference the file IDs in the attachedFileIds field.")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Syllabus created successfully",
                content = @Content(schema = @Schema(implementation = Syllabus.class))),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Forbidden - only teachers can create syllabus")
    })
    @PostMapping(value = "/classroom/{classroomId}/syllabus", consumes = "application/json", produces = "application/json")
    @ResponseStatus(HttpStatus.CREATED)
    public Syllabus createSyllabus(
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal,
            @Parameter(description = "ID of the classroom") @PathVariable String classroomId,
            @Parameter(description = "Syllabus details") @Valid @RequestBody SyllabusCreationRequest request) {
//        String userId = userPrincipal.getClaimAsString("username");
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());
        return service.createSyllabus(request, classroomId, user.getId());
    }

    @Operation(summary = "Update syllabus", description = "Updates an existing syllabus")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Syllabus updated successfully",
                content = @Content(schema = @Schema(implementation = Syllabus.class))),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "404", description = "Syllabus not found"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Forbidden - only teachers can update syllabus")
    })
    @PutMapping(value = "/syllabus/{id}", consumes = "application/json", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public Syllabus updateSyllabus(
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal,
            @Parameter(description = "ID of the syllabus") @PathVariable String id,
            @Parameter(description = "Updated syllabus details") @Valid @RequestBody SyllabusUpdateRequest request) {
//        String userId = userPrincipal.getClaimAsString("username");
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());
        return service.updateSyllabus(id, request, user.getId());
    }

    @Operation(summary = "Get syllabus by ID", description = "Returns a syllabus by its ID")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successful operation",
                content = @Content(schema = @Schema(implementation = Syllabus.class))),
        @ApiResponse(responseCode = "404", description = "Syllabus not found")
    })
    @GetMapping(value = "/syllabus/{id}", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public Syllabus getSyllabus(@Parameter(description = "ID of the syllabus to retrieve") @PathVariable String id) {
        return service.getSyllabus(id);
    }

    @GetMapping(value = "/classroom/{classroomId}/syllabus", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<Syllabus> getSyllabusByClassroom(@PathVariable String classroomId) {
        return service.getSyllabusByClassroom(classroomId);
    }

    @GetMapping(value = "/syllabus/status/{status}", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<Syllabus> getSyllabusByStatus(@PathVariable SyllabusStatus status) {
        return service.getSyllabusByStatus(status);
    }

    @GetMapping(value = "/classroom/{classroomId}/syllabus/status/{status}", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<Syllabus> getSyllabusByClassroomAndStatus(
            @PathVariable String classroomId,
            @PathVariable SyllabusStatus status) {
        return service.getSyllabusByClassroomAndStatus(classroomId, status);
    }

    @Operation(summary = "Update syllabus status", description = "Updates the status of a syllabus (OPEN, IN_PROGRESS, COMPLETED)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Status updated successfully",
                content = @Content(schema = @Schema(implementation = Syllabus.class))),
        @ApiResponse(responseCode = "404", description = "Syllabus not found"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Forbidden - only teachers can update syllabus status")
    })
    @PatchMapping(value = "/syllabus/{id}/status/{status}", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public Syllabus updateSyllabusStatus(
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal,
            @Parameter(description = "ID of the syllabus") @PathVariable String id,
            @Parameter(description = "New status value") @PathVariable SyllabusStatus status) {
        String userId = userPrincipal.getClaimAsString("username");

        // Create update request with only status change
        SyllabusUpdateRequest request = new SyllabusUpdateRequest();
        request.setStatus(status);

        return service.updateSyllabus(id, request, userId);
    }

    @DeleteMapping(value = "/syllabus/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void deleteSyllabus(@PathVariable String id) {
        service.deleteSyllabus(id);
    }

    @Operation(summary = "Generate syllabus topics using AI", description = "Generate syllabus topics with descriptions and reference URLs using LLM")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Topics generated successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @PostMapping(value = "/generate-topics", consumes = "application/json", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<SyllabusTopic> generateSyllabusTopics(
            @Parameter(description = "Syllabus generation request") @Valid @RequestBody SyllabusGenerationRequest request) {
        return service.generateSyllabusTopics(request);
    }
}
