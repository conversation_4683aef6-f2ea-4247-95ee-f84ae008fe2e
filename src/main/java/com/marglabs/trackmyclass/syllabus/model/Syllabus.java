package com.marglabs.trackmyclass.syllabus.model;

import com.marglabs.trackmyclass.file.model.FileInfo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.Instant;
import java.util.List;

@Data
@Accessors(chain = true)
public class Syllabus {
    private String id;
    private String classroomId;
    private String title;
    private String content;

    // File attachments
    private List<String> attachedFileIds;
    private List<FileInfo> attachedFiles; // Populated by service layer
    
    // Reference URLs
    private List<String> referenceUrls;

    private Instant createdDate;
    private Instant updatedDate;
    private String userId;
    private SyllabusStatus status;
}
