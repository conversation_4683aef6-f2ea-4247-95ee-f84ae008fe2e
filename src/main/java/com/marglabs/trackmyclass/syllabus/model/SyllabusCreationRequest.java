package com.marglabs.trackmyclass.syllabus.model;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

@Data
public class SyllabusCreationRequest {

    @NotBlank(message = "Title is required")
    @Size(max = 200, message = "Title cannot exceed 200 characters")
    private String title;

    @Size(max = 2000, message = "Content cannot exceed 2000 characters")
    private String content;

    // Multiple file attachments
    private List<String> attachedFileIds;
    
    // Optional reference URLs
    private List<String> referenceUrls;

}
