package com.marglabs.trackmyclass.syllabus.model;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class SyllabusGenerationRequest {
    
    @NotBlank(message = "Subject is required")
    private String subject;
    
    @NotBlank(message = "Grade level is required")
    private String gradeLevel;
    
    private String additionalRequirements; // Optional additional instructions
}