package com.marglabs.trackmyclass.syllabus.mapper;

import com.marglabs.trackmyclass.syllabus.entity.SyllabusEntity;
import com.marglabs.trackmyclass.syllabus.model.Syllabus;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Mapper(componentModel = "spring")
public interface SyllabusMapper {

    @Mapping(target = "attachedFileIds", expression = "java(stringToList(entity.getAttachedFileIds()))")
    @Mapping(target = "referenceUrls", expression = "java(stringToList(entity.getReferenceUrls()))")
    @Mapping(target = "attachedFiles", ignore = true) // Will be populated by service
    Syllabus toDto(SyllabusEntity entity);

    @Mapping(target = "createdDate", ignore = true)
    @Mapping(target = "updatedDate", ignore = true)
    @Mapping(target = "attachedFileIds", expression = "java(listToString(syllabus.getAttachedFileIds()))")
    @Mapping(target = "referenceUrls", expression = "java(listToString(syllabus.getReferenceUrls()))")
    SyllabusEntity toEntity(Syllabus syllabus);

    /**
     * Convert comma-separated string to list
     */
    default List<String> stringToList(String str) {
        if (!StringUtils.hasText(str)) {
            return Collections.emptyList();
        }
        return Arrays.asList(str.split(","));
    }

    /**
     * Convert list to comma-separated string
     */
    default String listToString(List<String> list) {
        if (list == null || list.isEmpty()) {
            return null;
        }
        return String.join(",", list);
    }
}
