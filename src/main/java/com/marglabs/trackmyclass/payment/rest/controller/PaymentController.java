package com.marglabs.trackmyclass.payment.rest.controller;

import com.marglabs.trackmyclass.payment.model.*;
import com.marglabs.trackmyclass.payment.service.PaymentService;
import com.marglabs.trackmyclass.user.model.User;
import com.marglabs.trackmyclass.user.model.RoleType;
import com.marglabs.trackmyclass.user.service.UserService;
import org.springframework.security.access.AccessDeniedException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/paymentManagement/v1")
@Tag(name = "Payment Management", description = "APIs for managing student payments")
public class PaymentController {

    @Autowired
    private PaymentService paymentService;

    @Autowired
    private UserService userService;

    @Operation(summary = "Record a payment", description = "Teachers can record payments from students")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Payment recorded successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request data"),
        @ApiResponse(responseCode = "403", description = "Not authorized to record payment for this classroom"),
        @ApiResponse(responseCode = "409", description = "Duplicate receipt number"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @PostMapping(value = "/classrooms/{classroomId}/payments", produces = "application/json")
    @ResponseStatus(HttpStatus.CREATED)
    public Payment recordPayment(
            @Parameter(description = "Classroom ID") @PathVariable String classroomId,
            @Valid @RequestBody PaymentCreationRequest request,
            @AuthenticationPrincipal Jwt userPrincipal) {

        // Get teacher from Cognito token
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());

        return paymentService.recordPayment(request, classroomId, teacher.getId());
    }

    @Operation(summary = "Update payment record", description = "Update an existing payment record")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Payment updated successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request data"),
        @ApiResponse(responseCode = "404", description = "Payment record not found"),
        @ApiResponse(responseCode = "403", description = "Not authorized to update this payment record"),
        @ApiResponse(responseCode = "409", description = "Duplicate receipt number"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @PutMapping(value = "/payments/{paymentId}", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public Payment updatePayment(
            @Parameter(description = "Payment ID") @PathVariable String paymentId,
            @Valid @RequestBody PaymentUpdateRequest request,
            @AuthenticationPrincipal Jwt userPrincipal) {

        // Get teacher from Cognito token
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());

        return paymentService.updatePayment(paymentId, request, teacher.getId());
    }

    @Operation(summary = "Get payment by ID", description = "Retrieve a specific payment record")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Payment retrieved successfully"),
        @ApiResponse(responseCode = "404", description = "Payment record not found"),
        @ApiResponse(responseCode = "403", description = "Not authorized to view this payment record"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/payments/{paymentId}", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public Payment getPayment(
            @Parameter(description = "Payment ID") @PathVariable String paymentId,
            @AuthenticationPrincipal Jwt userPrincipal) {

        // Get teacher from Cognito token
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());

        return paymentService.getPaymentById(paymentId, teacher.getId());
    }

    @Operation(summary = "Get payments by classroom", description = "Retrieve all payment records for a classroom")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Payments retrieved successfully"),
        @ApiResponse(responseCode = "403", description = "Not authorized to view payments for this classroom"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/classrooms/{classroomId}/payments", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<Payment> getPaymentsByClassroom(
            @Parameter(description = "Classroom ID") @PathVariable String classroomId,
            @AuthenticationPrincipal Jwt userPrincipal) {

        // Get teacher from Cognito token
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());

        return paymentService.getPaymentsByClassroom(classroomId, teacher.getId());
    }

    @Operation(summary = "Get payments by classroom and date range", description = "Retrieve payment records for a classroom within a date range")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Payments retrieved successfully"),
        @ApiResponse(responseCode = "403", description = "Not authorized to view payments for this classroom"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/classrooms/{classroomId}/payments/range", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<Payment> getPaymentsByClassroomAndDateRange(
            @Parameter(description = "Classroom ID") @PathVariable String classroomId,
            @Parameter(description = "Start date (YYYY-MM-DD)") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") String startDate,
            @Parameter(description = "End date (YYYY-MM-DD)") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") String endDate,
            @AuthenticationPrincipal Jwt userPrincipal) {

        // Get teacher from Cognito token
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());

        return paymentService.getPaymentsByClassroomAndDateRange(classroomId, startDate, endDate, teacher.getId());
    }

    @Operation(summary = "Get payments by student and date range", description = "Retrieve payment records for a student within a date range")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Payments retrieved successfully"),
        @ApiResponse(responseCode = "403", description = "Not authorized to view payments for this student"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/students/{studentId}/payments", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<Payment> getPaymentsByStudentAndDateRange(
            @Parameter(description = "Student ID") @PathVariable String studentId,
            @Parameter(description = "Start date (YYYY-MM-DD)") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") String startDate,
            @Parameter(description = "End date (YYYY-MM-DD)") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") String endDate,
            @AuthenticationPrincipal Jwt userPrincipal) {

        // Get teacher from Cognito token
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());

        return paymentService.getPaymentsByStudentAndDateRange(studentId, startDate, endDate, teacher.getId());
    }

    @Operation(summary = "Get payments for student by classroom", description = "Retrieve payment records for a specific student in a classroom")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Payments retrieved successfully"),
        @ApiResponse(responseCode = "403", description = "Not authorized to view payments for this classroom/student"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/classrooms/{classroomId}/students/payments", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<Payment> getPaymentsByClassroomAndStudent(
            @Parameter(description = "Classroom ID") @PathVariable String classroomId,
            @AuthenticationPrincipal Jwt userPrincipal) {

        // Get student from Cognito token
        User student = userService.getUserByCognitoId(userPrincipal.getSubject());

        return paymentService.getPaymentsByClassroomAndStudent(classroomId, student.getId());
    }

    @Operation(summary = "Get payments by schedule", description = "Retrieve payment records for a specific class schedule")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Payments retrieved successfully"),
        @ApiResponse(responseCode = "403", description = "Not authorized to view payments for this schedule"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/schedules/{scheduleId}/payments", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<Payment> getPaymentsBySchedule(
            @Parameter(description = "Schedule ID") @PathVariable String scheduleId,
            @AuthenticationPrincipal Jwt userPrincipal) {

        // Get teacher from Cognito token
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());

        return paymentService.getPaymentsBySchedule(scheduleId, teacher.getId());
    }


    @Operation(summary = "Get payment statistics", description = "Get payment statistics for a classroom within a date range")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Statistics retrieved successfully"),
        @ApiResponse(responseCode = "403", description = "Not authorized to view statistics for this classroom"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/classrooms/{classroomId}/payments/statistics", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public PaymentService.PaymentStatistics getPaymentStatistics(
            @Parameter(description = "Classroom ID") @PathVariable String classroomId,
            @Parameter(description = "Start date (YYYY-MM-DD)") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") String startDate,
            @Parameter(description = "End date (YYYY-MM-DD)") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") String endDate,
            @AuthenticationPrincipal Jwt userPrincipal) {

        // Get teacher from Cognito token
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());

        return paymentService.getPaymentStatistics(classroomId, startDate, endDate, teacher.getId());
    }

    @Operation(summary = "Get payment summary for classroom", description = "Get a quick summary of payments for a classroom (count and total amount)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Payment summary retrieved successfully"),
        @ApiResponse(responseCode = "403", description = "Not authorized to view payments for this classroom"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/classrooms/{classroomId}/payments/summary", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public PaymentSummary getPaymentSummaryByClassroom(
            @Parameter(description = "Classroom ID") @PathVariable String classroomId,
            @AuthenticationPrincipal Jwt userPrincipal) {

        // Get teacher from Cognito token
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());

        return paymentService.getPaymentSummaryByClassroom(classroomId, teacher.getId());
    }

    @Operation(summary = "Get simple payments by classroom", description = "Retrieve basic payment records for a classroom (lightweight version)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Payments retrieved successfully"),
        @ApiResponse(responseCode = "403", description = "Not authorized to view payments for this classroom"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/classrooms/{classroomId}/payments/simple", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<Payment> getSimplePaymentsByClassroom(
            @Parameter(description = "Classroom ID") @PathVariable String classroomId,
            @AuthenticationPrincipal Jwt userPrincipal) {

        // Get teacher from Cognito token
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());

        return paymentService.getSimplePaymentsByClassroom(classroomId, teacher.getId());
    }

    @Operation(summary = "Get payments by classroom with pagination", description = "Retrieve payment records for a classroom with pagination support")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Payments retrieved successfully"),
        @ApiResponse(responseCode = "403", description = "Not authorized to view payments for this classroom"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/classrooms/{classroomId}/payments/paginated", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public Page<Payment> getPaymentsByClassroomPaginated(
            @Parameter(description = "Classroom ID") @PathVariable String classroomId,
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "Sort by field") @RequestParam(defaultValue = "paymentDate") String sortBy,
            @Parameter(description = "Sort direction (asc/desc)") @RequestParam(defaultValue = "desc") String sortDir,
            @AuthenticationPrincipal Jwt userPrincipal) {

        // Get teacher from Cognito token
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());

        // Create Pageable object
        Sort sort = sortDir.equalsIgnoreCase("desc") ?
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);

        return paymentService.getPaymentsByClassroomPaginated(classroomId, teacher.getId(), pageable);
    }

    @Operation(summary = "Delete payment record", description = "Delete a payment record")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "Payment record deleted successfully"),
        @ApiResponse(responseCode = "404", description = "Payment record not found"),
        @ApiResponse(responseCode = "403", description = "Not authorized to delete this payment record"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @DeleteMapping(value = "/payments/{paymentId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void deletePayment(
            @Parameter(description = "Payment ID") @PathVariable String paymentId,
            @AuthenticationPrincipal Jwt userPrincipal) {

        // Get teacher from Cognito token
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());

        paymentService.deletePayment(paymentId, teacher.getId());
    }

    @Operation(summary = "Get payment analytics",
               description = "Get comprehensive payment analytics for the authenticated teacher including revenue, pending amounts, overdue payments, and class-wise statistics. Optional date range parameters (YYYY-MM-DD format). If not provided, defaults to current year.")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Payment analytics retrieved successfully",
                content = @Content(schema = @Schema(implementation = PaymentAnalytics.class))),
        @ApiResponse(responseCode = "400", description = "Invalid date format (use YYYY-MM-DD)"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Only teachers can access payment analytics")
    })
    @GetMapping(value = "/analytics", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public PaymentAnalytics getPaymentAnalytics(
            @Parameter(description = "Start date for analytics (YYYY-MM-DD format). If not provided, defaults to start of current year")
            @RequestParam(required = false) String startDate,
            @Parameter(description = "End date for analytics (YYYY-MM-DD format). If not provided, defaults to current date")
            @RequestParam(required = false) String endDate,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {

        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());

        // Verify user is a teacher
        boolean isTeacher = teacher.getRoles().stream()
                .anyMatch(role -> role.getRole() == RoleType.TEACHER);

        if (!isTeacher) {
            throw new AccessDeniedException("Only teachers can access payment analytics");
        }

        // Validate date format if provided
        if (startDate != null) {
            validateDateFormat(startDate, "startDate");
        }
        if (endDate != null) {
            validateDateFormat(endDate, "endDate");
        }

        // Validate date range if both provided
        if (startDate != null && endDate != null) {
            validateDateRange(startDate, endDate);
        }

        return paymentService.getPaymentAnalytics(teacher.getId(), startDate, endDate);
    }

    /**
     * Validate date format (YYYY-MM-DD)
     */
    private void validateDateFormat(String date, String paramName) {
        try {
            java.time.LocalDate.parse(date);
        } catch (java.time.format.DateTimeParseException e) {
            throw new IllegalArgumentException("Invalid " + paramName + " format. Use YYYY-MM-DD format. Example: 2024-01-01");
        }
    }

    /**
     * Validate that start date is before or equal to end date
     */
    private void validateDateRange(String startDate, String endDate) {
        try {
            java.time.LocalDate start = java.time.LocalDate.parse(startDate);
            java.time.LocalDate end = java.time.LocalDate.parse(endDate);

            if (start.isAfter(end)) {
                throw new IllegalArgumentException("Start date (" + startDate + ") must be before or equal to end date (" + endDate + ")");
            }

            // Optional: Check if date range is reasonable (not too far in the past or future)
            java.time.LocalDate now = java.time.LocalDate.now();
            if (start.isBefore(now.minusYears(10))) {
                throw new IllegalArgumentException("Start date cannot be more than 10 years in the past");
            }
            if (end.isAfter(now.plusYears(1))) {
                throw new IllegalArgumentException("End date cannot be more than 1 year in the future");
            }

        } catch (java.time.format.DateTimeParseException e) {
            throw new IllegalArgumentException("Invalid date format in date range validation");
        }
    }
}
