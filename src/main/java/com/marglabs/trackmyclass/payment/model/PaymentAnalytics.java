package com.marglabs.trackmyclass.payment.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
public class PaymentAnalytics {
    
    // Overall statistics
    private BigDecimal totalRevenue;
    private BigDecimal totalPaid;
    private BigDecimal totalPending;
    private BigDecimal totalOverdue;
    
    // Student payment information
    private List<StudentPaymentInfo> studentsNotPaidLast3Months;
    
    // Class-wise statistics
    private List<ClassPaymentStats> classStats;
    
    // Summary counts
    private int totalClasses;
    private int totalStudents;
    private int studentsWithOverduePayments;
    private int studentsWithPendingPayments;
    
    // Date range for the analytics
    private String analyticsStartDate;
    private String analyticsEndDate;
    private long generatedDate;
}
