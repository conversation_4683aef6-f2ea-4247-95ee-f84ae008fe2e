package com.marglabs.trackmyclass.payment.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.math.RoundingMode;

@Data
@Accessors(chain = true)
public class PaymentSummary {

    private String classroomId;
    private String classroomName;
    private long totalPayments;
    private long completedPayments;
    private long pendingPayments;
    private BigDecimal totalAmount;
    private BigDecimal completedAmount;
    private BigDecimal pendingAmount;

    // Calculated properties
    public double getCompletionRate() {
        if (totalPayments == 0) return 0.0;
        return (double) completedPayments / totalPayments * 100;
    }

    public BigDecimal getAveragePaymentAmount() {
        if (completedPayments == 0) return BigDecimal.ZERO;
        return completedAmount.divide(BigDecimal.valueOf(completedPayments), 2, RoundingMode.HALF_UP);
    }
}
