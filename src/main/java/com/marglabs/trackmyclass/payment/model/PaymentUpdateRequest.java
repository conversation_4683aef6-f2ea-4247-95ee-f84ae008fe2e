package com.marglabs.trackmyclass.payment.model;

import jakarta.validation.constraints.*;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class PaymentUpdateRequest {

    private String scheduleId; // Optional - link payment to specific class schedule

    @DecimalMin(value = "0.01", message = "Amount must be greater than 0")
    @Digits(integer = 8, fraction = 2, message = "Amount must have at most 8 digits before decimal and 2 after")
    private BigDecimal amount;

    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "Payment date must be in format YYYY-MM-DD")
    private String paymentDate;

    @Size(max = 500, message = "Description cannot exceed 500 characters")
    private String description;

    @Size(max = 100, message = "Receipt number cannot exceed 100 characters")
    private String receiptNumber;

    private PaymentMethod paymentMethod;

    private PaymentStatus status;

    @Size(max = 1000, message = "Notes cannot exceed 1000 characters")
    private String notes;
}
