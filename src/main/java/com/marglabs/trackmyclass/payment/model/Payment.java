package com.marglabs.trackmyclass.payment.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.Instant;

@Data
@Accessors(chain = true)
public class Payment {

    private String id;
    private String classroomId;
    private String scheduleId;
    private String studentId;
    private String teacherId;
    private BigDecimal amount;
    private String paymentDate; // Format: YYYY-MM-DD
    private String description;
    private String receiptNumber;
    private PaymentMethod paymentMethod;
    private PaymentStatus status;
    private String notes;
    private String recordedBy;
    private Instant createdDate;
    private Instant updatedDate;

    // Transient fields for display
    private String studentName;
    private String studentEmail;
    private String classroomName;
    private String teacherName;
    private String recordedByName;
}
