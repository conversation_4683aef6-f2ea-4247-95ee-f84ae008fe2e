package com.marglabs.trackmyclass.payment.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class ClassPaymentStats {
    
    private String classroomId;
    private String className;
    private String subjectName;
    private BigDecimal amountMadeThisYear;
    private BigDecimal pendingAmount;
    private BigDecimal totalRevenue; // Expected total revenue
    private int studentCount;
    private int studentsWithPendingPayments;
    private int studentsWithOverduePayments;
    private BigDecimal averagePaymentPerStudent;
    private String lastPaymentDate; // YYYY-MM-DD format
    private double collectionRate; // Percentage of payments collected
}
