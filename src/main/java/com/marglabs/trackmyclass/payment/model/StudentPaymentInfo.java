package com.marglabs.trackmyclass.payment.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class StudentPaymentInfo {
    
    private String studentId;
    private String studentName;
    private String studentEmail;
    private String classroomId;
    private String className;
    private BigDecimal totalOwed;
    private BigDecimal totalPaid;
    private BigDecimal pendingAmount;
    private String lastPaymentDate; // YYYY-MM-DD format
    private int daysSinceLastPayment;
    private String enrollmentDate; // YYYY-MM-DD format
    private boolean isOverdue;
}
