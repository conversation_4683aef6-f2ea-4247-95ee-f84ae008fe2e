package com.marglabs.trackmyclass.payment.dao;

import com.marglabs.common.rest.error.EntityNotFoundException;
import com.marglabs.trackmyclass.payment.entity.PaymentEntity;
import com.marglabs.trackmyclass.payment.model.PaymentMethod;
import com.marglabs.trackmyclass.payment.model.PaymentStatus;
import com.marglabs.trackmyclass.payment.repository.PaymentRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

@Component
public class PaymentDao {

    @Autowired
    private PaymentRepository repository;

    public PaymentEntity create(PaymentEntity entity) {
        // Timestamps are set automatically by @CreationTimestamp and @UpdateTimestamp
        return repository.save(entity);
    }

    public PaymentEntity update(PaymentEntity entity) {
        // UpdateTimestamp is set automatically by @UpdateTimestamp
        return repository.save(entity);
    }

    public PaymentEntity getById(String id) {
        return repository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException(HttpStatus.NOT_FOUND, "PAYMENT", "PAYMENT_NOT_FOUND",
                        "payment_not_found_details", id));
    }

    public Optional<PaymentEntity> findById(String id) {
        return repository.findById(id);
    }

    public List<PaymentEntity> getAll() {
        return repository.findAll();
    }

    public Page<PaymentEntity> getAll(Pageable pageable) {
        return repository.findAll(pageable);
    }

    public List<PaymentEntity> getByClassroom(String classroomId) {
        return repository.findByClassroomIdOrderByPaymentDateDesc(classroomId);
    }

    public List<PaymentEntity> getByStudent(String studentId) {
        return repository.findByStudentIdOrderByPaymentDateDesc(studentId);
    }

    public List<PaymentEntity> getByTeacher(String teacherId) {
        return repository.findByTeacherIdOrderByPaymentDateDesc(teacherId);
    }

    public List<PaymentEntity> getByClassroomAndDateRange(String classroomId, String startDate, String endDate) {
        return repository.findByClassroomIdAndDateRange(classroomId, startDate, endDate);
    }

    public List<PaymentEntity> getByStudentAndDateRange(String studentId, String startDate, String endDate) {
        return repository.findByStudentIdAndDateRange(studentId, startDate, endDate);
    }

    public List<PaymentEntity> getByTeacherAndDateRange(String teacherId, String startDate, String endDate) {
        return repository.findByTeacherIdAndDateRange(teacherId, startDate, endDate);
    }

    public List<PaymentEntity> getByClassroomAndStatus(String classroomId, PaymentStatus status) {
        return repository.findByClassroomIdAndStatus(classroomId, status);
    }

    public List<PaymentEntity> getByClassroomAndPaymentMethod(String classroomId, PaymentMethod paymentMethod) {
        return repository.findByClassroomIdAndPaymentMethod(classroomId, paymentMethod);
    }

    public Optional<PaymentEntity> findByReceiptNumber(String receiptNumber) {
        return repository.findByReceiptNumber(receiptNumber);
    }

    public List<PaymentEntity> getByClassroomAndStudent(String classroomId, String studentId) {
        return repository.findByClassroomIdAndStudentIdOrderByPaymentDateDesc(classroomId, studentId);
    }

    public List<PaymentEntity> getBySchedule(String scheduleId) {
        return repository.findByScheduleIdOrderByPaymentDateDesc(scheduleId);
    }

    public List<PaymentEntity> getByScheduleAndStudent(String scheduleId, String studentId) {
        return repository.findByScheduleIdAndStudentIdOrderByPaymentDateDesc(scheduleId, studentId);
    }

    public List<Object[]> getPaymentStatsByClassroom(String classroomId, String startDate, String endDate) {
        return repository.getPaymentStatsByClassroom(classroomId, startDate, endDate);
    }

    public List<Object[]> getPaymentStatsByMethod(String classroomId, String startDate, String endDate) {
        return repository.getPaymentStatsByMethod(classroomId, startDate, endDate);
    }

    public BigDecimal getTotalAmountByClassroom(String classroomId, String startDate, String endDate) {
        BigDecimal total = repository.getTotalAmountByClassroom(classroomId, startDate, endDate);
        return total != null ? total : BigDecimal.ZERO;
    }

    public BigDecimal getTotalAmountByStudent(String studentId, String startDate, String endDate) {
        BigDecimal total = repository.getTotalAmountByStudent(studentId, startDate, endDate);
        return total != null ? total : BigDecimal.ZERO;
    }

    public Page<PaymentEntity> getByClassroomPaginated(String classroomId, Pageable pageable) {
        return repository.findByClassroomIdOrderByPaymentDateDesc(classroomId, pageable);
    }

    public Page<PaymentEntity> getByTeacherPaginated(String teacherId, Pageable pageable) {
        return repository.findByTeacherIdOrderByPaymentDateDesc(teacherId, pageable);
    }

    public long countByClassroom(String classroomId) {
        return repository.countByClassroomId(classroomId);
    }

    public long countByClassroomAndStatus(String classroomId, PaymentStatus status) {
        return repository.countByClassroomIdAndStatus(classroomId, status);
    }

    public void deleteById(String id) {
        try {
            repository.deleteById(id);
        } catch (EmptyResultDataAccessException e) {
            throw new EntityNotFoundException(HttpStatus.NOT_FOUND, "PAYMENT", "PAYMENT_NOT_FOUND",
                    "payment_not_found_details", id);
        }
    }

    public BigDecimal calculateTotalPaymentsThisYear(String teacherId, String startOfYear) {
        BigDecimal total = repository.calculateTotalPaymentsThisYear(teacherId, startOfYear);
        return total != null ? total : BigDecimal.ZERO;
    }

    public BigDecimal calculateTotalPaymentsByClassroomThisYear(String teacherId, String classroomId, String startOfYear) {
        BigDecimal total = repository.calculateTotalPaymentsByClassroomThisYear(teacherId, classroomId, startOfYear);
        return total != null ? total : BigDecimal.ZERO;
    }

    public String findLastPaymentDateByStudentAndClassroom(String studentId, String classroomId) {
        return repository.findLastPaymentDateByStudentAndClassroom(studentId, classroomId);
    }

    public BigDecimal calculateTotalPaymentsByStudentAndClassroom(String studentId, String classroomId) {
        BigDecimal total = repository.calculateTotalPaymentsByStudentAndClassroom(studentId, classroomId);
        return total != null ? total : BigDecimal.ZERO;
    }

    public List<String> findStudentsWithNoRecentPayments(String teacherId, String cutoffDate) {
        return repository.findStudentsWithNoRecentPayments(teacherId, cutoffDate);
    }

    public List<Object[]> getPaymentStatsByClassroomForTeacher(String teacherId, String startOfYear) {
        return repository.getPaymentStatsByClassroom(teacherId, startOfYear);
    }
    
    public long countCompletedPaymentsByClassroom(String classroomId) {
        return repository.countByClassroomIdAndStatus(classroomId, PaymentStatus.COMPLETED);
    }
    
    public long countDistinctStudentsWithCompletedPayments(String classroomId) {
        return repository.countDistinctStudentsWithCompletedPayments(classroomId);
    }
    
    public BigDecimal getTotalIncomeByTeacher(String teacherId) {
        return repository.getTotalIncomeByTeacher(teacherId);
    }
    
    public BigDecimal getTotalPaymentsByStudent(String studentId) {
        return repository.getTotalPaymentsByStudent(studentId);
    }
}
