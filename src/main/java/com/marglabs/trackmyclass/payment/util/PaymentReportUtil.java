package com.marglabs.trackmyclass.payment.util;

import com.marglabs.trackmyclass.payment.model.PaymentAnalytics;
import com.marglabs.trackmyclass.user.model.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Component
public class PaymentReportUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(PaymentReportUtil.class);
    
    /**
     * Generate PDF report from payment analytics
     */
    public byte[] generatePdfReport(PaymentAnalytics analytics, User teacher) {
        logger.info("Generating PDF report for teacher: {}", teacher.getName());
        
        try {
            // Create a simple HTML-to-PDF conversion
            // In a production environment, you would use libraries like iText, Flying Saucer, or similar
            String htmlContent = generateHtmlContent(analytics, teacher);
            
            // For now, return HTML as bytes (in production, convert to actual PDF)
            // You can use libraries like:
            // - iText 7 (com.itextpdf:itext7-core)
            // - Flying Saucer (org.xhtmlrenderer:flying-saucer-pdf-itext5)
            // - wkhtmltopdf wrapper
            
            return convertHtmlToPdf(htmlContent);
            
        } catch (Exception e) {
            logger.error("Failed to generate PDF report: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to generate PDF report", e);
        }
    }
    
    /**
     * Generate CSV report from payment analytics
     */
    public byte[] generateCsvReport(PaymentAnalytics analytics, User teacher) {
        StringBuilder csv = new StringBuilder();
        
        // Header information
        csv.append("Payment Analytics Report\n");
        csv.append("Teacher: ").append(teacher.getName()).append("\n");
        csv.append("Period: ").append(analytics.getAnalyticsStartDate()).append(" to ").append(analytics.getAnalyticsEndDate()).append("\n");
        csv.append("Generated: ").append(java.time.Instant.ofEpochMilli(analytics.getGeneratedDate()).toString()).append("\n\n");
        
        // Summary statistics
        csv.append("SUMMARY STATISTICS\n");
        csv.append("Metric,Amount\n");
        csv.append("Total Revenue,").append(analytics.getTotalRevenue()).append("\n");
        csv.append("Total Paid,").append(analytics.getTotalPaid()).append("\n");
        csv.append("Total Pending,").append(analytics.getTotalPending()).append("\n");
        csv.append("Total Overdue,").append(analytics.getTotalOverdue()).append("\n");
        csv.append("Total Classes,").append(analytics.getTotalClasses()).append("\n");
        csv.append("Total Students,").append(analytics.getTotalStudents()).append("\n");
        csv.append("Students with Pending Payments,").append(analytics.getStudentsWithPendingPayments()).append("\n");
        csv.append("Students with Overdue Payments,").append(analytics.getStudentsWithOverduePayments()).append("\n\n");
        
        // Class-wise statistics
        csv.append("CLASS-WISE STATISTICS\n");
        csv.append("Class Name,Subject,Amount Made,Pending Amount,Total Revenue,Student Count,Collection Rate (%),Last Payment Date\n");
        
        if (analytics.getClassStats() != null) {
            for (var classStats : analytics.getClassStats()) {
                csv.append("\"").append(classStats.getClassName()).append("\",");
                csv.append("\"").append(classStats.getSubjectName()).append("\",");
                csv.append(classStats.getAmountMadeThisYear()).append(",");
                csv.append(classStats.getPendingAmount()).append(",");
                csv.append(classStats.getTotalRevenue()).append(",");
                csv.append(classStats.getStudentCount()).append(",");
                csv.append(String.format("%.2f", classStats.getCollectionRate())).append(",");
                csv.append(classStats.getLastPaymentDate() != null ? classStats.getLastPaymentDate() : "N/A").append("\n");
            }
        }
        
        csv.append("\n");
        
        // Students not paid in last 3 months
        csv.append("STUDENTS NOT PAID IN LAST 3 MONTHS\n");
        csv.append("Student Name,Email,Class Name,Total Owed,Total Paid,Pending Amount,Last Payment Date,Days Since Last Payment\n");
        
        if (analytics.getStudentsNotPaidLast3Months() != null) {
            for (var student : analytics.getStudentsNotPaidLast3Months()) {
                csv.append("\"").append(student.getStudentName()).append("\",");
                csv.append("\"").append(student.getStudentEmail()).append("\",");
                csv.append("\"").append(student.getClassName()).append("\",");
                csv.append(student.getTotalOwed()).append(",");
                csv.append(student.getTotalPaid()).append(",");
                csv.append(student.getPendingAmount()).append(",");
                csv.append(student.getLastPaymentDate() != null ? student.getLastPaymentDate() : "N/A").append(",");
                csv.append(student.getDaysSinceLastPayment()).append("\n");
            }
        }
        
        return csv.toString().getBytes(java.nio.charset.StandardCharsets.UTF_8);
    }
    
    /**
     * Generate filename with date range
     */
    public String generateFileName(String baseName, String startDate, String endDate, String extension) {
        StringBuilder fileName = new StringBuilder(baseName);
        
        if (startDate != null && endDate != null) {
            fileName.append("_").append(startDate).append("_to_").append(endDate);
        } else {
            // Use current year as default
            String currentYear = String.valueOf(java.time.LocalDate.now().getYear());
            fileName.append("_").append(currentYear);
        }
        
        // Add timestamp to make filename unique
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        fileName.append("_").append(timestamp);
        
        fileName.append(".").append(extension);
        
        return fileName.toString();
    }
    
    /**
     * Generate HTML content for PDF conversion
     */
    private String generateHtmlContent(PaymentAnalytics analytics, User teacher) {
        StringBuilder html = new StringBuilder();
        
        html.append("<!DOCTYPE html>");
        html.append("<html><head>");
        html.append("<meta charset='UTF-8'>");
        html.append("<title>Payment Analytics Report</title>");
        html.append("<style>");
        html.append("body { font-family: Arial, sans-serif; margin: 20px; }");
        html.append("h1 { color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px; }");
        html.append("h2 { color: #34495e; margin-top: 30px; }");
        html.append("table { border-collapse: collapse; width: 100%; margin: 15px 0; }");
        html.append("th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }");
        html.append("th { background-color: #f2f2f2; font-weight: bold; }");
        html.append("tr:nth-child(even) { background-color: #f9f9f9; }");
        html.append(".summary-table { width: 50%; }");
        html.append(".amount { text-align: right; }");
        html.append(".header-info { background-color: #ecf0f1; padding: 15px; border-radius: 5px; margin-bottom: 20px; }");
        html.append("</style>");
        html.append("</head><body>");
        
        // Header
        html.append("<div class='header-info'>");
        html.append("<h1>Payment Analytics Report</h1>");
        html.append("<p><strong>Teacher:</strong> ").append(teacher.getName()).append("</p>");
        html.append("<p><strong>Period:</strong> ").append(analytics.getAnalyticsStartDate()).append(" to ").append(analytics.getAnalyticsEndDate()).append("</p>");
        html.append("<p><strong>Generated:</strong> ").append(java.time.Instant.ofEpochMilli(analytics.getGeneratedDate()).toString()).append("</p>");
        html.append("</div>");
        
        // Summary Statistics
        html.append("<h2>Summary Statistics</h2>");
        html.append("<table class='summary-table'>");
        html.append("<tr><th>Metric</th><th class='amount'>Amount</th></tr>");
        html.append("<tr><td>Total Revenue</td><td class='amount'>$").append(analytics.getTotalRevenue()).append("</td></tr>");
        html.append("<tr><td>Total Paid</td><td class='amount'>$").append(analytics.getTotalPaid()).append("</td></tr>");
        html.append("<tr><td>Total Pending</td><td class='amount'>$").append(analytics.getTotalPending()).append("</td></tr>");
        html.append("<tr><td>Total Overdue</td><td class='amount'>$").append(analytics.getTotalOverdue()).append("</td></tr>");
        html.append("<tr><td>Total Classes</td><td class='amount'>").append(analytics.getTotalClasses()).append("</td></tr>");
        html.append("<tr><td>Total Students</td><td class='amount'>").append(analytics.getTotalStudents()).append("</td></tr>");
        html.append("<tr><td>Students with Pending</td><td class='amount'>").append(analytics.getStudentsWithPendingPayments()).append("</td></tr>");
        html.append("<tr><td>Students with Overdue</td><td class='amount'>").append(analytics.getStudentsWithOverduePayments()).append("</td></tr>");
        html.append("</table>");
        
        // Class-wise Statistics
        html.append("<h2>Class-wise Statistics</h2>");
        html.append("<table>");
        html.append("<tr><th>Class Name</th><th>Subject</th><th>Amount Made</th><th>Pending</th><th>Total Revenue</th><th>Students</th><th>Collection Rate</th><th>Last Payment</th></tr>");
        
        if (analytics.getClassStats() != null) {
            for (var classStats : analytics.getClassStats()) {
                html.append("<tr>");
                html.append("<td>").append(classStats.getClassName()).append("</td>");
                html.append("<td>").append(classStats.getSubjectName()).append("</td>");
                html.append("<td class='amount'>$").append(classStats.getAmountMadeThisYear()).append("</td>");
                html.append("<td class='amount'>$").append(classStats.getPendingAmount()).append("</td>");
                html.append("<td class='amount'>$").append(classStats.getTotalRevenue()).append("</td>");
                html.append("<td class='amount'>").append(classStats.getStudentCount()).append("</td>");
                html.append("<td class='amount'>").append(String.format("%.1f%%", classStats.getCollectionRate())).append("</td>");
                html.append("<td>").append(classStats.getLastPaymentDate() != null ? classStats.getLastPaymentDate() : "N/A").append("</td>");
                html.append("</tr>");
            }
        }
        html.append("</table>");
        
        // Students not paid in last 3 months
        if (analytics.getStudentsNotPaidLast3Months() != null && !analytics.getStudentsNotPaidLast3Months().isEmpty()) {
            html.append("<h2>Students Not Paid in Last 3 Months</h2>");
            html.append("<table>");
            html.append("<tr><th>Student Name</th><th>Email</th><th>Class</th><th>Total Owed</th><th>Total Paid</th><th>Pending</th><th>Last Payment</th><th>Days Since</th></tr>");
            
            for (var student : analytics.getStudentsNotPaidLast3Months()) {
                html.append("<tr>");
                html.append("<td>").append(student.getStudentName()).append("</td>");
                html.append("<td>").append(student.getStudentEmail()).append("</td>");
                html.append("<td>").append(student.getClassName()).append("</td>");
                html.append("<td class='amount'>$").append(student.getTotalOwed()).append("</td>");
                html.append("<td class='amount'>$").append(student.getTotalPaid()).append("</td>");
                html.append("<td class='amount'>$").append(student.getPendingAmount()).append("</td>");
                html.append("<td>").append(student.getLastPaymentDate() != null ? student.getLastPaymentDate() : "N/A").append("</td>");
                html.append("<td class='amount'>").append(student.getDaysSinceLastPayment()).append("</td>");
                html.append("</tr>");
            }
            html.append("</table>");
        }
        
        html.append("</body></html>");
        
        return html.toString();
    }
    
    /**
     * Convert HTML to PDF (simplified implementation)
     * In production, use libraries like iText, Flying Saucer, or wkhtmltopdf
     */
    private byte[] convertHtmlToPdf(String htmlContent) throws IOException {
        // This is a placeholder implementation
        // In production, you would use a proper HTML-to-PDF library
        
        // For demonstration, we'll return the HTML content as bytes
        // with PDF headers (this won't be a valid PDF, but shows the structure)
        
        StringBuilder pdfLikeContent = new StringBuilder();
        pdfLikeContent.append("%PDF-1.4\n");
        pdfLikeContent.append("% This is a simplified PDF representation\n");
        pdfLikeContent.append("% In production, use proper PDF generation libraries\n\n");
        pdfLikeContent.append("HTML Content:\n");
        pdfLikeContent.append(htmlContent);
        
        return pdfLikeContent.toString().getBytes(java.nio.charset.StandardCharsets.UTF_8);
        
        // Production implementation would look like:
        /*
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            // Using Flying Saucer + iText:
            ITextRenderer renderer = new ITextRenderer();
            renderer.setDocumentFromString(htmlContent);
            renderer.layout();
            renderer.createPDF(outputStream);
            return outputStream.toByteArray();
        }
        */
        
        // Or using wkhtmltopdf:
        /*
        ProcessBuilder pb = new ProcessBuilder("wkhtmltopdf", "--page-size", "A4", "-", "-");
        Process process = pb.start();
        
        try (OutputStreamWriter writer = new OutputStreamWriter(process.getOutputStream())) {
            writer.write(htmlContent);
        }
        
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            process.getInputStream().transferTo(outputStream);
            process.waitFor();
            return outputStream.toByteArray();
        }
        */
    }
}
