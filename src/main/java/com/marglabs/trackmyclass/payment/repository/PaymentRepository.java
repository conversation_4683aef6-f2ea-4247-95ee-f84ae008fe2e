package com.marglabs.trackmyclass.payment.repository;

import com.marglabs.trackmyclass.payment.entity.PaymentEntity;
import com.marglabs.trackmyclass.payment.model.PaymentMethod;
import com.marglabs.trackmyclass.payment.model.PaymentStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.data.rest.core.annotation.RepositoryRestResource;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

@Repository
@RepositoryRestResource(exported = false)
public interface PaymentRepository extends JpaRepository<PaymentEntity, String> {

    // Find payments by classroom
    List<PaymentEntity> findByClassroomIdOrderByPaymentDateDesc(String classroomId);

    // Find payments by student
    List<PaymentEntity> findByStudentIdOrderByPaymentDateDesc(String studentId);

    // Find payments by teacher
    List<PaymentEntity> findByTeacherIdOrderByPaymentDateDesc(String teacherId);

    // Find payments by classroom and date range
    @Query("SELECT p FROM PaymentEntity p WHERE p.classroomId = :classroomId " +
           "AND p.paymentDate BETWEEN :startDate AND :endDate ORDER BY p.paymentDate DESC")
    List<PaymentEntity> findByClassroomIdAndDateRange(@Param("classroomId") String classroomId,
                                                      @Param("startDate") String startDate,
                                                      @Param("endDate") String endDate);

    // Find payments by student and date range
    @Query("SELECT p FROM PaymentEntity p WHERE p.studentId = :studentId " +
           "AND p.paymentDate BETWEEN :startDate AND :endDate ORDER BY p.paymentDate DESC")
    List<PaymentEntity> findByStudentIdAndDateRange(@Param("studentId") String studentId,
                                                    @Param("startDate") String startDate,
                                                    @Param("endDate") String endDate);

    // Find payments by teacher and date range
    @Query("SELECT p FROM PaymentEntity p WHERE p.teacherId = :teacherId " +
           "AND p.paymentDate BETWEEN :startDate AND :endDate ORDER BY p.paymentDate DESC")
    List<PaymentEntity> findByTeacherIdAndDateRange(@Param("teacherId") String teacherId,
                                                    @Param("startDate") String startDate,
                                                    @Param("endDate") String endDate);

    // Find payments by status
    List<PaymentEntity> findByClassroomIdAndStatus(String classroomId, PaymentStatus status);

    // Find payments by payment method
    List<PaymentEntity> findByClassroomIdAndPaymentMethod(String classroomId, PaymentMethod paymentMethod);

    // Find payment by receipt number
    Optional<PaymentEntity> findByReceiptNumber(String receiptNumber);

    // Find payments by classroom and student
    List<PaymentEntity> findByClassroomIdAndStudentIdOrderByPaymentDateDesc(String classroomId, String studentId);

    // Find payments by schedule
    List<PaymentEntity> findByScheduleIdOrderByPaymentDateDesc(String scheduleId);

    // Find payments by schedule and student
    List<PaymentEntity> findByScheduleIdAndStudentIdOrderByPaymentDateDesc(String scheduleId, String studentId);

    // Get payment statistics by classroom
    @Query("SELECT p.status, COUNT(p), SUM(p.amount) FROM PaymentEntity p WHERE p.classroomId = :classroomId " +
           "AND p.paymentDate BETWEEN :startDate AND :endDate GROUP BY p.status")
    List<Object[]> getPaymentStatsByClassroom(@Param("classroomId") String classroomId,
                                             @Param("startDate") String startDate,
                                             @Param("endDate") String endDate);

    // Get payment statistics by payment method
    @Query("SELECT p.paymentMethod, COUNT(p), SUM(p.amount) FROM PaymentEntity p WHERE p.classroomId = :classroomId " +
           "AND p.paymentDate BETWEEN :startDate AND :endDate GROUP BY p.paymentMethod")
    List<Object[]> getPaymentStatsByMethod(@Param("classroomId") String classroomId,
                                          @Param("startDate") String startDate,
                                          @Param("endDate") String endDate);

    // Get total amount by classroom
    @Query("SELECT SUM(p.amount) FROM PaymentEntity p WHERE p.classroomId = :classroomId " +
           "AND p.status = 'COMPLETED' AND p.paymentDate BETWEEN :startDate AND :endDate")
    BigDecimal getTotalAmountByClassroom(@Param("classroomId") String classroomId,
                                        @Param("startDate") String startDate,
                                        @Param("endDate") String endDate);

    // Get total amount by student
    @Query("SELECT SUM(p.amount) FROM PaymentEntity p WHERE p.studentId = :studentId " +
           "AND p.status = 'COMPLETED' AND p.paymentDate BETWEEN :startDate AND :endDate")
    BigDecimal getTotalAmountByStudent(@Param("studentId") String studentId,
                                      @Param("startDate") String startDate,
                                      @Param("endDate") String endDate);

    // Find payments with pagination
    Page<PaymentEntity> findByClassroomIdOrderByPaymentDateDesc(String classroomId, Pageable pageable);

    // Find payments by teacher with pagination
    Page<PaymentEntity> findByTeacherIdOrderByPaymentDateDesc(String teacherId, Pageable pageable);

    // Count payments by classroom
    long countByClassroomId(String classroomId);

    // Count payments by status
    long countByClassroomIdAndStatus(String classroomId, PaymentStatus status);
    
    // Count distinct students with completed payments
    @Query("SELECT COUNT(DISTINCT p.studentId) FROM PaymentEntity p WHERE p.classroomId = :classroomId AND p.status = 'COMPLETED'")
    long countDistinctStudentsWithCompletedPayments(@Param("classroomId") String classroomId);
    
    // Get total income by teacher
    @Query("SELECT SUM(p.amount) FROM PaymentEntity p WHERE p.teacherId = :teacherId AND p.status = 'COMPLETED'")
    BigDecimal getTotalIncomeByTeacher(@Param("teacherId") String teacherId);
    
    // Get total payments by student
    @Query("SELECT SUM(p.amount) FROM PaymentEntity p WHERE p.studentId = :studentId AND p.status = 'COMPLETED'")
    BigDecimal getTotalPaymentsByStudent(@Param("studentId") String studentId);

    // Calculate total payments by teacher for current year
    @Query("SELECT SUM(p.amount) FROM PaymentEntity p WHERE p.teacherId = :teacherId AND p.paymentDate >= :startOfYear")
    BigDecimal calculateTotalPaymentsThisYear(@Param("teacherId") String teacherId, @Param("startOfYear") String startOfYear);

    // Calculate total payments by teacher and classroom for current year
    @Query("SELECT SUM(p.amount) FROM PaymentEntity p WHERE p.teacherId = :teacherId AND p.classroomId = :classroomId AND p.paymentDate >= :startOfYear")
    BigDecimal calculateTotalPaymentsByClassroomThisYear(@Param("teacherId") String teacherId,
                                                        @Param("classroomId") String classroomId,
                                                        @Param("startOfYear") String startOfYear);

    // Get last payment date by student and classroom
    @Query("SELECT MAX(p.paymentDate) FROM PaymentEntity p WHERE p.studentId = :studentId AND p.classroomId = :classroomId")
    String findLastPaymentDateByStudentAndClassroom(@Param("studentId") String studentId, @Param("classroomId") String classroomId);

    // Get total payments by student and classroom
    @Query("SELECT SUM(p.amount) FROM PaymentEntity p WHERE p.studentId = :studentId AND p.classroomId = :classroomId")
    BigDecimal calculateTotalPaymentsByStudentAndClassroom(@Param("studentId") String studentId, @Param("classroomId") String classroomId);

    // Get students who haven't paid in last N days for a teacher
    @Query("SELECT DISTINCT p.studentId FROM PaymentEntity p WHERE p.teacherId = :teacherId AND p.paymentDate < :cutoffDate")
    List<String> findStudentsWithNoRecentPayments(@Param("teacherId") String teacherId, @Param("cutoffDate") String cutoffDate);

    // Get payment statistics by classroom
    @Query("SELECT p.classroomId, SUM(p.amount), COUNT(p), MAX(p.paymentDate) " +
           "FROM PaymentEntity p WHERE p.teacherId = :teacherId AND p.paymentDate >= :startOfYear " +
           "GROUP BY p.classroomId")
    List<Object[]> getPaymentStatsByClassroom(@Param("teacherId") String teacherId, @Param("startOfYear") String startOfYear);
}
