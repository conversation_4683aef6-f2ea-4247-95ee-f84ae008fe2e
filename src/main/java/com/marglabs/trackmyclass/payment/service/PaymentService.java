package com.marglabs.trackmyclass.payment.service;

import com.marglabs.common.rest.error.GeneralException;
import com.marglabs.common.utils.IdGenerator;
import com.marglabs.trackmyclass.classroom.service.ClassroomService;
import com.marglabs.trackmyclass.payment.dao.PaymentDao;
import com.marglabs.trackmyclass.payment.entity.PaymentEntity;
import com.marglabs.trackmyclass.payment.mapper.PaymentMapper;
import com.marglabs.trackmyclass.payment.model.*;

import com.marglabs.trackmyclass.user.service.UserService;
import com.marglabs.trackmyclass.enrollment.service.EnrollmentService;
import com.marglabs.trackmyclass.enrollment.model.Enrollment;
import com.marglabs.trackmyclass.classroom.model.Classroom;
import com.marglabs.trackmyclass.classroom.model.ClassroomSummary;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Transactional
public class PaymentService {

    private static final Logger logger = LoggerFactory.getLogger(PaymentService.class);

    @Autowired
    private PaymentDao paymentDao;

    @Autowired
    private PaymentMapper mapper;

    @Autowired
    private IdGenerator idGenerator;

    @Autowired
    private ClassroomService classroomService;



    @Autowired
    private UserService userService;

    @Autowired
    private EnrollmentService enrollmentService;

    /**
     * Record a new payment
     */
    public Payment recordPayment(PaymentCreationRequest request, String classroomId, String teacherId) {
        // Validate the request
        validatePaymentRequest(request, classroomId, teacherId);

        // Check for duplicate receipt number if provided
        if (request.getReceiptNumber() != null && !request.getReceiptNumber().trim().isEmpty()) {
            var existingPayment = paymentDao.findByReceiptNumber(request.getReceiptNumber().trim());
            if (existingPayment.isPresent()) {
                throw new GeneralException(HttpStatus.CONFLICT, "PAYMENT", "DUPLICATE_RECEIPT_NUMBER",
                        "receipt_number_already_exists", request.getReceiptNumber());
            }
        }

        // Create payment entity
        PaymentEntity paymentEntity = new PaymentEntity()
                .setId(idGenerator.generateId())
                .setClassroomId(classroomId)
                .setScheduleId(request.getScheduleId())
                .setStudentId(request.getStudentId())
                .setTeacherId(teacherId)
                .setAmount(request.getAmount())
                .setPaymentDate(request.getPaymentDate())
                .setDescription(request.getDescription())
                .setReceiptNumber(request.getReceiptNumber() != null ? request.getReceiptNumber().trim() : null)
                .setPaymentMethod(request.getPaymentMethod())
                .setStatus(PaymentStatus.COMPLETED) // Default to completed for manual entries
                .setNotes(request.getNotes())
                .setRecordedBy(teacherId);

        // Save payment
        paymentEntity = paymentDao.create(paymentEntity);

        logger.info("Payment recorded: {} for student {} by teacher {}",
                paymentEntity.getId(), request.getStudentId(), teacherId);

        return enrichPaymentWithDetails(mapper.toDto(paymentEntity));
    }

    /**
     * Update existing payment
     */
    public Payment updatePayment(String paymentId, PaymentUpdateRequest request, String teacherId) {
        PaymentEntity existingPayment = paymentDao.getById(paymentId);

        // Verify teacher has access
        if (!existingPayment.getTeacherId().equals(teacherId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "PAYMENT", "UNAUTHORIZED_PAYMENT_ACCESS",
                    "teacher_not_authorized_for_payment", paymentId);
        }

        // Validate payment date if provided
        if (request.getPaymentDate() != null) {
            validateDate(request.getPaymentDate());
            existingPayment.setPaymentDate(request.getPaymentDate());
        }

        // Check for duplicate receipt number if changed
        if (request.getReceiptNumber() != null &&
            !request.getReceiptNumber().trim().equals(existingPayment.getReceiptNumber())) {
            var duplicateCheck = paymentDao.findByReceiptNumber(request.getReceiptNumber().trim());
            if (duplicateCheck.isPresent() && !duplicateCheck.get().getId().equals(paymentId)) {
                throw new GeneralException(HttpStatus.CONFLICT, "PAYMENT", "DUPLICATE_RECEIPT_NUMBER",
                        "receipt_number_already_exists", request.getReceiptNumber());
            }
            existingPayment.setReceiptNumber(request.getReceiptNumber().trim());
        }

        // Update other fields
        if (request.getScheduleId() != null) {
            existingPayment.setScheduleId(request.getScheduleId());
        }
        if (request.getAmount() != null) {
            existingPayment.setAmount(request.getAmount());
        }
        if (request.getDescription() != null) {
            existingPayment.setDescription(request.getDescription());
        }
        if (request.getPaymentMethod() != null) {
            existingPayment.setPaymentMethod(request.getPaymentMethod());
        }
        if (request.getStatus() != null) {
            existingPayment.setStatus(request.getStatus());
        }
        if (request.getNotes() != null) {
            existingPayment.setNotes(request.getNotes());
        }

        existingPayment.setRecordedBy(teacherId);

        PaymentEntity updatedEntity = paymentDao.update(existingPayment);

        logger.info("Payment updated: {} by teacher {}", paymentId, teacherId);

        return enrichPaymentWithDetails(mapper.toDto(updatedEntity));
    }

    /**
     * Get payment by ID
     */
    public Payment getPaymentById(String id, String teacherId) {
        PaymentEntity entity = paymentDao.getById(id);

        // Verify teacher has access
        if (!entity.getTeacherId().equals(teacherId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "PAYMENT", "UNAUTHORIZED_PAYMENT_ACCESS",
                    "teacher_not_authorized_for_payment", id);
        }

        return enrichPaymentWithDetails(mapper.toDto(entity));
    }

    /**
     * Get payments for a classroom
     */
    public List<Payment> getPaymentsByClassroom(String classroomId, String teacherId) {
        // Verify teacher owns the classroom
        var classroom = classroomService.getClassroomById(classroomId);
        if (!classroom.getTeacherId().equals(teacherId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "PAYMENT", "UNAUTHORIZED_CLASSROOM_ACCESS",
                    "teacher_not_authorized_for_classroom", classroomId);
        }

        List<PaymentEntity> entities = paymentDao.getByClassroom(classroomId);
        return entities.stream()
                .map(entity -> enrichPaymentWithDetails(mapper.toDto(entity)))
                .collect(Collectors.toList());
    }

    /**
     * Get payments for a classroom within a date range
     */
    public List<Payment> getPaymentsByClassroomAndDateRange(String classroomId, String startDate,
                                                           String endDate, String teacherId) {
        // Verify teacher owns the classroom
        var classroom = classroomService.getClassroomById(classroomId);
        if (!classroom.getTeacherId().equals(teacherId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "PAYMENT", "UNAUTHORIZED_CLASSROOM_ACCESS",
                    "teacher_not_authorized_for_classroom", classroomId);
        }

        List<PaymentEntity> entities = paymentDao.getByClassroomAndDateRange(classroomId, startDate, endDate);
        return entities.stream()
                .map(entity -> enrichPaymentWithDetails(mapper.toDto(entity)))
                .collect(Collectors.toList());
    }

    /**
     * Get payments for a student within a date range
     */
    public List<Payment> getPaymentsByStudentAndDateRange(String studentId, String startDate,
                                                         String endDate, String teacherId) {
        // Verify user exists
        userService.getUserById(studentId);

        List<PaymentEntity> entities = paymentDao.getByStudentAndDateRange(studentId, startDate, endDate);
        return entities.stream()
                .map(entity -> enrichPaymentWithDetails(mapper.toDto(entity)))
                .collect(Collectors.toList());
    }

    /**
     * Get payments for a specific student in a classroom
     */
    public List<Payment> getPaymentsByClassroomAndStudent(String classroomId, String studentId) {
        // Verify teacher owns the classroom
        Classroom classroom = classroomService.getClassroomById(classroomId);

        // Verify user exists
        userService.getUserById(studentId);

        List<PaymentEntity> entities = paymentDao.getByClassroomAndStudent(classroomId, studentId);
        return entities.stream()
                .map(entity -> enrichPaymentWithDetails(mapper.toDto(entity)))
                .collect(Collectors.toList());
    }

    /**
     * Get payments for a specific schedule
     */
    public List<Payment> getPaymentsBySchedule(String scheduleId, String teacherId) {
        // Note: We should validate that the teacher owns the schedule
        // For now, we'll get the payments and verify teacher access through the classroom
        List<PaymentEntity> entities = paymentDao.getBySchedule(scheduleId);

        // Filter payments to only include those from teacher's classrooms
        return entities.stream()
                .filter(entity -> entity.getTeacherId().equals(teacherId))
                .map(entity -> enrichPaymentWithDetails(mapper.toDto(entity)))
                .collect(Collectors.toList());
    }

    /**
     * Get payments for a specific schedule and student
     */
    public List<Payment> getPaymentsByScheduleAndStudent(String scheduleId, String studentId, String teacherId) {
        // Verify user exists
        userService.getUserById(studentId);

        List<PaymentEntity> entities = paymentDao.getByScheduleAndStudent(scheduleId, studentId);

        // Filter payments to only include those from teacher's classrooms
        return entities.stream()
                .filter(entity -> entity.getTeacherId().equals(teacherId))
                .map(entity -> enrichPaymentWithDetails(mapper.toDto(entity)))
                .collect(Collectors.toList());
    }

    /**
     * Get payments for a classroom (internal method without teacher authorization)
     * This method should be used carefully and only for administrative purposes
     */
    public List<Payment> getPaymentsByClassroomInternal(String classroomId) {
        List<PaymentEntity> entities = paymentDao.getByClassroom(classroomId);
        return entities.stream()
                .map(entity -> enrichPaymentWithDetails(mapper.toDto(entity)))
                .collect(Collectors.toList());
    }

    /**
     * Get payment summary for a classroom (count and total amount)
     */
    public PaymentSummary getPaymentSummaryByClassroom(String classroomId, String teacherId) {
        // Verify teacher owns the classroom
        var classroom = classroomService.getClassroomById(classroomId);
        if (!classroom.getTeacherId().equals(teacherId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "PAYMENT", "UNAUTHORIZED_CLASSROOM_ACCESS",
                    "teacher_not_authorized_for_classroom", classroomId);
        }

        List<PaymentEntity> entities = paymentDao.getByClassroom(classroomId);

        long totalPayments = entities.size();
        BigDecimal totalAmount = entities.stream()
                .filter(entity -> entity.getStatus() == PaymentStatus.COMPLETED)
                .map(PaymentEntity::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        long completedPayments = entities.stream()
                .mapToLong(entity -> entity.getStatus() == PaymentStatus.COMPLETED ? 1 : 0)
                .sum();

        return new PaymentSummary()
                .setClassroomId(classroomId)
                .setClassroomName(classroom.getClassName())
                .setTotalPayments(totalPayments)
                .setCompletedPayments(completedPayments)
                .setTotalAmount(totalAmount);
    }

    /**
     * Get simple payment list by classroom (lightweight version)
     * Returns basic payment information without enrichment for better performance
     */
    public List<Payment> getSimplePaymentsByClassroom(String classroomId, String teacherId) {
        // Verify teacher owns the classroom
        var classroom = classroomService.getClassroomById(classroomId);
        if (!classroom.getTeacherId().equals(teacherId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "PAYMENT", "UNAUTHORIZED_CLASSROOM_ACCESS",
                    "teacher_not_authorized_for_classroom", classroomId);
        }

        List<PaymentEntity> entities = paymentDao.getByClassroom(classroomId);
        return entities.stream()
                .map(mapper::toDto) // No enrichment for better performance
                .collect(Collectors.toList());
    }

    /**
     * Get payments by classroom with pagination
     */
    public Page<Payment> getPaymentsByClassroomPaginated(String classroomId, String teacherId, Pageable pageable) {
        // Verify teacher owns the classroom
        var classroom = classroomService.getClassroomById(classroomId);
        if (!classroom.getTeacherId().equals(teacherId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "PAYMENT", "UNAUTHORIZED_CLASSROOM_ACCESS",
                    "teacher_not_authorized_for_classroom", classroomId);
        }

        Page<PaymentEntity> entityPage = paymentDao.getByClassroomPaginated(classroomId, pageable);
        return entityPage.map(entity -> enrichPaymentWithDetails(mapper.toDto(entity)));
    }

    /**
     * Get payment statistics for a classroom
     */
    public PaymentStatistics getPaymentStatistics(String classroomId, String startDate,
                                                 String endDate, String teacherId) {
        // Verify teacher owns the classroom
        var classroom = classroomService.getClassroomById(classroomId);
        if (!classroom.getTeacherId().equals(teacherId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "PAYMENT", "UNAUTHORIZED_CLASSROOM_ACCESS",
                    "teacher_not_authorized_for_classroom", classroomId);
        }

        // Get payment statistics by status
        List<Object[]> statusStats = paymentDao.getPaymentStatsByClassroom(classroomId, startDate, endDate);

        Map<PaymentStatus, Long> statusCounts = new HashMap<>();
        Map<PaymentStatus, BigDecimal> statusAmounts = new HashMap<>();
        long totalPayments = 0;
        BigDecimal totalAmount = BigDecimal.ZERO;

        for (Object[] stat : statusStats) {
            PaymentStatus status = (PaymentStatus) stat[0];
            Long count = (Long) stat[1];
            BigDecimal amount = (BigDecimal) stat[2];

            statusCounts.put(status, count);
            statusAmounts.put(status, amount != null ? amount : BigDecimal.ZERO);
            totalPayments += count;
            totalAmount = totalAmount.add(amount != null ? amount : BigDecimal.ZERO);
        }

        // Get payment statistics by method
        List<Object[]> methodStats = paymentDao.getPaymentStatsByMethod(classroomId, startDate, endDate);

        Map<PaymentMethod, Long> methodCounts = new HashMap<>();
        Map<PaymentMethod, BigDecimal> methodAmounts = new HashMap<>();

        for (Object[] stat : methodStats) {
            PaymentMethod method = (PaymentMethod) stat[0];
            Long count = (Long) stat[1];
            BigDecimal amount = (BigDecimal) stat[2];

            methodCounts.put(method, count);
            methodAmounts.put(method, amount != null ? amount : BigDecimal.ZERO);
        }

        return new PaymentStatistics()
                .setClassroomId(classroomId)
                .setClassroomName(classroom.getClassName())
                .setStartDate(startDate)
                .setEndDate(endDate)
                .setTotalPayments(totalPayments)
                .setTotalAmount(totalAmount)
                .setCompletedCount(statusCounts.getOrDefault(PaymentStatus.COMPLETED, 0L))
                .setCompletedAmount(statusAmounts.getOrDefault(PaymentStatus.COMPLETED, BigDecimal.ZERO))
                .setPendingCount(statusCounts.getOrDefault(PaymentStatus.PENDING, 0L))
                .setPendingAmount(statusAmounts.getOrDefault(PaymentStatus.PENDING, BigDecimal.ZERO))
                .setFailedCount(statusCounts.getOrDefault(PaymentStatus.FAILED, 0L))
                .setFailedAmount(statusAmounts.getOrDefault(PaymentStatus.FAILED, BigDecimal.ZERO))
                .setCancelledCount(statusCounts.getOrDefault(PaymentStatus.CANCELLED, 0L))
                .setCancelledAmount(statusAmounts.getOrDefault(PaymentStatus.CANCELLED, BigDecimal.ZERO))
                .setRefundedCount(statusCounts.getOrDefault(PaymentStatus.REFUNDED, 0L))
                .setRefundedAmount(statusAmounts.getOrDefault(PaymentStatus.REFUNDED, BigDecimal.ZERO))
                .setMethodCounts(methodCounts)
                .setMethodAmounts(methodAmounts);
    }

    /**
     * Delete payment record
     */
    public void deletePayment(String paymentId, String teacherId) {
        PaymentEntity payment = paymentDao.getById(paymentId);

        // Verify teacher has access
        if (!payment.getTeacherId().equals(teacherId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "PAYMENT", "UNAUTHORIZED_PAYMENT_ACCESS",
                    "teacher_not_authorized_for_payment", paymentId);
        }

        paymentDao.deleteById(paymentId);
        logger.info("Payment record {} deleted by teacher {}", paymentId, teacherId);
    }

    // Private helper methods

    private void validatePaymentRequest(PaymentCreationRequest request, String classroomId, String teacherId) {
        // Verify classroom exists and teacher owns it
        var classroom = classroomService.getClassroomById(classroomId);
        if (!classroom.getTeacherId().equals(teacherId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "PAYMENT", "UNAUTHORIZED_CLASSROOM_ACCESS",
                    "teacher_not_authorized_for_classroom", classroomId);
        }

        // Verify user exists
        userService.getUserById(request.getStudentId());

        // Validate date
        validateDate(request.getPaymentDate());

        // Validate amount
        if (request.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new GeneralException(HttpStatus.BAD_REQUEST, "PAYMENT", "INVALID_PAYMENT_AMOUNT",
                    "payment_amount_must_be_positive");
        }
    }

    private void validateDate(String dateString) {
        try {
            LocalDate date = LocalDate.parse(dateString, DateTimeFormatter.ISO_LOCAL_DATE);
            LocalDate today = LocalDate.now();

            // Don't allow future dates beyond today
            if (date.isAfter(today)) {
                throw new GeneralException(HttpStatus.BAD_REQUEST, "PAYMENT", "INVALID_PAYMENT_DATE",
                        "payment_date_cannot_be_future");
            }

            // Don't allow dates too far in the past (e.g., more than 5 years)
            if (date.isBefore(today.minusYears(5))) {
                throw new GeneralException(HttpStatus.BAD_REQUEST, "PAYMENT", "INVALID_PAYMENT_DATE",
                        "payment_date_too_old");
            }
        } catch (DateTimeParseException e) {
            throw new GeneralException(HttpStatus.BAD_REQUEST, "PAYMENT", "INVALID_PAYMENT_DATE",
                    "invalid_payment_date_format");
        }
    }

    private Payment enrichPaymentWithDetails(Payment payment) {
        try {
            // Get user details
            var user = userService.getUserById(payment.getStudentId());
            payment.setStudentName(user.getName());
            payment.setStudentEmail(user.getEmail());

            // Get classroom details
            var classroom = classroomService.getClassroomById(payment.getClassroomId());
            payment.setClassroomName(classroom.getClassName());

            // Get teacher details
            var teacher = userService.getUserById(payment.getTeacherId());
            payment.setTeacherName(teacher.getName() != null ? teacher.getName() : teacher.getEmail());

            // Get recorded by details
            if (payment.getRecordedBy() != null) {
                var recordedByUser = userService.getUserById(payment.getRecordedBy());
                payment.setRecordedByName(recordedByUser.getName() != null ? recordedByUser.getName() : recordedByUser.getEmail());
            }

        } catch (Exception e) {
            logger.warn("Failed to enrich payment with details: {}", e.getMessage());
        }

        return payment;
    }

    /**
     * Get comprehensive payment analytics for a teacher with default date range (current year)
     */
    public PaymentAnalytics getPaymentAnalytics(String teacherId) {
        return getPaymentAnalytics(teacherId, null, null);
    }

    /**
     * Get comprehensive payment analytics for a teacher with custom date range
     */
    public PaymentAnalytics getPaymentAnalytics(String teacherId, String startDate, String endDate) {
        logger.info("Generating payment analytics for teacher: {} from {} to {}", teacherId, startDate, endDate);

        // Verify teacher exists
        userService.getUserById(teacherId);

        // Calculate date ranges with defaults
        String analyticsStartDate;
        String analyticsEndDate;
        String threeMonthsAgo;

        if (startDate != null && endDate != null) {
            // Use provided dates
            analyticsStartDate = startDate;
            analyticsEndDate = endDate;

            // Calculate 3 months ago from the end date
            try {
                LocalDate endLocalDate = LocalDate.parse(endDate);
                threeMonthsAgo = endLocalDate.minusMonths(3).toString();
            } catch (Exception e) {
                logger.warn("Failed to parse end date {}, using current date for 3 months calculation", endDate);
                threeMonthsAgo = LocalDate.now().minusMonths(3).toString();
            }
        } else {
            // Use default values (current year)
            String currentYear = String.valueOf(LocalDate.now().getYear());
            analyticsStartDate = currentYear + "-01-01";
            analyticsEndDate = LocalDate.now().toString();
            threeMonthsAgo = LocalDate.now().minusMonths(3).toString();
        }

        PaymentAnalytics analytics = new PaymentAnalytics();

        // Get teacher's classrooms
        List<ClassroomSummary> classroomSummaries = classroomService.getClassroomSummariesByTeacherId(teacherId);
        List<Classroom> teacherClassrooms = classroomSummaries.stream()
                .map(summary -> classroomService.getClassroomById(summary.getId()))
                .collect(java.util.stream.Collectors.toList());

        // Calculate overall statistics
        BigDecimal totalPaidInPeriod = paymentDao.calculateTotalPaymentsThisYear(teacherId, analyticsStartDate);
        analytics.setTotalPaid(totalPaidInPeriod);

        // Initialize totals
        BigDecimal totalRevenue = BigDecimal.ZERO;
        BigDecimal totalPending = BigDecimal.ZERO;
        BigDecimal totalOverdue = BigDecimal.ZERO;

        List<ClassPaymentStats> classStatsList = new ArrayList<>();
        List<StudentPaymentInfo> studentsNotPaidLast3Months = new ArrayList<>();

        int totalStudents = 0;
        int studentsWithOverdue = 0;
        int studentsWithPending = 0;

        // Process each classroom
        for (Classroom classroom : teacherClassrooms) {
            // Get enrollments for this classroom
            List<Enrollment> enrollments = enrollmentService.getEnrollmentsByClassroomId(classroom.getId());
            totalStudents += enrollments.size();

            // Calculate class payment statistics
            ClassPaymentStats classStats = calculateClassPaymentStats(classroom, enrollments, teacherId, analyticsStartDate, threeMonthsAgo);
            classStatsList.add(classStats);

            // Add to overall totals
            totalRevenue = totalRevenue.add(classStats.getTotalRevenue());
            totalPending = totalPending.add(classStats.getPendingAmount());

            // Process students for this classroom
            for (Enrollment enrollment : enrollments) {
                StudentPaymentInfo studentInfo = calculateStudentPaymentInfo(enrollment, classroom, threeMonthsAgo);

                // Check if student hasn't paid in last 3 months
                if (studentInfo.getDaysSinceLastPayment() >= 90) { // 3 months = ~90 days
                    studentsNotPaidLast3Months.add(studentInfo);
                }

                // Count overdue and pending students
                if (studentInfo.isOverdue()) {
                    studentsWithOverdue++;
                }

                if (studentInfo.getPendingAmount().compareTo(BigDecimal.ZERO) > 0) {
                    studentsWithPending++;
                }
            }
        }

        // Set all analytics data
        analytics.setTotalRevenue(totalRevenue)
                 .setTotalPending(totalPending)
                 .setTotalOverdue(totalOverdue) // For now, treating pending as overdue
                 .setStudentsNotPaidLast3Months(studentsNotPaidLast3Months)
                 .setClassStats(classStatsList)
                 .setTotalClasses(teacherClassrooms.size())
                 .setTotalStudents(totalStudents)
                 .setStudentsWithOverduePayments(studentsWithOverdue)
                 .setStudentsWithPendingPayments(studentsWithPending)
                 .setAnalyticsStartDate(analyticsStartDate)
                 .setAnalyticsEndDate(analyticsEndDate)
                 .setGeneratedDate(System.currentTimeMillis());

        logger.info("Successfully generated payment analytics for teacher: {} - {} classes, {} students",
                   teacherId, teacherClassrooms.size(), totalStudents);
        return analytics;
    }

    /**
     * Calculate payment statistics for a specific classroom
     */
    private ClassPaymentStats calculateClassPaymentStats(Classroom classroom, List<Enrollment> enrollments,
                                                        String teacherId, String startDate, String threeMonthsAgo) {

        // Calculate amount made in the specified period for this classroom
        BigDecimal amountMadeInPeriod = paymentDao.calculateTotalPaymentsByClassroomThisYear(teacherId, classroom.getId(), startDate);

        // For now, we'll estimate total revenue and pending amounts
        // In a real implementation, you might have fee structures to calculate expected revenue
        BigDecimal estimatedTotalRevenue = amountMadeInPeriod.multiply(new BigDecimal("1.2")); // Assume 20% more expected
        BigDecimal pendingAmount = estimatedTotalRevenue.subtract(amountMadeInPeriod);

        // Count students with pending/overdue payments
        int studentsWithPending = 0;
        int studentsWithOverdue = 0;

        for (Enrollment enrollment : enrollments) {
            BigDecimal studentPaid = paymentDao.calculateTotalPaymentsByStudentAndClassroom(enrollment.getUserId(), classroom.getId());
            String lastPaymentDate = paymentDao.findLastPaymentDateByStudentAndClassroom(enrollment.getUserId(), classroom.getId());

            // Simple logic: if student has paid less than expected average, they have pending
            BigDecimal expectedPerStudent = enrollments.size() > 0 ?
                estimatedTotalRevenue.divide(new BigDecimal(enrollments.size()), 2, BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO;

            if (studentPaid.compareTo(expectedPerStudent) < 0) {
                studentsWithPending++;

                // If no payment in last 3 months, consider overdue
                if (lastPaymentDate == null || lastPaymentDate.compareTo(threeMonthsAgo) < 0) {
                    studentsWithOverdue++;
                }
            }
        }

        // Calculate collection rate
        double collectionRate = estimatedTotalRevenue.compareTo(BigDecimal.ZERO) > 0 ?
            amountMadeInPeriod.divide(estimatedTotalRevenue, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100")).doubleValue() : 0.0;

        // Calculate average payment per student
        BigDecimal averagePaymentPerStudent = enrollments.size() > 0 ?
            amountMadeInPeriod.divide(new BigDecimal(enrollments.size()), 2, BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO;

        // Get last payment date for the classroom
        List<Object[]> classroomStats = paymentDao.getPaymentStatsByClassroomForTeacher(teacherId, startDate);
        String lastPaymentDate = null;
        for (Object[] stat : classroomStats) {
            if (classroom.getId().equals(stat[0])) {
                lastPaymentDate = (String) stat[3]; // MAX(payment_date)
                break;
            }
        }

        return new ClassPaymentStats()
                .setClassroomId(classroom.getId())
                .setClassName(classroom.getClassName())
                .setSubjectName(classroom.getSubjectName())
                .setAmountMadeThisYear(amountMadeInPeriod)
                .setPendingAmount(pendingAmount)
                .setTotalRevenue(estimatedTotalRevenue)
                .setStudentCount(enrollments.size())
                .setStudentsWithPendingPayments(studentsWithPending)
                .setStudentsWithOverduePayments(studentsWithOverdue)
                .setAveragePaymentPerStudent(averagePaymentPerStudent)
                .setLastPaymentDate(lastPaymentDate)
                .setCollectionRate(collectionRate);
    }

    /**
     * Calculate payment information for a specific student in a classroom
     */
    private StudentPaymentInfo calculateStudentPaymentInfo(Enrollment enrollment, Classroom classroom, String threeMonthsAgo) {
        String studentId = enrollment.getUserId();

        // Get student details
        var student = userService.getUserById(studentId);

        // Get payment information
        BigDecimal totalPaid = paymentDao.calculateTotalPaymentsByStudentAndClassroom(studentId, classroom.getId());
        String lastPaymentDate = paymentDao.findLastPaymentDateByStudentAndClassroom(studentId, classroom.getId());

        // Calculate days since last payment
        int daysSinceLastPayment = 0;
        if (lastPaymentDate != null) {
            try {
                LocalDate lastPayment = LocalDate.parse(lastPaymentDate);
                LocalDate today = LocalDate.now();
                daysSinceLastPayment = (int) java.time.temporal.ChronoUnit.DAYS.between(lastPayment, today);
            } catch (Exception e) {
                logger.warn("Failed to parse last payment date {} for student {}", lastPaymentDate, studentId);
                daysSinceLastPayment = 999; // Set high value if parsing fails
            }
        } else {
            // No payment found, calculate days since enrollment
            try {
                LocalDate enrollmentDate = LocalDate.ofEpochDay(enrollment.getCreatedDate() / (24 * 60 * 60 * 1000));
                LocalDate today = LocalDate.now();
                daysSinceLastPayment = (int) java.time.temporal.ChronoUnit.DAYS.between(enrollmentDate, today);
            } catch (Exception e) {
                daysSinceLastPayment = 999; // Set high value if parsing fails
            }
        }

        // Estimate total owed (this would ideally come from fee structures)
        // For now, we'll use a simple estimation
        BigDecimal estimatedTotalOwed = new BigDecimal("500.00"); // Default estimated amount per student
        BigDecimal pendingAmount = estimatedTotalOwed.subtract(totalPaid);
        if (pendingAmount.compareTo(BigDecimal.ZERO) < 0) {
            pendingAmount = BigDecimal.ZERO; // Don't show negative pending
        }

        // Determine if overdue (more than 90 days since last payment)
        boolean isOverdue = daysSinceLastPayment >= 90;

        return new StudentPaymentInfo()
                .setStudentId(studentId)
                .setStudentName(student.getName())
                .setStudentEmail(student.getEmail())
                .setClassroomId(classroom.getId())
                .setClassName(classroom.getClassName())
                .setTotalOwed(estimatedTotalOwed)
                .setTotalPaid(totalPaid)
                .setPendingAmount(pendingAmount)
                .setLastPaymentDate(lastPaymentDate)
                .setDaysSinceLastPayment(daysSinceLastPayment)
                .setEnrollmentDate(java.time.Instant.ofEpochMilli(enrollment.getCreatedDate()).toString().substring(0, 10))
                .setOverdue(isOverdue);
    }

    // Inner class for statistics
    public static class PaymentStatistics {
        private String classroomId;
        private String classroomName;
        private String startDate;
        private String endDate;
        private long totalPayments;
        private BigDecimal totalAmount;
        private long completedCount;
        private BigDecimal completedAmount;
        private long pendingCount;
        private BigDecimal pendingAmount;
        private long failedCount;
        private BigDecimal failedAmount;
        private long cancelledCount;
        private BigDecimal cancelledAmount;
        private long refundedCount;
        private BigDecimal refundedAmount;
        private Map<PaymentMethod, Long> methodCounts;
        private Map<PaymentMethod, BigDecimal> methodAmounts;

        // Getters and setters
        public String getClassroomId() { return classroomId; }
        public PaymentStatistics setClassroomId(String classroomId) { this.classroomId = classroomId; return this; }

        public String getClassroomName() { return classroomName; }
        public PaymentStatistics setClassroomName(String classroomName) { this.classroomName = classroomName; return this; }

        public String getStartDate() { return startDate; }
        public PaymentStatistics setStartDate(String startDate) { this.startDate = startDate; return this; }

        public String getEndDate() { return endDate; }
        public PaymentStatistics setEndDate(String endDate) { this.endDate = endDate; return this; }

        public long getTotalPayments() { return totalPayments; }
        public PaymentStatistics setTotalPayments(long totalPayments) { this.totalPayments = totalPayments; return this; }

        public BigDecimal getTotalAmount() { return totalAmount; }
        public PaymentStatistics setTotalAmount(BigDecimal totalAmount) { this.totalAmount = totalAmount; return this; }

        public long getCompletedCount() { return completedCount; }
        public PaymentStatistics setCompletedCount(long completedCount) { this.completedCount = completedCount; return this; }

        public BigDecimal getCompletedAmount() { return completedAmount; }
        public PaymentStatistics setCompletedAmount(BigDecimal completedAmount) { this.completedAmount = completedAmount; return this; }

        public long getPendingCount() { return pendingCount; }
        public PaymentStatistics setPendingCount(long pendingCount) { this.pendingCount = pendingCount; return this; }

        public BigDecimal getPendingAmount() { return pendingAmount; }
        public PaymentStatistics setPendingAmount(BigDecimal pendingAmount) { this.pendingAmount = pendingAmount; return this; }

        public long getFailedCount() { return failedCount; }
        public PaymentStatistics setFailedCount(long failedCount) { this.failedCount = failedCount; return this; }

        public BigDecimal getFailedAmount() { return failedAmount; }
        public PaymentStatistics setFailedAmount(BigDecimal failedAmount) { this.failedAmount = failedAmount; return this; }

        public long getCancelledCount() { return cancelledCount; }
        public PaymentStatistics setCancelledCount(long cancelledCount) { this.cancelledCount = cancelledCount; return this; }

        public BigDecimal getCancelledAmount() { return cancelledAmount; }
        public PaymentStatistics setCancelledAmount(BigDecimal cancelledAmount) { this.cancelledAmount = cancelledAmount; return this; }

        public long getRefundedCount() { return refundedCount; }
        public PaymentStatistics setRefundedCount(long refundedCount) { this.refundedCount = refundedCount; return this; }

        public BigDecimal getRefundedAmount() { return refundedAmount; }
        public PaymentStatistics setRefundedAmount(BigDecimal refundedAmount) { this.refundedAmount = refundedAmount; return this; }

        public Map<PaymentMethod, Long> getMethodCounts() { return methodCounts; }
        public PaymentStatistics setMethodCounts(Map<PaymentMethod, Long> methodCounts) { this.methodCounts = methodCounts; return this; }

        public Map<PaymentMethod, BigDecimal> getMethodAmounts() { return methodAmounts; }
        public PaymentStatistics setMethodAmounts(Map<PaymentMethod, BigDecimal> methodAmounts) { this.methodAmounts = methodAmounts; return this; }
    }
}
