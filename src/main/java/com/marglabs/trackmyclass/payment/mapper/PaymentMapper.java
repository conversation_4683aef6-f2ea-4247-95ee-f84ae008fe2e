package com.marglabs.trackmyclass.payment.mapper;

import com.marglabs.trackmyclass.payment.entity.PaymentEntity;
import com.marglabs.trackmyclass.payment.model.Payment;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface PaymentMapper {

    @Mapping(target = "studentName", ignore = true)
    @Mapping(target = "studentEmail", ignore = true)
    @Mapping(target = "classroomName", ignore = true)
    @Mapping(target = "teacherName", ignore = true)
    @Mapping(target = "recordedByName", ignore = true)
    Payment toDto(PaymentEntity entity);

    @Mapping(target = "createdDate", ignore = true)
    @Mapping(target = "updatedDate", ignore = true)
    PaymentEntity toEntity(Payment payment);
}
