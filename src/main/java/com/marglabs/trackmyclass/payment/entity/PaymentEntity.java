package com.marglabs.trackmyclass.payment.entity;

import com.marglabs.trackmyclass.payment.model.PaymentMethod;
import com.marglabs.trackmyclass.payment.model.PaymentStatus;
import jakarta.persistence.*;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.Instant;

@Entity
@Table(name = "payments")
@Data
@Accessors(chain = true)
public class PaymentEntity {

    @Id
    private String id;

    @Column(name = "classroom_id", nullable = false)
    private String classroomId;

    @Column(name = "schedule_id")
    private String scheduleId;

    @Column(name = "student_id", nullable = false)
    private String studentId;

    @Column(name = "teacher_id", nullable = false)
    private String teacherId;

    @Column(name = "amount", nullable = false, precision = 10, scale = 2)
    private BigDecimal amount;

    @Column(name = "payment_date", nullable = false)
    private String paymentDate; // Format: YYYY-MM-DD

    @Column(name = "description", length = 500)
    private String description;

    @Column(name = "receipt_number", length = 100)
    private String receiptNumber;

    @Enumerated(EnumType.STRING)
    @Column(name = "payment_method", nullable = false)
    private PaymentMethod paymentMethod;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private PaymentStatus status;

    @Column(name = "notes", length = 1000)
    private String notes;

    @Column(name = "recorded_by", nullable = false)
    private String recordedBy; // Teacher who recorded the payment

    @CreationTimestamp
    @Column(name = "created_date", nullable = false, updatable = false)
    private Instant createdDate;

    @UpdateTimestamp
    @Column(name = "updated_date", nullable = false)
    private Instant updatedDate;

    // Indexes for better query performance
    @Table(indexes = {
        @Index(name = "idx_payment_classroom", columnList = "classroom_id"),
        @Index(name = "idx_payment_schedule", columnList = "schedule_id"),
        @Index(name = "idx_payment_student", columnList = "student_id"),
        @Index(name = "idx_payment_teacher", columnList = "teacher_id"),
        @Index(name = "idx_payment_date", columnList = "payment_date"),
        @Index(name = "idx_payment_status", columnList = "status"),
        @Index(name = "idx_payment_receipt", columnList = "receipt_number")
    })
    public static class PaymentIndexes {}
}
