package com.marglabs.trackmyclass.workexperience.rest.controller;

import com.marglabs.trackmyclass.user.model.User;
import com.marglabs.trackmyclass.user.service.UserService;
import com.marglabs.trackmyclass.workexperience.model.WorkExperience;
import com.marglabs.trackmyclass.workexperience.model.WorkExperienceRequest;
import com.marglabs.trackmyclass.workexperience.service.WorkExperienceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/workExperience/v1")
@Tag(name = "Work Experience Management", description = "APIs for managing teacher work experience records")
public class WorkExperienceController {
    
    @Autowired
    private WorkExperienceService workExperienceService;
    
    @Autowired
    private UserService userService;
    
    @Operation(summary = "Add work experience", description = "Add a new work experience record for the authenticated user")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Work experience added successfully",
                content = @Content(schema = @Schema(implementation = WorkExperience.class))),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @PostMapping(value = "/work-experience", consumes = "application/json", produces = "application/json")
    @ResponseStatus(HttpStatus.CREATED)
    public WorkExperience addWorkExperience(
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal,
            @Parameter(description = "Work experience details") @Valid @RequestBody WorkExperienceRequest request) {

        User user = userService.getUserByCognitoId(userPrincipal.getSubject());
        return workExperienceService.addWorkExperience(request, user.getId());
    }
    
    @Operation(summary = "Update work experience", description = "Update an existing work experience record")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Work experience updated successfully",
                content = @Content(schema = @Schema(implementation = WorkExperience.class))),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Not authorized to update this work experience"),
        @ApiResponse(responseCode = "404", description = "Work experience not found")
    })
    @PutMapping(value = "/work-experience/{workExperienceId}", consumes = "application/json", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public WorkExperience updateWorkExperience(
            @Parameter(description = "Work experience ID") @PathVariable String workExperienceId,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal,
            @Parameter(description = "Work experience update details") @Valid @RequestBody WorkExperienceRequest request) {

        User user = userService.getUserByCognitoId(userPrincipal.getSubject());
        return workExperienceService.updateWorkExperience(workExperienceId, request, user.getId());
    }
    
    @Operation(summary = "Get all work experiences", description = "Get all work experience records for the authenticated user")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Work experiences retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/work-experience", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<WorkExperience> getWorkExperiences(@Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());
        return workExperienceService.getWorkExperiencesByUserId(user.getId());
    }
    
    @Operation(summary = "Get work experience by ID", description = "Get a specific work experience record by ID")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Work experience retrieved successfully",
                content = @Content(schema = @Schema(implementation = WorkExperience.class))),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Not authorized to view this work experience"),
        @ApiResponse(responseCode = "404", description = "Work experience not found")
    })
    @GetMapping(value = "/work-experience/{workExperienceId}", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public WorkExperience getWorkExperience(
            @Parameter(description = "Work experience ID") @PathVariable String workExperienceId,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {

        User user = userService.getUserByCognitoId(userPrincipal.getSubject());
        return workExperienceService.getWorkExperienceById(workExperienceId, user.getId());
    }
    
    @Operation(summary = "Get current work experiences", description = "Get all current work experience records (currently working)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Current work experiences retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/work-experience/current", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<WorkExperience> getCurrentWorkExperiences(@Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());
        return workExperienceService.getCurrentWorkExperiences(user.getId());
    }
    
    @Operation(summary = "Search work experiences by company", description = "Search work experience records by company name")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Work experiences found successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/work-experience/search/company", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<WorkExperience> searchByCompanyName(
            @Parameter(description = "Company name to search for") @RequestParam String companyName,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {

        User user = userService.getUserByCognitoId(userPrincipal.getSubject());
        return workExperienceService.searchByCompanyName(user.getId(), companyName);
    }
    
    @Operation(summary = "Filter by employment type", description = "Filter work experience records by employment type")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Work experiences filtered successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/work-experience/filter/employment-type", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<WorkExperience> getByEmploymentType(
            @Parameter(description = "Employment type (FULL_TIME, PART_TIME, CONTRACT, FREELANCE, INTERNSHIP)") 
            @RequestParam String employmentType,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {

        User user = userService.getUserByCognitoId(userPrincipal.getSubject());
        return workExperienceService.getByEmploymentType(user.getId(), employmentType);
    }
    

    
    @Operation(summary = "Get work experiences by year range", description = "Get work experience records within a specific year range")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Work experiences retrieved successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid year range"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/work-experience/filter/year-range", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<WorkExperience> getByYearRange(
            @Parameter(description = "Start year") @RequestParam Integer startYear,
            @Parameter(description = "End year") @RequestParam Integer endYear,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {

        User user = userService.getUserByCognitoId(userPrincipal.getSubject());
        return workExperienceService.getByYearRange(user.getId(), startYear, endYear);
    }
    
    @Operation(summary = "Delete work experience", description = "Delete a work experience record")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "Work experience deleted successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Not authorized to delete this work experience"),
        @ApiResponse(responseCode = "404", description = "Work experience not found")
    })
    @DeleteMapping(value = "/work-experience/{workExperienceId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void deleteWorkExperience(
            @Parameter(description = "Work experience ID") @PathVariable String workExperienceId,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {

        User user = userService.getUserByCognitoId(userPrincipal.getSubject());
        workExperienceService.deleteWorkExperience(workExperienceId, user.getId());
    }
    
    @Operation(summary = "Get work experience statistics", description = "Get comprehensive statistics about user's work experience")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Statistics retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/work-experience/statistics", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public WorkExperienceService.WorkExperienceStatistics getWorkExperienceStatistics(
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {

        User user = userService.getUserByCognitoId(userPrincipal.getSubject());
        return workExperienceService.getWorkExperienceStatistics(user.getId());
    }
}
