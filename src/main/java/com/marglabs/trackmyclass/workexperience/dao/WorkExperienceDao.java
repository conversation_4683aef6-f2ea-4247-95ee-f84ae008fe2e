package com.marglabs.trackmyclass.workexperience.dao;

import com.marglabs.common.rest.error.GeneralException;
import com.marglabs.trackmyclass.workexperience.entity.WorkExperienceEntity;
import com.marglabs.trackmyclass.workexperience.repository.WorkExperienceRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

@Component
public class WorkExperienceDao {
    
    @Autowired
    private WorkExperienceRepository repository;
    
    public WorkExperienceEntity save(WorkExperienceEntity entity) {
        return repository.save(entity);
    }
    
    public WorkExperienceEntity getById(String id) {
        return repository.findById(id)
                .orElseThrow(() -> new GeneralException(HttpStatus.NOT_FOUND, "WORK_EXPERIENCE", "WORK_EXPERIENCE_NOT_FOUND",
                        "work_experience_not_found_details", id));
    }
    
    public WorkExperienceEntity getByIdAndUserId(String id, String userId) {
        return repository.findByIdAndUserId(id, userId)
                .orElseThrow(() -> new GeneralException(HttpStatus.NOT_FOUND, "WORK_EXPERIENCE", "WORK_EXPERIENCE_NOT_FOUND",
                        "work_experience_not_found_details", id));
    }
    
    public Optional<WorkExperienceEntity> findById(String id) {
        return repository.findById(id);
    }
    
    public Optional<WorkExperienceEntity> findByIdAndUserId(String id, String userId) {
        return repository.findByIdAndUserId(id, userId);
    }
    
    public List<WorkExperienceEntity> getByUserId(String userId) {
        return repository.findByUserId(userId);
    }
    
    public List<WorkExperienceEntity> getByUserIdOrderByStartDateDesc(String userId) {
        return repository.findByUserIdOrderByStartDateDesc(userId);
    }
    
    public List<WorkExperienceEntity> getCurrentWorkExperiences(String userId) {
        return repository.findByUserIdAndCurrentlyWorkingTrue(userId);
    }
    
    public List<WorkExperienceEntity> getByCompanyName(String userId, String companyName) {
        return repository.findByUserIdAndCompanyNameContainingIgnoreCase(userId, companyName);
    }
    
    public List<WorkExperienceEntity> getByEmploymentType(String userId, String employmentType) {
        return repository.findByUserIdAndEmploymentType(userId, employmentType);
    }

    public List<WorkExperienceEntity> getByYearRange(String userId, Integer startYear, Integer endYear) {
        return repository.findByUserIdAndYearRange(userId, startYear, endYear);
    }
    
    public long countByUserId(String userId) {
        return repository.countByUserId(userId);
    }
    
    public boolean hasCurrentWorkExperience(String userId) {
        return repository.existsByUserIdAndCurrentlyWorkingTrue(userId);
    }
    
    public void deleteById(String id) {
        repository.deleteById(id);
    }
    
    public boolean existsById(String id) {
        return repository.existsById(id);
    }
    
    public boolean existsByIdAndUserId(String id, String userId) {
        return repository.findByIdAndUserId(id, userId).isPresent();
    }
}
