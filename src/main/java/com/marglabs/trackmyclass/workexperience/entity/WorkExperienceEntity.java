package com.marglabs.trackmyclass.workexperience.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.Instant;

@Entity
@Table(name = "work_experiences")
@Data
@Accessors(chain = true)
public class WorkExperienceEntity {
    
    @Id
    @Column(name = "id")
    private String id;
    
    @Column(name = "user_id", nullable = false)
    private String userId;
    
    @Column(name = "job_title", nullable = false, length = 200)
    private String jobTitle;
    
    @Column(name = "company_name", nullable = false, length = 200)
    private String companyName;
    
    @Column(name = "company_location", length = 200)
    private String companyLocation;

    @Column(name = "employment_type", length = 20)
    private String employmentType;
    
    @Column(name = "start_year")
    private Integer startYear;
    
    @Column(name = "start_month")
    private Integer startMonth;
    
    @Column(name = "end_year")
    private Integer endYear;
    
    @Column(name = "end_month")
    private Integer endMonth;
    
    @Column(name = "currently_working")
    private boolean currentlyWorking;
    
    @Column(name = "description", length = 2000)
    private String description;
    
    @CreationTimestamp
    @Column(name = "created_date", nullable = false, updatable = false)
    private Instant createdDate;
    
    @UpdateTimestamp
    @Column(name = "updated_date", nullable = false)
    private Instant updatedDate;
}
