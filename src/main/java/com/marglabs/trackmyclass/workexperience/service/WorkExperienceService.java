package com.marglabs.trackmyclass.workexperience.service;

import com.marglabs.common.rest.error.GeneralException;
import com.marglabs.trackmyclass.workexperience.dao.WorkExperienceDao;
import com.marglabs.trackmyclass.workexperience.entity.WorkExperienceEntity;
import com.marglabs.trackmyclass.workexperience.mapper.WorkExperienceMapper;
import com.marglabs.trackmyclass.workexperience.model.WorkExperience;
import com.marglabs.trackmyclass.workexperience.model.WorkExperienceRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
public class WorkExperienceService {
    
    private static final Logger logger = LoggerFactory.getLogger(WorkExperienceService.class);
    
    @Autowired
    private WorkExperienceDao workExperienceDao;
    
    @Autowired
    private WorkExperienceMapper mapper;
    
    /**
     * Add work experience for a user
     */
    @Transactional
    public WorkExperience addWorkExperience(WorkExperienceRequest request, String userId) {
        logger.info("Adding work experience for user: {}", userId);
        
        // Validate the request
        validateWorkExperienceRequest(request);
        
        // Create entity
        WorkExperienceEntity entity = mapper.toEntity(request);
        entity.setId(UUID.randomUUID().toString());
        entity.setUserId(userId);
        
        // Save entity
        WorkExperienceEntity savedEntity = workExperienceDao.save(entity);
        
        logger.info("Successfully added work experience with ID: {} for user: {}", savedEntity.getId(), userId);
        return mapper.toDto(savedEntity);
    }
    
    /**
     * Update work experience
     */
    @Transactional
    public WorkExperience updateWorkExperience(String workExperienceId, WorkExperienceRequest request, String userId) {
        logger.info("Updating work experience {} for user: {}", workExperienceId, userId);
        
        // Validate the request
        validateWorkExperienceRequest(request);
        
        // Get existing entity and verify ownership
        WorkExperienceEntity entity = workExperienceDao.getByIdAndUserId(workExperienceId, userId);
        
        // Update entity
        mapper.updateEntityFromRequest(request, entity);
        
        // Save updated entity
        WorkExperienceEntity updatedEntity = workExperienceDao.save(entity);
        
        logger.info("Successfully updated work experience with ID: {} for user: {}", workExperienceId, userId);
        return mapper.toDto(updatedEntity);
    }
    
    /**
     * Get all work experiences for a user
     */
    public List<WorkExperience> getWorkExperiencesByUserId(String userId) {
        logger.info("Getting work experiences for user: {}", userId);
        
        List<WorkExperienceEntity> entities = workExperienceDao.getByUserIdOrderByStartDateDesc(userId);
        
        return entities.stream()
                .map(mapper::toDto)
                .collect(Collectors.toList());
    }
    
    /**
     * Get work experience by ID
     */
    public WorkExperience getWorkExperienceById(String workExperienceId, String userId) {
        logger.info("Getting work experience {} for user: {}", workExperienceId, userId);
        
        WorkExperienceEntity entity = workExperienceDao.getByIdAndUserId(workExperienceId, userId);
        return mapper.toDto(entity);
    }
    
    /**
     * Get current work experiences for a user
     */
    public List<WorkExperience> getCurrentWorkExperiences(String userId) {
        logger.info("Getting current work experiences for user: {}", userId);
        
        List<WorkExperienceEntity> entities = workExperienceDao.getCurrentWorkExperiences(userId);
        
        return entities.stream()
                .map(mapper::toDto)
                .collect(Collectors.toList());
    }
    
    /**
     * Search work experiences by company name
     */
    public List<WorkExperience> searchByCompanyName(String userId, String companyName) {
        logger.info("Searching work experiences by company name '{}' for user: {}", companyName, userId);
        
        List<WorkExperienceEntity> entities = workExperienceDao.getByCompanyName(userId, companyName);
        
        return entities.stream()
                .map(mapper::toDto)
                .collect(Collectors.toList());
    }
    
    /**
     * Filter work experiences by employment type
     */
    public List<WorkExperience> getByEmploymentType(String userId, String employmentType) {
        logger.info("Getting work experiences by employment type '{}' for user: {}", employmentType, userId);
        
        List<WorkExperienceEntity> entities = workExperienceDao.getByEmploymentType(userId, employmentType);
        
        return entities.stream()
                .map(mapper::toDto)
                .collect(Collectors.toList());
    }
    

    
    /**
     * Get work experiences within a year range
     */
    public List<WorkExperience> getByYearRange(String userId, Integer startYear, Integer endYear) {
        logger.info("Getting work experiences from {} to {} for user: {}", startYear, endYear, userId);
        
        if (startYear > endYear) {
            throw new GeneralException(HttpStatus.BAD_REQUEST, "WORK_EXPERIENCE", "INVALID_YEAR_RANGE",
                    "start_year_after_end_year", startYear.toString(), endYear.toString());
        }
        
        List<WorkExperienceEntity> entities = workExperienceDao.getByYearRange(userId, startYear, endYear);
        
        return entities.stream()
                .map(mapper::toDto)
                .collect(Collectors.toList());
    }
    
    /**
     * Delete work experience
     */
    @Transactional
    public void deleteWorkExperience(String workExperienceId, String userId) {
        logger.info("Deleting work experience {} for user: {}", workExperienceId, userId);
        
        // Verify ownership before deletion
        WorkExperienceEntity entity = workExperienceDao.getByIdAndUserId(workExperienceId, userId);
        
        workExperienceDao.deleteById(workExperienceId);
        
        logger.info("Successfully deleted work experience with ID: {} for user: {}", workExperienceId, userId);
    }
    
    /**
     * Get work experience statistics for a user
     */
    public WorkExperienceStatistics getWorkExperienceStatistics(String userId) {
        logger.info("Getting work experience statistics for user: {}", userId);
        
        List<WorkExperienceEntity> allExperiences = workExperienceDao.getByUserId(userId);
        
        long totalExperiences = allExperiences.size();
        long currentExperiences = allExperiences.stream()
                .filter(WorkExperienceEntity::isCurrentlyWorking)
                .count();
        
        int totalYearsExperience = calculateTotalYearsExperience(allExperiences);

        List<String> companies = allExperiences.stream()
                .map(WorkExperienceEntity::getCompanyName)
                .distinct()
                .collect(Collectors.toList());

        return new WorkExperienceStatistics()
                .setTotalExperiences(totalExperiences)
                .setCurrentExperiences(currentExperiences)
                .setTotalYearsExperience(totalYearsExperience)
                .setCompanies(companies);
    }
    
    private void validateWorkExperienceRequest(WorkExperienceRequest request) {
        // Validate date logic
        if (!request.isCurrentlyWorking()) {
            if (request.getEndYear() == null) {
                throw new GeneralException(HttpStatus.BAD_REQUEST, "WORK_EXPERIENCE", "WORK_EXPERIENCE_BAD_REQUEST",
                        "end_year_required_when_not_currently_working");
            }
            
            if (request.getStartYear() > request.getEndYear()) {
                throw new GeneralException(HttpStatus.BAD_REQUEST, "WORK_EXPERIENCE", "WORK_EXPERIENCE_BAD_REQUEST",
                        "start_year_after_end_year", request.getStartYear().toString(), request.getEndYear().toString());
            }
            
            if (request.getStartYear().equals(request.getEndYear()) && 
                request.getStartMonth() != null && request.getEndMonth() != null &&
                request.getStartMonth() > request.getEndMonth()) {
                throw new GeneralException(HttpStatus.BAD_REQUEST, "WORK_EXPERIENCE", "WORK_EXPERIENCE_BAD_REQUEST",
                        "start_month_after_end_month");
            }
        }
        
        // Validate start year is not in the future
        int currentYear = LocalDate.now().getYear();
        if (request.getStartYear() > currentYear) {
            throw new GeneralException(HttpStatus.BAD_REQUEST, "WORK_EXPERIENCE", "WORK_EXPERIENCE_BAD_REQUEST",
                    "start_year_cannot_be_future");
        }
        
        // If end year is provided, validate it's not in the future (unless currently working)
        if (!request.isCurrentlyWorking() && request.getEndYear() != null && request.getEndYear() > currentYear) {
            throw new GeneralException(HttpStatus.BAD_REQUEST, "WORK_EXPERIENCE", "WORK_EXPERIENCE_BAD_REQUEST",
                    "end_year_cannot_be_future");
        }
    }
    
    private int calculateTotalYearsExperience(List<WorkExperienceEntity> experiences) {
        int totalMonths = 0;
        int currentYear = LocalDate.now().getYear();
        int currentMonth = LocalDate.now().getMonthValue();
        
        for (WorkExperienceEntity exp : experiences) {
            int startYear = exp.getStartYear();
            int startMonth = exp.getStartMonth() != null ? exp.getStartMonth() : 1;
            
            int endYear = exp.isCurrentlyWorking() ? currentYear : 
                         (exp.getEndYear() != null ? exp.getEndYear() : currentYear);
            int endMonth = exp.isCurrentlyWorking() ? currentMonth :
                          (exp.getEndMonth() != null ? exp.getEndMonth() : 12);
            
            int months = (endYear - startYear) * 12 + (endMonth - startMonth) + 1;
            totalMonths += Math.max(0, months);
        }
        
        return totalMonths / 12;
    }
    
    // Inner class for statistics
    @lombok.Data
    @lombok.experimental.Accessors(chain = true)
    public static class WorkExperienceStatistics {
        private long totalExperiences;
        private long currentExperiences;
        private int totalYearsExperience;
        private List<String> companies;
    }
}
