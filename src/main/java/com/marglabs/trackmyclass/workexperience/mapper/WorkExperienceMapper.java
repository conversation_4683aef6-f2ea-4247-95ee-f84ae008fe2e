package com.marglabs.trackmyclass.workexperience.mapper;

import com.marglabs.trackmyclass.workexperience.entity.WorkExperienceEntity;
import com.marglabs.trackmyclass.workexperience.model.WorkExperience;
import com.marglabs.trackmyclass.workexperience.model.WorkExperienceRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(componentModel = "spring")
public interface WorkExperienceMapper {
    
    @Mapping(target = "createdDate", expression = "java(entity.getCreatedDate() != null ? entity.getCreatedDate().toEpochMilli() : 0)")
    @Mapping(target = "updatedDate", expression = "java(entity.getUpdatedDate() != null ? entity.getUpdatedDate().toEpochMilli() : 0)")
    WorkExperience toDto(WorkExperienceEntity entity);
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "userId", ignore = true)
    @Mapping(target = "createdDate", ignore = true)
    @Mapping(target = "updatedDate", ignore = true)
    WorkExperienceEntity toEntity(WorkExperienceRequest request);
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "userId", ignore = true)
    @Mapping(target = "createdDate", ignore = true)
    @Mapping(target = "updatedDate", ignore = true)
    void updateEntityFromRequest(WorkExperienceRequest request, @MappingTarget WorkExperienceEntity entity);
}
