package com.marglabs.trackmyclass.workexperience.repository;

import com.marglabs.trackmyclass.workexperience.entity.WorkExperienceEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface WorkExperienceRepository extends JpaRepository<WorkExperienceEntity, String> {
    
    // Find work experiences by user ID
    List<WorkExperienceEntity> findByUserId(String userId);
    
    // Find work experiences by user ID ordered by start date (most recent first)
    @Query("SELECT w FROM WorkExperienceEntity w WHERE w.userId = :userId ORDER BY w.startYear DESC, w.startMonth DESC NULLS LAST")
    List<WorkExperienceEntity> findByUserIdOrderByStartDateDesc(@Param("userId") String userId);
    
    // Find current work experiences (currently working)
    List<WorkExperienceEntity> findByUserIdAndCurrentlyWorkingTrue(String userId);
    
    // Find work experiences by company name
    List<WorkExperienceEntity> findByUserIdAndCompanyNameContainingIgnoreCase(String userId, String companyName);
    
    // Find work experiences by employment type
    List<WorkExperienceEntity> findByUserIdAndEmploymentType(String userId, String employmentType);

    // Find work experience by ID and user ID (for authorization)
    Optional<WorkExperienceEntity> findByIdAndUserId(String id, String userId);
    
    // Count work experiences for a user
    long countByUserId(String userId);
    
    // Check if user has any current work experience
    boolean existsByUserIdAndCurrentlyWorkingTrue(String userId);
    
    // Find work experiences within a year range
    @Query("SELECT w FROM WorkExperienceEntity w WHERE w.userId = :userId AND " +
           "((w.startYear >= :startYear AND w.startYear <= :endYear) OR " +
           "(w.endYear >= :startYear AND w.endYear <= :endYear) OR " +
           "(w.startYear <= :startYear AND (w.endYear >= :endYear OR w.currentlyWorking = true)))")
    List<WorkExperienceEntity> findByUserIdAndYearRange(@Param("userId") String userId, 
                                                        @Param("startYear") Integer startYear, 
                                                        @Param("endYear") Integer endYear);
}
