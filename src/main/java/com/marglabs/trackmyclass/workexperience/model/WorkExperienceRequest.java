package com.marglabs.trackmyclass.workexperience.model;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class WorkExperienceRequest {
    
    @NotBlank(message = "Job title is required")
    @Size(max = 200, message = "Job title cannot exceed 200 characters")
    private String jobTitle;
    
    @NotBlank(message = "Company name is required")
    @Size(max = 200, message = "Company name cannot exceed 200 characters")
    private String companyName;
    
    @Size(max = 200, message = "Company location cannot exceed 200 characters")
    private String companyLocation;

    @Pattern(regexp = "^(FULL_TIME|PART_TIME|CONTRACT|FREELANCE|INTERNSHIP)$",
             message = "Employment type must be one of: FULL_TIME, PART_TIME, CONTRACT, FREELANCE, INTERNSHIP")
    private String employmentType;
    
    @Min(value = 1900, message = "Start year must be after 1900")
    @Max(value = 2100, message = "Start year must be before 2100")
    private Integer startYear;
    
    @Min(value = 1, message = "Start month must be between 1 and 12")
    @Max(value = 12, message = "Start month must be between 1 and 12")
    private Integer startMonth;
    
    @Min(value = 1900, message = "End year must be after 1900")
    @Max(value = 2100, message = "End year must be before 2100")
    private Integer endYear;
    
    @Min(value = 1, message = "End month must be between 1 and 12")
    @Max(value = 12, message = "End month must be between 1 and 12")
    private Integer endMonth;
    
    private boolean currentlyWorking;
    
    @Size(max = 2000, message = "Description cannot exceed 2000 characters")
    private String description;
}
