package com.marglabs.trackmyclass.workexperience.model;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class WorkExperience {
    
    private String id;
    private String userId;
    private String jobTitle;
    private String companyName;
    private String companyLocation;
    private String employmentType; // FULL_TIME, PART_TIME, CONTRACT, FREELANCE, INTERNSHIP
    private Integer startYear;
    private Integer startMonth;
    private Integer endYear;
    private Integer endMonth;
    private boolean currentlyWorking;
    private String description;
    private long createdDate;
    private long updatedDate;
}
