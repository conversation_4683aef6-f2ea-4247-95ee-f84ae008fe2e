package com.marglabs.trackmyclass.contact.dao;

import com.marglabs.common.rest.error.EntityNotFoundException;
import com.marglabs.trackmyclass.contact.entity.ContactMessageEntity;
import com.marglabs.trackmyclass.contact.repository.ContactMessageRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

@Component
public class ContactMessageDao {
    @Autowired
    private ContactMessageRepository repository;

    public ContactMessageEntity create(ContactMessageEntity entity) {
        // CreatedDate is set automatically by @CreationTimestamp
        return repository.save(entity);
    }

    public ContactMessageEntity getById(String id) {
        Optional<ContactMessageEntity> entity = repository.findById(id);
        if (entity.isPresent()) {
            return entity.get();
        }
        throw new EntityNotFoundException(HttpStatus.NOT_FOUND, "CONTACT_MESSAGE", "CONTACT_MESSAGE_NOT_FOUND", "contact_message_not_found_details", id);
    }

    public List<ContactMessageEntity> getByStatus(String status) {
        return repository.findAllByStatus(status);
    }

    public Page<ContactMessageEntity> getAllPaginated(Pageable pageable) {
        return repository.findAll(pageable);
    }

    public Page<ContactMessageEntity> getByStatusPaginated(String status, Pageable pageable) {
        return repository.findByStatus(status, pageable);
    }

    public ContactMessageEntity update(ContactMessageEntity entity) {
        // CreatedDate is preserved automatically (updatable = false)
        return repository.save(entity);
    }

    public void deleteById(String id) {
        try {
            repository.deleteById(id);
        } catch (EmptyResultDataAccessException e) {
            throw new EntityNotFoundException(HttpStatus.NOT_FOUND, "CONTACT_MESSAGE", "CONTACT_MESSAGE_NOT_FOUND", "contact_message_not_found_details", id);
        }
    }
}
