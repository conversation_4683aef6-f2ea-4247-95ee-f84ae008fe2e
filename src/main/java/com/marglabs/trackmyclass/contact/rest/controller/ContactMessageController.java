package com.marglabs.trackmyclass.contact.rest.controller;

import com.marglabs.trackmyclass.contact.model.ContactMessage;
import com.marglabs.trackmyclass.contact.model.ContactMessageCreationRequest;
import com.marglabs.trackmyclass.contact.model.ContactMessageStatus;
import com.marglabs.trackmyclass.contact.service.ContactMessageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/contact/v1")
@Tag(name = "Contact Messages", description = "API for managing contact form submissions")
public class ContactMessageController {
    @Autowired
    private ContactMessageService service;

    @Operation(summary = "Submit a contact message", description = "Allows users to submit a contact form message")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Message submitted successfully",
                content = @Content(schema = @Schema(implementation = ContactMessage.class))),
        @ApiResponse(responseCode = "400", description = "Invalid input")
    })
    @PostMapping(value = "/messages", consumes = "application/json", produces = "application/json")
    @ResponseStatus(HttpStatus.CREATED)
    public ContactMessage submitContactMessage(
            @Parameter(description = "Contact message details") @Valid @RequestBody ContactMessageCreationRequest contactMessageRequest,
            HttpServletRequest request) {
        String ipAddress = getClientIpAddress(request);
        return service.createContactMessage(contactMessageRequest, ipAddress);
    }

    @Operation(summary = "Get all contact messages", description = "Returns all contact messages with pagination")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successful operation"),
        @ApiResponse(responseCode = "403", description = "Forbidden - requires admin access")
    })
    @GetMapping(value = "/messages", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    @PreAuthorize("hasRole('ADMIN')")
    public Page<ContactMessage> getAllContactMessages(
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size) {
        return service.getAllContactMessagesPaginated(
                PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdDate")));
    }

    @Operation(summary = "Get contact messages by status", description = "Returns contact messages filtered by status")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successful operation"),
        @ApiResponse(responseCode = "403", description = "Forbidden - requires admin access")
    })
    @GetMapping(value = "/messages/status/{status}", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    @PreAuthorize("hasRole('ADMIN')")
    public Page<ContactMessage> getContactMessagesByStatus(
            @Parameter(description = "Status filter (NEW, READ, RESPONDED)") @PathVariable ContactMessageStatus status,
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size) {
        return service.getContactMessagesByStatusPaginated(status,
                PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdDate")));
    }

    @Operation(summary = "Get contact message by ID", description = "Returns a specific contact message by ID")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successful operation"),
        @ApiResponse(responseCode = "404", description = "Message not found"),
        @ApiResponse(responseCode = "403", description = "Forbidden - requires admin access")
    })
    @GetMapping(value = "/messages/{id}", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    @PreAuthorize("hasRole('ADMIN')")
    public ContactMessage getContactMessage(
            @Parameter(description = "Contact message ID") @PathVariable String id) {
        return service.getContactMessage(id);
    }

    @Operation(summary = "Update contact message status", description = "Updates the status of a contact message")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Status updated successfully"),
        @ApiResponse(responseCode = "404", description = "Message not found"),
        @ApiResponse(responseCode = "403", description = "Forbidden - requires admin access")
    })
    @PatchMapping(value = "/messages/{id}/status/{status}", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    @PreAuthorize("hasRole('ADMIN')")
    public ContactMessage updateContactMessageStatus(
            @Parameter(description = "Contact message ID") @PathVariable String id,
            @Parameter(description = "New status value") @PathVariable ContactMessageStatus status) {
        return service.updateContactMessageStatus(id, status);
    }

    @Operation(summary = "Delete contact message", description = "Deletes a contact message")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "Message deleted successfully"),
        @ApiResponse(responseCode = "404", description = "Message not found"),
        @ApiResponse(responseCode = "403", description = "Forbidden - requires admin access")
    })
    @DeleteMapping(value = "/messages/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @PreAuthorize("hasRole('ADMIN')")
    public void deleteContactMessage(
            @Parameter(description = "Contact message ID") @PathVariable String id) {
        service.deleteContactMessage(id);
    }

    /**
     * Extract client IP address from HTTP request, handling proxies and load balancers
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String ipAddress = request.getHeader("X-Forwarded-For");

        if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("Proxy-Client-IP");
        }

        if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("WL-Proxy-Client-IP");
        }

        if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("HTTP_CLIENT_IP");
        }

        if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("HTTP_X_FORWARDED_FOR");
        }

        if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getRemoteAddr();
        }

        // Handle multiple IPs in X-Forwarded-For header (take the first one)
        if (ipAddress != null && ipAddress.contains(",")) {
            ipAddress = ipAddress.split(",")[0].trim();
        }

        return ipAddress;
    }
}
