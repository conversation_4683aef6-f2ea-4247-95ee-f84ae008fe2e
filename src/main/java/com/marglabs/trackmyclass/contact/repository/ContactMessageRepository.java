package com.marglabs.trackmyclass.contact.repository;

import com.marglabs.trackmyclass.contact.entity.ContactMessageEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.rest.core.annotation.RepositoryRestResource;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@RepositoryRestResource(exported = false)
public interface ContactMessageRepository extends JpaRepository<ContactMessageEntity, String> {
    List<ContactMessageEntity> findAllByStatus(String status);
    Page<ContactMessageEntity> findAll(Pageable pageable);
    Page<ContactMessageEntity> findByStatus(String status, Pageable pageable);
}
