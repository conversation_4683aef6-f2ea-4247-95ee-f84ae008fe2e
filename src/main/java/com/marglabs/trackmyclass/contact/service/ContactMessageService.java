package com.marglabs.trackmyclass.contact.service;

import com.marglabs.common.utils.IdGenerator;
import com.marglabs.trackmyclass.contact.dao.ContactMessageDao;
import com.marglabs.trackmyclass.contact.entity.ContactMessageEntity;
import com.marglabs.trackmyclass.contact.mapper.ContactMessageMapper;
import com.marglabs.trackmyclass.contact.model.ContactMessage;
import com.marglabs.trackmyclass.contact.model.ContactMessageCreationRequest;
import com.marglabs.trackmyclass.contact.model.ContactMessageStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class ContactMessageService {
    @Autowired
    private ContactMessageDao contactMessageDao;

    @Autowired
    private ContactMessageMapper mapper;

    @Autowired
    private IdGenerator idGenerator;

    public ContactMessage createContactMessage(ContactMessageCreationRequest request, String ipAddress) {
        // Create ContactMessage from request
        ContactMessage contactMessage = new ContactMessage()
                .setId(idGenerator.generateId())
                .setName(request.getName())
                .setEmail(request.getEmail())
                .setMessage(request.getMessage())
                .setStatus(ContactMessageStatus.NEW.name())
                .setIpAddress(ipAddress);

        ContactMessageEntity entity = mapper.toEntity(contactMessage);
        entity = contactMessageDao.create(entity);
        return mapper.toDto(entity);
    }

    public ContactMessage getContactMessage(String id) {
        return mapper.toDto(contactMessageDao.getById(id));
    }

    public List<ContactMessage> getContactMessagesByStatus(ContactMessageStatus status) {
        return contactMessageDao.getByStatus(status.name()).stream()
                .map(mapper::toDto)
                .collect(Collectors.toList());
    }

    public Page<ContactMessage> getAllContactMessagesPaginated(Pageable pageable) {
        return contactMessageDao.getAllPaginated(pageable)
                .map(mapper::toDto);
    }

    public Page<ContactMessage> getContactMessagesByStatusPaginated(ContactMessageStatus status, Pageable pageable) {
        return contactMessageDao.getByStatusPaginated(status.name(), pageable)
                .map(mapper::toDto);
    }

    public ContactMessage updateContactMessageStatus(String id, ContactMessageStatus status) {
        ContactMessageEntity entity = contactMessageDao.getById(id);
        entity.setStatus(status.name());
        entity = contactMessageDao.update(entity);
        return mapper.toDto(entity);
    }

    public void deleteContactMessage(String id) {
        contactMessageDao.deleteById(id);
    }
}
