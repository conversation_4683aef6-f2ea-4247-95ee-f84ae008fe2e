package com.marglabs.trackmyclass.contact.mapper;

import com.marglabs.trackmyclass.contact.entity.ContactMessageEntity;
import com.marglabs.trackmyclass.contact.model.ContactMessage;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface ContactMessageMapper {

    ContactMessage toDto(ContactMessageEntity entity);

    @Mapping(target = "createdDate", ignore = true)
    ContactMessageEntity toEntity(ContactMessage contactMessage);
}
