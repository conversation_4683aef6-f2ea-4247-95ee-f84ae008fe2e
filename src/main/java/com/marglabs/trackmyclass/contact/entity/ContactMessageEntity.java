package com.marglabs.trackmyclass.contact.entity;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;

import java.time.Instant;

@Entity
@Table(name = "contact_messages")
@Data
public class ContactMessageEntity {
    @Id
    private String id;

    @Column(nullable = false)
    private String name;

    @Column(nullable = false)
    private String email;

    @Column(length = 2000, nullable = false)
    private String message;

    @CreationTimestamp
    @Column(name = "createddate", nullable = false, updatable = false)
    private Instant createdDate;

    @Column(name = "status")
    private String status; // NEW, READ, RESPONDED

    @Column(name = "ipaddress")
    private String ipAddress;
}
