package com.marglabs.trackmyclass.profile.service;

import com.marglabs.common.rest.error.GeneralException;
import com.marglabs.trackmyclass.profile.dao.*;
import com.marglabs.trackmyclass.profile.entity.*;
import com.marglabs.trackmyclass.profile.mapper.*;
import com.marglabs.trackmyclass.profile.model.*;
import com.marglabs.common.utils.IdGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class ProfileService {
    private static final Logger logger = LoggerFactory.getLogger(ProfileService.class);

    @Autowired
    private ProfileDao profileDao;

    @Autowired
    private EducationDao educationDao;

    @Autowired
    private SkillDao skillDao;

    @Autowired
    private CertificationDao certificationDao;

    @Autowired
    private ProfileMapper profileMapper;

    @Autowired
    private EducationMapper educationMapper;

    @Autowired
    private SkillMapper skillMapper;

    @Autowired
    private CertificationMapper certificationMapper;

    @Autowired
    private IdGenerator idGenerator;

    // Profile Management

    @Transactional
    public Profile createOrUpdateProfile(ProfileRequest request, String userId) {
        logger.info("Creating/updating profile for user {}", userId);

        Optional<ProfileEntity> existingProfile = profileDao.findByUserId(userId);

        ProfileEntity profileEntity;
        if (existingProfile.isPresent()) {
            // Update existing profile
            profileEntity = existingProfile.get();
            if (request.getAboutMe() != null) {
                profileEntity.setAboutMe(request.getAboutMe());
            }
            if (request.getProfilePicture() != null) {
                profileEntity.setProfilePicture(request.getProfilePicture());
            }
            if (request.getGender() != null) {
                profileEntity.setGender(request.getGender());
            }
        } else {
            // Create new profile
            profileEntity = new ProfileEntity()
                    .setId(idGenerator.generateId())
                    .setUserId(userId)
                    .setAboutMe(request.getAboutMe())
                    .setGender(request.getGender())
                    .setProfilePicture(request.getProfilePicture());
        }

        ProfileEntity savedEntity = profileDao.create(profileEntity);
        logger.info("Successfully created/updated profile {} for user {}", savedEntity.getId(), userId);

        return enrichProfileWithDetails(profileMapper.toDto(savedEntity));
    }

    public Profile getProfileByUserId(String userId) {
        Optional<ProfileEntity> profileEntity = profileDao.findByUserId(userId);

        if (profileEntity.isPresent()) {
            return enrichProfileWithDetails(profileMapper.toDto(profileEntity.get()));
        } else {
            throw new GeneralException(HttpStatus.NOT_FOUND, "PROFILE", "PROFILE_NOT_FOUND",
                    "profile_not_found_for_user", userId);
        }
    }

    // Education Management

    @Transactional
    public Education addEducation(EducationRequest request, String userId) {
        logger.info("Adding education for user {}", userId);

        EducationEntity educationEntity = new EducationEntity()
                .setId(idGenerator.generateId())
                .setUserId(userId)
                .setTitle(request.getTitle())
                .setInstitution(request.getInstitution())
                .setSubject(request.getSubject())
                .setStartYear(request.getStartYear())
                .setEndYear(request.getEndYear())
                .setDescription(request.getDescription());

        EducationEntity savedEntity = educationDao.create(educationEntity);
        logger.info("Successfully added education {} for user {}", savedEntity.getId(), userId);

        return educationMapper.toDto(savedEntity);
    }

    @Transactional
    public Education updateEducation(String educationId, EducationRequest request, String userId) {
        logger.info("Updating education {} for user {}", educationId, userId);

        EducationEntity existingEducation = educationDao.getById(educationId);

        // Verify the education belongs to the user
        if (!existingEducation.getUserId().equals(userId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "EDUCATION", "UNAUTHORIZED_EDUCATION_ACCESS",
                    "user_not_authorized_for_education", educationId);
        }

        // Update fields
        updateEducationFields(existingEducation, request);

        EducationEntity updatedEntity = educationDao.update(existingEducation);
        logger.info("Successfully updated education {}", educationId);

        return educationMapper.toDto(updatedEntity);
    }

    public List<Education> getEducationsByUserId(String userId) {
        List<EducationEntity> educations = educationDao.getByUserIdOrderByEndYear(userId);
        return educations.stream()
                .map(educationMapper::toDto)
                .collect(Collectors.toList());
    }

    @Transactional
    public void deleteEducation(String educationId, String userId) {
        logger.info("Deleting education {} for user {}", educationId, userId);

        EducationEntity education = educationDao.getById(educationId);

        // Verify the education belongs to the user
        if (!education.getUserId().equals(userId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "EDUCATION", "UNAUTHORIZED_EDUCATION_ACCESS",
                    "user_not_authorized_for_education", educationId);
        }

        educationDao.deleteById(educationId);
        logger.info("Successfully deleted education {}", educationId);
    }

    // Skill Management

    @Transactional
    public Skill addSkill(SkillRequest request, String userId) {
        logger.info("Adding skill for user {}", userId);

        // Check if skill already exists for user
        if (skillDao.existsByUserIdAndSkillName(userId, request.getSkillName())) {
            throw new GeneralException(HttpStatus.CONFLICT, "SKILL", "SKILL_ALREADY_EXISTS",
                    "skill_already_exists_for_user", request.getSkillName());
        }

        SkillEntity skillEntity = new SkillEntity()
                .setId(idGenerator.generateId())
                .setUserId(userId)
                .setSkillName(request.getSkillName())
                .setProficiencyLevel(request.getProficiencyLevel())
                .setCategory(request.getCategory())
                .setYearsOfExperience(request.getYearsOfExperience())
                .setDescription(request.getDescription());

        SkillEntity savedEntity = skillDao.create(skillEntity);
        logger.info("Successfully added skill {} for user {}", savedEntity.getId(), userId);

        return skillMapper.toDto(savedEntity);
    }

    @Transactional
    public Skill updateSkill(String skillId, SkillRequest request, String userId) {
        logger.info("Updating skill {} for user {}", skillId, userId);

        SkillEntity existingSkill = skillDao.getById(skillId);

        // Verify the skill belongs to the user
        if (!existingSkill.getUserId().equals(userId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "SKILL", "UNAUTHORIZED_SKILL_ACCESS",
                    "user_not_authorized_for_skill", skillId);
        }

        // Update fields
        updateSkillFields(existingSkill, request);

        SkillEntity updatedEntity = skillDao.update(existingSkill);
        logger.info("Successfully updated skill {}", skillId);

        return skillMapper.toDto(updatedEntity);
    }

    public List<Skill> getSkillsByUserId(String userId) {
        List<SkillEntity> skills = skillDao.getByUserIdOrderByProficiency(userId);
        return skills.stream()
                .map(skillMapper::toDto)
                .collect(Collectors.toList());
    }

    @Transactional
    public void deleteSkill(String skillId, String userId) {
        logger.info("Deleting skill {} for user {}", skillId, userId);

        SkillEntity skill = skillDao.getById(skillId);

        // Verify the skill belongs to the user
        if (!skill.getUserId().equals(userId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "SKILL", "UNAUTHORIZED_SKILL_ACCESS",
                    "user_not_authorized_for_skill", skillId);
        }

        skillDao.deleteById(skillId);
        logger.info("Successfully deleted skill {}", skillId);
    }

    // Certification Management

    @Transactional
    public Certification addCertification(CertificationRequest request, String userId) {
        logger.info("Adding certification for user {}", userId);

        // Check if certification already exists for user
        if (certificationDao.existsByUserIdAndCertificationName(userId, request.getCertificationName())) {
            throw new GeneralException(HttpStatus.CONFLICT, "CERTIFICATION", "CERTIFICATION_ALREADY_EXISTS",
                    "certification_already_exists_for_user", request.getCertificationName());
        }

        CertificationEntity certificationEntity = new CertificationEntity()
                .setId(idGenerator.generateId())
                .setUserId(userId)
                .setCertificationName(request.getCertificationName())
                .setCertificationLevel(request.getCertificationLevel())
                .setIssuingOrganization(request.getIssuingOrganization())
                .setIssueYear(request.getIssueYear())
                .setExpiryYear(request.getExpiryYear());

        CertificationEntity savedEntity = certificationDao.create(certificationEntity);
        logger.info("Successfully added certification {} for user {}", savedEntity.getId(), userId);

        return certificationMapper.toDto(savedEntity);
    }

    @Transactional
    public Certification updateCertification(String certificationId, CertificationRequest request, String userId) {
        logger.info("Updating certification {} for user {}", certificationId, userId);

        CertificationEntity existingCertification = certificationDao.getById(certificationId);

        // Verify the certification belongs to the user
        if (!existingCertification.getUserId().equals(userId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "CERTIFICATION", "UNAUTHORIZED_CERTIFICATION_ACCESS",
                    "user_not_authorized_for_certification", certificationId);
        }

        // Update fields
        updateCertificationFields(existingCertification, request);

        CertificationEntity updatedEntity = certificationDao.update(existingCertification);
        logger.info("Successfully updated certification {}", certificationId);

        return certificationMapper.toDto(updatedEntity);
    }

    public List<Certification> getCertificationsByUserId(String userId) {
        List<CertificationEntity> certifications = certificationDao.getByUserIdOrderByIssueYear(userId);
        return certifications.stream()
                .map(certificationMapper::toDto)
                .collect(Collectors.toList());
    }

    @Transactional
    public void deleteCertification(String certificationId, String userId) {
        logger.info("Deleting certification {} for user {}", certificationId, userId);

        CertificationEntity certification = certificationDao.getById(certificationId);

        // Verify the certification belongs to the user
        if (!certification.getUserId().equals(userId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "CERTIFICATION", "UNAUTHORIZED_CERTIFICATION_ACCESS",
                    "user_not_authorized_for_certification", certificationId);
        }

        certificationDao.deleteById(certificationId);
        logger.info("Successfully deleted certification {}", certificationId);
    }

    // Helper methods

    private Profile enrichProfileWithDetails(Profile profile) {
        String userId = profile.getUserId();

        // Get educations
        List<Education> educations = getEducationsByUserId(userId);
        profile.setEducations(educations);

        // Get skills
        List<Skill> skills = getSkillsByUserId(userId);
        profile.setSkills(skills);

        // Get certifications
        List<Certification> certifications = getCertificationsByUserId(userId);
        profile.setCertifications(certifications);

        return profile;
    }

    private void updateEducationFields(EducationEntity education, EducationRequest request) {
        if (request.getTitle() != null) {
            education.setTitle(request.getTitle());
        }
        if (request.getInstitution() != null) {
            education.setInstitution(request.getInstitution());
        }
        if (request.getSubject() != null) {
            education.setSubject(request.getSubject());
        }
        if (request.getStartYear() != null) {
            education.setStartYear(request.getStartYear());
        }
        if (request.getEndYear() != null) {
            education.setEndYear(request.getEndYear());
        }
        if (request.getDescription() != null) {
            education.setDescription(request.getDescription());
        }
    }

    private void updateSkillFields(SkillEntity skill, SkillRequest request) {
        if (request.getSkillName() != null) {
            skill.setSkillName(request.getSkillName());
        }
        if (request.getProficiencyLevel() != null) {
            skill.setProficiencyLevel(request.getProficiencyLevel());
        }
        if (request.getCategory() != null) {
            skill.setCategory(request.getCategory());
        }
        if (request.getYearsOfExperience() != null) {
            skill.setYearsOfExperience(request.getYearsOfExperience());
        }
        if (request.getDescription() != null) {
            skill.setDescription(request.getDescription());
        }
    }

    private void updateCertificationFields(CertificationEntity certification, CertificationRequest request) {
        if (request.getCertificationName() != null) {
            certification.setCertificationName(request.getCertificationName());
        }
        if (request.getCertificationLevel() != null) {
            certification.setCertificationLevel(request.getCertificationLevel());
        }
        if (request.getIssuingOrganization() != null) {
            certification.setIssuingOrganization(request.getIssuingOrganization());
        }
        if (request.getIssueYear() != null) {
            certification.setIssueYear(request.getIssueYear());
        }
        if (request.getExpiryYear() != null) {
            certification.setExpiryYear(request.getExpiryYear());
        }
    }
}
