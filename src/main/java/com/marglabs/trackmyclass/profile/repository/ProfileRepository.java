package com.marglabs.trackmyclass.profile.repository;

import com.marglabs.trackmyclass.profile.entity.ProfileEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.rest.core.annotation.RepositoryRestResource;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
@RepositoryRestResource(exported = false)
public interface ProfileRepository extends JpaRepository<ProfileEntity, String> {

    // Find profile by user ID
    Optional<ProfileEntity> findByUserId(String userId);

    // Check if profile exists for user
    boolean existsByUserId(String userId);
}
