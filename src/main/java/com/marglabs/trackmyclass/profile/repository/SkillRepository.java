package com.marglabs.trackmyclass.profile.repository;

import com.marglabs.trackmyclass.profile.entity.SkillEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.data.rest.core.annotation.RepositoryRestResource;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@RepositoryRestResource(exported = false)
public interface SkillRepository extends JpaRepository<SkillEntity, String> {
    
    // Find skills by user ID
    List<SkillEntity> findByUserId(String userId);
    
    // Find skills by user ID and category
    List<SkillEntity> findByUserIdAndCategory(String userId, String category);
    
    // Find skills by user ID ordered by proficiency level
    @Query("SELECT s FROM SkillEntity s WHERE s.userId = :userId ORDER BY " +
           "CASE s.proficiencyLevel " +
           "WHEN 'EXPERT' THEN 1 " +
           "WHEN 'ADVANCED' THEN 2 " +
           "WHEN 'INTERMEDIATE' THEN 3 " +
           "WHEN 'BEGINNER' THEN 4 " +
           "ELSE 5 END")
    List<SkillEntity> findByUserIdOrderByProficiencyLevel(@Param("userId") String userId);
    
    // Count skills for a user
    long countByUserId(String userId);
    
    // Check if skill exists for user
    boolean existsByUserIdAndSkillName(String userId, String skillName);
}
