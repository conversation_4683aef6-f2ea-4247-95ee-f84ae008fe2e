package com.marglabs.trackmyclass.profile.repository;

import com.marglabs.trackmyclass.profile.entity.EducationEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.data.rest.core.annotation.RepositoryRestResource;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@RepositoryRestResource(exported = false)
public interface EducationRepository extends JpaRepository<EducationEntity, String> {

    // Find educations by user ID
    List<EducationEntity> findByUserId(String userId);

    // Find educations by user ID ordered by end year descending
    @Query("SELECT e FROM EducationEntity e WHERE e.userId = :userId ORDER BY e.endYear DESC NULLS LAST, e.startYear DESC")
    List<EducationEntity> findByUserIdOrderByEndYearDesc(@Param("userId") String userId);

    // Find current educations (where endYear is null, indicating currently studying)
    @Query("SELECT e FROM EducationEntity e WHERE e.userId = :userId AND e.endYear IS NULL")
    List<EducationEntity> findCurrentEducations(@Param("userId") String userId);

    // Count educations for a user
    long countByUserId(String userId);
}
