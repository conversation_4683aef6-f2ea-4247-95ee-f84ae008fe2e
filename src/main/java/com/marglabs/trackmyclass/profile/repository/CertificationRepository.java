package com.marglabs.trackmyclass.profile.repository;

import com.marglabs.trackmyclass.profile.entity.CertificationEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.data.rest.core.annotation.RepositoryRestResource;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@RepositoryRestResource(exported = false)
public interface CertificationRepository extends JpaRepository<CertificationEntity, String> {
    
    // Find certifications by user ID
    List<CertificationEntity> findByUserId(String userId);
    
    // Find certifications by user ID ordered by issue year descending
    @Query("SELECT c FROM CertificationEntity c WHERE c.userId = :userId ORDER BY c.issueYear DESC NULLS LAST")
    List<CertificationEntity> findByUserIdOrderByIssueYearDesc(@Param("userId") String userId);
    
    // Find certifications by issuing organization
    List<CertificationEntity> findByIssuingOrganization(String issuingOrganization);
    
    // Count certifications for a user
    long countByUserId(String userId);
    
    // Check if certification exists for user
    boolean existsByUserIdAndCertificationName(String userId, String certificationName);
    
    // Find active certifications (not expired)
    @Query("SELECT c FROM CertificationEntity c WHERE c.userId = :userId AND (c.expiryYear IS NULL OR c.expiryYear >= :currentYear)")
    List<CertificationEntity> findActiveCertifications(@Param("userId") String userId, @Param("currentYear") Integer currentYear);
}
