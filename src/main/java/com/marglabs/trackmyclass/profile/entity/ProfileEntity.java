package com.marglabs.trackmyclass.profile.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.Instant;

@Entity
@Table(name = "profiles")
@Data
@Accessors(chain = true)
public class ProfileEntity {
    @Id
    private String id;

    @Column(name = "user_id", nullable = false, unique = true)
    private String userId;

    @Column(name = "about_me", length = 2000)
    private String aboutMe;

    @Column(name = "gender", length = 20)
    private String gender; // MALE, FEMALE, OTHER, PREFER_NOT_TO_SAY

    @Column(name = "profile_picture", length = 500)
    private String profilePicture;

    @CreationTimestamp
    @Column(name = "created_date", nullable = false, updatable = false)
    private Instant createdDate;

    @UpdateTimestamp
    @Column(name = "updated_date", nullable = false)
    private Instant updatedDate;
}
