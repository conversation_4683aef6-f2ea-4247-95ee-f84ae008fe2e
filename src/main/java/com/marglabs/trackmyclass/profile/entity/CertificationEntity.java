package com.marglabs.trackmyclass.profile.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.Instant;

@Entity
@Table(name = "certifications")
@Data
@Accessors(chain = true)
public class CertificationEntity {
    @Id
    private String id;

    @Column(name = "user_id", nullable = false)
    private String userId;

    @Column(name = "certification_name", nullable = false, length = 200)
    private String certificationName;

    @Column(name = "certification_level", length = 50)
    private String certificationLevel; // A1, A2, B1, B2, C1, C2 (CEFR levels) or other levels

    @Column(name = "issuing_organization", length = 200)
    private String issuingOrganization;

    @Column(name = "issue_date")
    private Integer issueYear;

    @Column(name = "expiry_date")
    private Integer expiryYear;

    @CreationTimestamp
    @Column(name = "created_date", nullable = false, updatable = false)
    private Instant createdDate;

    @UpdateTimestamp
    @Column(name = "updated_date", nullable = false)
    private Instant updatedDate;
}
