package com.marglabs.trackmyclass.profile.rest.controller;

import com.marglabs.trackmyclass.profile.model.*;
import com.marglabs.trackmyclass.profile.service.ProfileService;
import com.marglabs.trackmyclass.user.model.User;
import com.marglabs.trackmyclass.user.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/profileManagement/v1")
@Tag(name = "Profile Management", description = "APIs for managing user profiles, education, skills, and certifications")
public class ProfileManagementController {

    @Autowired
    private ProfileService profileService;

    @Autowired
    private UserService userService;

    // Profile Management

    @Operation(summary = "Create or update profile", description = "Create or update profile for the authenticated user")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Profile created/updated successfully",
                content = @Content(schema = @Schema(implementation = Profile.class))),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @PostMapping(value = "/profile", consumes = "application/json", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public Profile createOrUpdateProfile(
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal,
            @Parameter(description = "Profile details") @Valid @RequestBody ProfileRequest request) {

        User user = userService.getUserByCognitoId(userPrincipal.getSubject());
        return profileService.createOrUpdateProfile(request, user.getId());
    }

    @Operation(summary = "Get profile", description = "Get complete profile for the authenticated user")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Profile retrieved successfully",
                content = @Content(schema = @Schema(implementation = Profile.class))),
        @ApiResponse(responseCode = "404", description = "Profile not found"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/profile", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public Profile getProfile(@Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());
        return profileService.getProfileByUserId(user.getId());
    }

    // Education Management

    @Operation(summary = "Add education", description = "Add education details for the authenticated user")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Education added successfully",
                content = @Content(schema = @Schema(implementation = Education.class))),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @PostMapping(value = "/education", consumes = "application/json", produces = "application/json")
    @ResponseStatus(HttpStatus.CREATED)
    public Education addEducation(
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal,
            @Parameter(description = "Education details") @Valid @RequestBody EducationRequest request) {

        User user = userService.getUserByCognitoId(userPrincipal.getSubject());
        return profileService.addEducation(request, user.getId());
    }

    @Operation(summary = "Update education", description = "Update education details")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Education updated successfully",
                content = @Content(schema = @Schema(implementation = Education.class))),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Not authorized to update this education"),
        @ApiResponse(responseCode = "404", description = "Education not found")
    })
    @PutMapping(value = "/education/{educationId}", consumes = "application/json", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public Education updateEducation(
            @Parameter(description = "Education ID") @PathVariable String educationId,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal,
            @Parameter(description = "Education update details") @Valid @RequestBody EducationRequest request) {

        User user = userService.getUserByCognitoId(userPrincipal.getSubject());
        return profileService.updateEducation(educationId, request, user.getId());
    }

    @Operation(summary = "Get educations", description = "Get all education details for the authenticated user")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Educations retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/education", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<Education> getEducations(@Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());
        return profileService.getEducationsByUserId(user.getId());
    }

    @Operation(summary = "Delete education", description = "Delete education details")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "Education deleted successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Not authorized to delete this education"),
        @ApiResponse(responseCode = "404", description = "Education not found")
    })
    @DeleteMapping(value = "/education/{educationId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void deleteEducation(
            @Parameter(description = "Education ID") @PathVariable String educationId,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {

        User user = userService.getUserByCognitoId(userPrincipal.getSubject());
        profileService.deleteEducation(educationId, user.getId());
    }

    // Skill Management

    @Operation(summary = "Add skill", description = "Add skill for the authenticated user")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Skill added successfully",
                content = @Content(schema = @Schema(implementation = Skill.class))),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "409", description = "Skill already exists"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @PostMapping(value = "/skill", consumes = "application/json", produces = "application/json")
    @ResponseStatus(HttpStatus.CREATED)
    public Skill addSkill(
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal,
            @Parameter(description = "Skill details") @Valid @RequestBody SkillRequest request) {

        User user = userService.getUserByCognitoId(userPrincipal.getSubject());
        return profileService.addSkill(request, user.getId());
    }

    @Operation(summary = "Update skill", description = "Update skill details")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Skill updated successfully",
                content = @Content(schema = @Schema(implementation = Skill.class))),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Not authorized to update this skill"),
        @ApiResponse(responseCode = "404", description = "Skill not found")
    })
    @PutMapping(value = "/skill/{skillId}", consumes = "application/json", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public Skill updateSkill(
            @Parameter(description = "Skill ID") @PathVariable String skillId,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal,
            @Parameter(description = "Skill update details") @Valid @RequestBody SkillRequest request) {

        User user = userService.getUserByCognitoId(userPrincipal.getSubject());
        return profileService.updateSkill(skillId, request, user.getId());
    }

    @Operation(summary = "Get skills", description = "Get all skills for the authenticated user")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Skills retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/skill", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<Skill> getSkills(@Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());
        return profileService.getSkillsByUserId(user.getId());
    }

    @Operation(summary = "Delete skill", description = "Delete skill")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "Skill deleted successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Not authorized to delete this skill"),
        @ApiResponse(responseCode = "404", description = "Skill not found")
    })
    @DeleteMapping(value = "/skill/{skillId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void deleteSkill(
            @Parameter(description = "Skill ID") @PathVariable String skillId,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {

        User user = userService.getUserByCognitoId(userPrincipal.getSubject());
        profileService.deleteSkill(skillId, user.getId());
    }

    // Certification Management

    @Operation(summary = "Add certification", description = "Add certification for the authenticated user")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Certification added successfully",
                content = @Content(schema = @Schema(implementation = Certification.class))),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "409", description = "Certification already exists"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @PostMapping(value = "/certification", consumes = "application/json", produces = "application/json")
    @ResponseStatus(HttpStatus.CREATED)
    public Certification addCertification(
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal,
            @Parameter(description = "Certification details") @Valid @RequestBody CertificationRequest request) {

        User user = userService.getUserByCognitoId(userPrincipal.getSubject());
        return profileService.addCertification(request, user.getId());
    }

    @Operation(summary = "Update certification", description = "Update certification details")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Certification updated successfully",
                content = @Content(schema = @Schema(implementation = Certification.class))),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Not authorized to update this certification"),
        @ApiResponse(responseCode = "404", description = "Certification not found")
    })
    @PutMapping(value = "/certification/{certificationId}", consumes = "application/json", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public Certification updateCertification(
            @Parameter(description = "Certification ID") @PathVariable String certificationId,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal,
            @Parameter(description = "Certification update details") @Valid @RequestBody CertificationRequest request) {

        User user = userService.getUserByCognitoId(userPrincipal.getSubject());
        return profileService.updateCertification(certificationId, request, user.getId());
    }

    @Operation(summary = "Get certifications", description = "Get all certifications for the authenticated user")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Certifications retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/certification", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<Certification> getCertifications(@Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());
        return profileService.getCertificationsByUserId(user.getId());
    }

    @Operation(summary = "Delete certification", description = "Delete certification")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "Certification deleted successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Not authorized to delete this certification"),
        @ApiResponse(responseCode = "404", description = "Certification not found")
    })
    @DeleteMapping(value = "/certification/{certificationId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void deleteCertification(
            @Parameter(description = "Certification ID") @PathVariable String certificationId,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {

        User user = userService.getUserByCognitoId(userPrincipal.getSubject());
        profileService.deleteCertification(certificationId, user.getId());
    }
}
