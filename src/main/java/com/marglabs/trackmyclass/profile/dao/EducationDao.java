package com.marglabs.trackmyclass.profile.dao;

import com.marglabs.common.rest.error.EntityNotFoundException;
import com.marglabs.trackmyclass.profile.entity.EducationEntity;
import com.marglabs.trackmyclass.profile.repository.EducationRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

@Component
public class EducationDao {

    @Autowired
    private EducationRepository repository;

    public EducationEntity create(EducationEntity entity) {
        return repository.save(entity);
    }

    public EducationEntity update(EducationEntity entity) {
        return repository.save(entity);
    }

    public EducationEntity getById(String id) {
        Optional<EducationEntity> entity = repository.findById(id);
        if (entity.isPresent()) {
            return entity.get();
        }
        throw new EntityNotFoundException(HttpStatus.NOT_FOUND, "EDUCATION", "EDUCATION_NOT_FOUND",
                "education_not_found_details", id);
    }

    public List<EducationEntity> getByUserId(String userId) {
        return repository.findByUserId(userId);
    }

    public List<EducationEntity> getByUserIdOrderByEndYear(String userId) {
        return repository.findByUserIdOrderByEndYearDesc(userId);
    }

    public List<EducationEntity> getCurrentEducations(String userId) {
        return repository.findCurrentEducations(userId);
    }

    public long countByUserId(String userId) {
        return repository.countByUserId(userId);
    }

    public void deleteById(String id) {
        repository.deleteById(id);
    }

    public List<EducationEntity> getAll() {
        return repository.findAll();
    }
}
