package com.marglabs.trackmyclass.profile.dao;

import com.marglabs.common.rest.error.EntityNotFoundException;
import com.marglabs.trackmyclass.profile.entity.CertificationEntity;
import com.marglabs.trackmyclass.profile.repository.CertificationRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Component
public class CertificationDao {

    @Autowired
    private CertificationRepository repository;

    public CertificationEntity create(CertificationEntity entity) {
        return repository.save(entity);
    }

    public CertificationEntity update(CertificationEntity entity) {
        return repository.save(entity);
    }

    public CertificationEntity getById(String id) {
        Optional<CertificationEntity> entity = repository.findById(id);
        if (entity.isPresent()) {
            return entity.get();
        }
        throw new EntityNotFoundException(HttpStatus.NOT_FOUND, "CERTIFICATION", "CERTIFICATION_NOT_FOUND",
                "certification_not_found_details", id);
    }

    public List<CertificationEntity> getByUserId(String userId) {
        return repository.findByUserId(userId);
    }

    public List<CertificationEntity> getByUserIdOrderByIssueYear(String userId) {
        return repository.findByUserIdOrderByIssueYearDesc(userId);
    }

    public List<CertificationEntity> getActiveCertifications(String userId) {
        int currentYear = LocalDate.now().getYear();
        return repository.findActiveCertifications(userId, currentYear);
    }

    public List<CertificationEntity> getByIssuingOrganization(String issuingOrganization) {
        return repository.findByIssuingOrganization(issuingOrganization);
    }

    public long countByUserId(String userId) {
        return repository.countByUserId(userId);
    }

    public boolean existsByUserIdAndCertificationName(String userId, String certificationName) {
        return repository.existsByUserIdAndCertificationName(userId, certificationName);
    }

    public void deleteById(String id) {
        repository.deleteById(id);
    }

    public List<CertificationEntity> getAll() {
        return repository.findAll();
    }
}
