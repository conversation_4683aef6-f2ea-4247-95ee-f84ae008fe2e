package com.marglabs.trackmyclass.profile.dao;

import com.marglabs.common.rest.error.EntityNotFoundException;
import com.marglabs.trackmyclass.profile.entity.ProfileEntity;
import com.marglabs.trackmyclass.profile.repository.ProfileRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

@Component
public class ProfileDao {

    @Autowired
    private ProfileRepository repository;

    public ProfileEntity create(ProfileEntity entity) {
        return repository.save(entity);
    }

    public ProfileEntity update(ProfileEntity entity) {
        return repository.save(entity);
    }

    public ProfileEntity getById(String id) {
        Optional<ProfileEntity> entity = repository.findById(id);
        if (entity.isPresent()) {
            return entity.get();
        }
        throw new EntityNotFoundException(HttpStatus.NOT_FOUND, "PROFILE", "PROFILE_NOT_FOUND",
                "profile_not_found_details", id);
    }

    public Optional<ProfileEntity> findByUserId(String userId) {
        return repository.findByUserId(userId);
    }

    public boolean existsByUserId(String userId) {
        return repository.existsByUserId(userId);
    }

    public void deleteById(String id) {
        repository.deleteById(id);
    }

    public List<ProfileEntity> getAll() {
        return repository.findAll();
    }
}
