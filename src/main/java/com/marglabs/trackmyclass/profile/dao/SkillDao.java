package com.marglabs.trackmyclass.profile.dao;

import com.marglabs.common.rest.error.EntityNotFoundException;
import com.marglabs.trackmyclass.profile.entity.SkillEntity;
import com.marglabs.trackmyclass.profile.repository.SkillRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

@Component
public class SkillDao {

    @Autowired
    private SkillRepository repository;

    public SkillEntity create(SkillEntity entity) {
        return repository.save(entity);
    }

    public SkillEntity update(SkillEntity entity) {
        return repository.save(entity);
    }

    public SkillEntity getById(String id) {
        Optional<SkillEntity> entity = repository.findById(id);
        if (entity.isPresent()) {
            return entity.get();
        }
        throw new EntityNotFoundException(HttpStatus.NOT_FOUND, "SKILL", "SKILL_NOT_FOUND",
                "skill_not_found_details", id);
    }

    public List<SkillEntity> getByUserId(String userId) {
        return repository.findByUserId(userId);
    }

    public List<SkillEntity> getByUserIdAndCategory(String userId, String category) {
        return repository.findByUserIdAndCategory(userId, category);
    }

    public List<SkillEntity> getByUserIdOrderByProficiency(String userId) {
        return repository.findByUserIdOrderByProficiencyLevel(userId);
    }

    public long countByUserId(String userId) {
        return repository.countByUserId(userId);
    }

    public boolean existsByUserIdAndSkillName(String userId, String skillName) {
        return repository.existsByUserIdAndSkillName(userId, skillName);
    }

    public void deleteById(String id) {
        repository.deleteById(id);
    }

    public List<SkillEntity> getAll() {
        return repository.findAll();
    }
}
