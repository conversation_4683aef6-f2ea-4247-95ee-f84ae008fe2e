package com.marglabs.trackmyclass.profile.mapper;

import com.marglabs.trackmyclass.profile.entity.EducationEntity;
import com.marglabs.trackmyclass.profile.model.Education;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface EducationMapper {
    
    @Mapping(target = "createdDate", expression = "java(entity.getCreatedDate() != null ? entity.getCreatedDate().toEpochMilli() : 0)")
    @Mapping(target = "updatedDate", expression = "java(entity.getUpdatedDate() != null ? entity.getUpdatedDate().toEpochMilli() : 0)")
    Education toDto(EducationEntity entity);
    
    @Mapping(target = "createdDate", ignore = true) // Handled by @CreationTimestamp
    @Mapping(target = "updatedDate", ignore = true) // Handled by @UpdateTimestamp
    EducationEntity toEntity(Education dto);
}
