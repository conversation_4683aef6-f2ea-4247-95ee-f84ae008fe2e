package com.marglabs.trackmyclass.profile.mapper;

import com.marglabs.trackmyclass.profile.entity.CertificationEntity;
import com.marglabs.trackmyclass.profile.model.Certification;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface CertificationMapper {
    
    @Mapping(target = "createdDate", expression = "java(entity.getCreatedDate() != null ? entity.getCreatedDate().toEpochMilli() : 0)")
    @Mapping(target = "updatedDate", expression = "java(entity.getUpdatedDate() != null ? entity.getUpdatedDate().toEpochMilli() : 0)")
    Certification toDto(CertificationEntity entity);
    
    @Mapping(target = "createdDate", ignore = true) // Handled by @CreationTimestamp
    @Mapping(target = "updatedDate", ignore = true) // Handled by @UpdateTimestamp
    CertificationEntity toEntity(Certification dto);
}
