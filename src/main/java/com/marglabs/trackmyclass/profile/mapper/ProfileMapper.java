package com.marglabs.trackmyclass.profile.mapper;

import com.marglabs.trackmyclass.profile.entity.ProfileEntity;
import com.marglabs.trackmyclass.profile.model.Profile;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface ProfileMapper {

    @Mapping(target = "createdDate", expression = "java(entity.getCreatedDate() != null ? entity.getCreatedDate().toEpochMilli() : 0)")
    @Mapping(target = "updatedDate", expression = "java(entity.getUpdatedDate() != null ? entity.getUpdatedDate().toEpochMilli() : 0)")
    @Mapping(target = "educations", ignore = true) // Set by service
    @Mapping(target = "skills", ignore = true) // Set by service
    @Mapping(target = "certifications", ignore = true) // Set by service
    Profile toDto(ProfileEntity entity);

    @Mapping(target = "createdDate", ignore = true) // Handled by @CreationTimestamp
    @Mapping(target = "updatedDate", ignore = true) // Handled by @UpdateTimestamp
    ProfileEntity toEntity(Profile dto);
}
