package com.marglabs.trackmyclass.profile.model;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class Skill {
    private String id;
    private String userId;
    private String skillName;
    private String proficiencyLevel; // BEGINNER, INTERMEDIATE, ADVANCED, EXPERT
    private String category; // TECHNICAL, SOFT_SKILL, LANGUAGE, ACADEMIC, etc.
    private Integer yearsOfExperience;
    private String description;
    private long createdDate;
    private long updatedDate;
}
