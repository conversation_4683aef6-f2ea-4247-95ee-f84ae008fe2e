package com.marglabs.trackmyclass.profile.model;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
public class CertificationRequest {
    
    @NotBlank(message = "Certification name is required")
    @Size(max = 200, message = "Certification name cannot exceed 200 characters")
    private String certificationName;
    
    @Size(max = 50, message = "Certification level cannot exceed 50 characters")
    private String certificationLevel; // A1, A2, B1, B2, C1, C2 (CEFR levels) or other levels
    
    @Size(max = 200, message = "Issuing organization cannot exceed 200 characters")
    private String issuingOrganization;
    
    @Min(value = 1900, message = "Issue year must be after 1900")
    @Max(value = 2100, message = "Issue year must be before 2100")
    private Integer issueYear;
    
    @Min(value = 1900, message = "Expiry year must be after 1900")
    @Max(value = 2100, message = "Expiry year must be before 2100")
    private Integer expiryYear;
}
