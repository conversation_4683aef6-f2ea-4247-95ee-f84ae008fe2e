package com.marglabs.trackmyclass.profile.model;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
public class ProfileRequest {

    @Size(max = 2000, message = "About me cannot exceed 2000 characters")
    private String aboutMe;

    @Pattern(regexp = "^(MALE|FEMALE|OTHER|PREFER_NOT_TO_SAY)$", message = "Gender must be MALE, FEMALE, OTHER, or PREFER_NOT_TO_SAY")
    private String gender;

    @Size(max = 500, message = "Profile picture URL cannot exceed 500 characters")
    private String profilePicture;
}
