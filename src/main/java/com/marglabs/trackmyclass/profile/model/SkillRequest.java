package com.marglabs.trackmyclass.profile.model;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
public class SkillRequest {
    
    @NotBlank(message = "Skill name is required")
    @Size(max = 100, message = "Skill name cannot exceed 100 characters")
    private String skillName;
    
    @Size(max = 50, message = "Proficiency level cannot exceed 50 characters")
    private String proficiencyLevel; // BEGINNER, INTERMEDIATE, ADVANCED, EXPERT
    
    @Size(max = 50, message = "Category cannot exceed 50 characters")
    private String category; // TECHNICAL, SOFT_SKILL, LANGUAGE, ACADEMIC, etc.
    
    @Min(value = 0, message = "Years of experience cannot be negative")
    @Max(value = 50, message = "Years of experience cannot exceed 50")
    private Integer yearsOfExperience;
    
    @Size(max = 500, message = "Description cannot exceed 500 characters")
    private String description;
}
