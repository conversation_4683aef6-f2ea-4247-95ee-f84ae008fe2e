package com.marglabs.trackmyclass.profile.model;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class Education {
    private String id;
    private String userId;
    private String title; // Degree/Certificate title
    private String institution; // College/University name
    private String subject; // Field of study
    private Integer startYear;
    private Integer endYear;
    private String description;
    private long createdDate;
    private long updatedDate;
}
