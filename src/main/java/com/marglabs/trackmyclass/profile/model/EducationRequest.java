package com.marglabs.trackmyclass.profile.model;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
public class EducationRequest {

    @NotBlank(message = "Title is required")
    @Size(max = 200, message = "Title cannot exceed 200 characters")
    private String title;

    @NotBlank(message = "Institution is required")
    @Size(max = 200, message = "Institution cannot exceed 200 characters")
    private String institution;

    @Size(max = 200, message = "Subject cannot exceed 200 characters")
    private String subject;

    @Min(value = 1900, message = "Start year must be after 1900")
    @Max(value = 2100, message = "Start year must be before 2100")
    private Integer startYear;

    @Min(value = 1900, message = "End year must be after 1900")
    @Max(value = 2100, message = "End year must be before 2100")
    private Integer endYear;

    @Size(max = 1000, message = "Description cannot exceed 1000 characters")
    private String description;
}
