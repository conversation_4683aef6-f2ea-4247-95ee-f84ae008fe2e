package com.marglabs.trackmyclass.profile.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class Profile {
    private String id;
    private String userId;
    private String aboutMe;
    private String gender; // MALE, FEMALE, OTHER, PREFER_NOT_TO_SAY
    private String profilePicture;
    private long createdDate;
    private long updatedDate;

    // Related data (transient fields)
    private List<Education> educations;
    private List<Skill> skills;
    private List<Certification> certifications;
}
