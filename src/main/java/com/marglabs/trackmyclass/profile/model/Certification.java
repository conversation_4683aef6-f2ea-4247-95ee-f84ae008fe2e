package com.marglabs.trackmyclass.profile.model;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class Certification {
    private String id;
    private String userId;
    private String certificationName;
    private String certificationLevel; // A1, A2, B1, B2, C1, C2 (CEFR levels) or other levels
    private String issuingOrganization;
    private Integer issueYear;
    private Integer expiryYear;
    private long createdDate;
    private long updatedDate;
}
