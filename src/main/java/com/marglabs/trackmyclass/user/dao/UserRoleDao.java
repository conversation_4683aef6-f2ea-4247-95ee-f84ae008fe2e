package com.marglabs.trackmyclass.user.dao;

import com.marglabs.common.rest.error.EntityNotFoundException;
import com.marglabs.trackmyclass.user.entity.UserRoleEntity;
import com.marglabs.trackmyclass.user.repository.UserRoleRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Component
public class UserRoleDao {
    @Autowired
    private UserRoleRepository repository;

    public UserRoleEntity create(UserRoleEntity entity) {
        entity.setCreatedDate(new Date());
        return repository.save(entity);
    }

    public UserRoleEntity getById(String id) {
        Optional<UserRoleEntity> entity = repository.findById(id);
        if (entity.isPresent()) {
            return entity.get();
        }
        throw new EntityNotFoundException(HttpStatus.NOT_FOUND, "USER_ROLE", "USER_ROLE_NOT_FOUND", "user_role_not_found_details", id);
    }
    
    public List<UserRoleEntity> getByUserId(String userId) {
        return repository.findByUserId(userId);
    }
    
    public Optional<UserRoleEntity> findByUserIdAndRole(String userId, String role) {
        return repository.findByUserIdAndRole(userId, role);
    }
    
    public boolean existsByUserIdAndRole(String userId, String role) {
        return repository.existsByUserIdAndRole(userId, role);
    }

    public void deleteById(String id) {
        try {
            repository.deleteById(id);
        } catch (EmptyResultDataAccessException e) {
            throw new EntityNotFoundException(HttpStatus.NOT_FOUND, "USER_ROLE", "USER_ROLE_NOT_FOUND", "user_role_not_found_details", id);
        }
    }
    
    public void deleteByUserIdAndRole(String userId, String role) {
        repository.deleteByUserIdAndRole(userId, role);
    }
}
