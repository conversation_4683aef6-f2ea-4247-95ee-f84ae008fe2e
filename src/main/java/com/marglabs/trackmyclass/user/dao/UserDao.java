package com.marglabs.trackmyclass.user.dao;

import com.marglabs.common.rest.error.EntityNotFoundException;
import com.marglabs.trackmyclass.user.entity.UserEntity;
import com.marglabs.trackmyclass.user.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

@Component
public class UserDao {
    @Autowired
    private UserRepository repository;

    public UserEntity create(UserEntity entity) {
        long now = Instant.now().toEpochMilli();
        entity.setCreatedDate(now);
        entity.setUpdatedDate(now);
        entity.setActive(true);
        return repository.save(entity);
    }

    public UserEntity update(UserEntity entity) {
        UserEntity existingEntity = getById(entity.getId());
        entity.setCreatedDate(existingEntity.getCreatedDate());
        entity.setUpdatedDate(Instant.now().toEpochMilli());
        return repository.save(entity);
    }

    public UserEntity getById(String id) {
        Optional<UserEntity> entity = repository.findById(id);
        if (entity.isPresent()) {
            return entity.get();
        }
        throw new EntityNotFoundException(HttpStatus.NOT_FOUND, "USER", "USER_NOT_FOUND", "user_not_found_details", id);
    }
    
    public Optional<UserEntity> findByCognitoId(String cognitoId) {
        return repository.findByCognitoId(cognitoId);
    }
    
    public Optional<UserEntity> findByEmail(String email) {
        return repository.findByEmail(email);
    }
    
    public boolean existsByEmail(String email) {
        return repository.existsByEmail(email);
    }
    
    public boolean existsByCognitoId(String cognitoId) {
        return repository.existsByCognitoId(cognitoId);
    }
    
    public List<UserEntity> getAll() {
        return repository.findAll();
    }

    public void deleteById(String id) {
        try {
            repository.deleteById(id);
        } catch (EmptyResultDataAccessException e) {
            throw new EntityNotFoundException(HttpStatus.NOT_FOUND, "USER", "USER_NOT_FOUND", "user_not_found_details", id);
        }
    }
    
    public UserEntity updateLastLogin(String id) {
        UserEntity entity = getById(id);
        entity.setLastLoginDate(Instant.now().toEpochMilli());
        return repository.save(entity);
    }
}
