package com.marglabs.trackmyclass.user.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class User {
    private String id;
    private String name;
    private String email;
    private String cognitoId;
    private String phoneNumber;
    private String profilePicture;
    private long createdDate;
    private long updatedDate;
    private long lastLoginDate;
    private String timezone;
    private boolean active;
    private List<UserRole> roles;
}
