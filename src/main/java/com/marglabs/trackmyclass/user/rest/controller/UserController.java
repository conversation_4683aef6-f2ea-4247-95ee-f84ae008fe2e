package com.marglabs.trackmyclass.user.rest.controller;

import com.marglabs.trackmyclass.user.model.AddRoleRequest;
import com.marglabs.trackmyclass.user.model.RoleType;
import com.marglabs.trackmyclass.user.model.User;
import com.marglabs.trackmyclass.user.model.UserRole;
import com.marglabs.trackmyclass.user.service.UserService;
import jakarta.validation.Valid;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/userManagement/v1")
@Tag(name = "User Management", description = "API for managing users and their roles")
public class UserController {
    @Autowired
    private UserService userService;

    @Operation(summary = "Get current user", description = "Returns the current authenticated user with their roles")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successful operation",
                content = @Content(schema = @Schema(implementation = User.class))),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/me", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public User getCurrentUser(@AuthenticationPrincipal Jwt userPrincipal) {
        String cognitoId = userPrincipal.getSubject();
        String email = userPrincipal.getClaimAsString("email");
//        String name = userPrincipal.getClaimAsString("name");

        // Sync user from Cognito (creates if not exists, updates if exists)
        return userService.syncUserFromCognito(cognitoId, email);
    }

    @Operation(summary = "Get user by ID", description = "Returns a user by their ID")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successful operation",
                content = @Content(schema = @Schema(implementation = User.class))),
        @ApiResponse(responseCode = "404", description = "User not found"),
        @ApiResponse(responseCode = "403", description = "Forbidden - requires admin access")
    })
    @GetMapping(value = "/users/{id}", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
//    @PreAuthorize("hasRole('ADMIN')")
    public User getUserById(@PathVariable String id) {
        return userService.getUserById(id);
    }

    @Operation(summary = "Get all users", description = "Returns all users with their roles")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successful operation"),
        @ApiResponse(responseCode = "403", description = "Forbidden - requires admin access")
    })
    @GetMapping(value = "/users", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
//    @PreAuthorize("hasRole('ADMIN')")
    public List<User> getAllUsers() {
        return userService.getAllUsers();
    }

    @Operation(summary = "Add role to user", description = "Adds a role to a user and updates their name")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Role added successfully",
                content = @Content(schema = @Schema(implementation = UserRole.class))),
        @ApiResponse(responseCode = "400", description = "User already has this role"),
        @ApiResponse(responseCode = "404", description = "User not found"),
        @ApiResponse(responseCode = "403", description = "Forbidden - requires admin access")
    })
    @PostMapping(value = "/users/roles", consumes = "application/json", produces = "application/json")
    @ResponseStatus(HttpStatus.CREATED)
//    @PreAuthorize("hasRole('ADMIN')")
    public UserRole addRoleToUser(@AuthenticationPrincipal Jwt userPrincipal,
            @Parameter(description = "Add role request") @Valid @RequestBody AddRoleRequest request)
 {
     User currentUser = userService.getUserByCognitoId(userPrincipal.getSubject());

     return userService.addRoleToUserWithName(currentUser.getId(), request.getRole(), request.getName(), request.getEmail(), request.getTimezone());
    }


    @Operation(summary = "Remove role from user", description = "Removes a role from a user")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "Role removed successfully"),
        @ApiResponse(responseCode = "404", description = "User or role not found"),
        @ApiResponse(responseCode = "403", description = "Forbidden - requires admin access")
    })
    @DeleteMapping(value = "/users/{id}/roles/{role}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
//    @PreAuthorize("hasRole('ADMIN')")
    public void removeRoleFromUser(
            @Parameter(description = "User ID") @PathVariable String id,
            @Parameter(description = "Role type") @PathVariable RoleType role) {
        userService.removeRoleFromUser(id, role);
    }



    @Operation(summary = "Get user roles", description = "Returns all roles for a user")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successful operation"),
        @ApiResponse(responseCode = "404", description = "User not found"),
        @ApiResponse(responseCode = "403", description = "Forbidden - requires admin access")
    })
    @GetMapping(value = "/users/{id}/roles", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
//    @PreAuthorize("hasRole('ADMIN')")
    public List<UserRole> getUserRoles(@PathVariable String id) {
        // This will throw an exception if user doesn't exist
        userService.getUserById(id);
        return userService.getUserRoles(id);
    }


    @Operation(summary = "Check if user has role", description = "Checks if a user has a specific role")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successful operation"),
        @ApiResponse(responseCode = "404", description = "User not found"),
        @ApiResponse(responseCode = "403", description = "Forbidden - requires admin access")
    })
    @GetMapping(value = "/users/{id}/roles/{role}", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
//    @PreAuthorize("hasRole('ADMIN')")
    public Map<String, Boolean> hasRole(
            @Parameter(description = "User ID") @PathVariable String id,
            @Parameter(description = "Role type") @PathVariable RoleType role) {
        // This will throw an exception if user doesn't exist
        userService.getUserById(id);
        boolean hasRole = userService.hasRole(id, role);
        return Map.of("hasRole", hasRole);
    }

    @Operation(summary = "Update user profile", description = "Updates a user's profile information")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Profile updated successfully",
                content = @Content(schema = @Schema(implementation = User.class))),
        @ApiResponse(responseCode = "404", description = "User not found"),
        @ApiResponse(responseCode = "403", description = "Forbidden - can only update own profile unless admin")
    })
    @PutMapping(value = "/users/profile", consumes = "application/json", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public User updateUserProfile(
            @AuthenticationPrincipal Jwt userPrincipal,
            @Parameter(description = "Profile data") @RequestBody Map<String, String> profileData) {

        // Only allow users to update their own profile unless they're an admin
        User currentUser = userService.getUserByCognitoId(userPrincipal.getSubject());
        boolean isAdmin = currentUser.getRoles().stream()
                .anyMatch(role -> role.getRole() == RoleType.ADMIN);

        if (!currentUser.getId().equals(currentUser.getId()) && !isAdmin) {
            throw new SecurityException("You can only update your own profile");
        }

        return userService.updateUserProfile(
                currentUser.getId(),
               profileData
        );
    }

    @Operation(summary = "Deactivate user", description = "Deactivates a user account")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "User deactivated successfully"),
        @ApiResponse(responseCode = "404", description = "User not found"),
        @ApiResponse(responseCode = "403", description = "Forbidden - requires admin access")
    })
    @PatchMapping(value = "/users/{id}/deactivate")
    @ResponseStatus(HttpStatus.NO_CONTENT)
//    @PreAuthorize("hasRole('ADMIN')")
    public void deactivateUser(@PathVariable String id) {
        userService.deactivateUser(id);
    }

    @Operation(summary = "Activate user", description = "Activates a user account")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "User activated successfully"),
        @ApiResponse(responseCode = "404", description = "User not found"),
        @ApiResponse(responseCode = "403", description = "Forbidden - requires admin access")
    })
    @PatchMapping(value = "/users/{id}/activate")
    @ResponseStatus(HttpStatus.NO_CONTENT)
//    @PreAuthorize("hasRole('ADMIN')")
    public void activateUser(@PathVariable String id) {
        userService.activateUser(id);
    }
}
