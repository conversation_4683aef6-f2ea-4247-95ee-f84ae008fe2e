package com.marglabs.trackmyclass.user.service;

import com.marglabs.common.rest.error.EntityNotFoundException;
import com.marglabs.common.rest.error.GeneralException;
import com.marglabs.common.utils.IdGenerator;
import com.marglabs.trackmyclass.user.dao.UserDao;
import com.marglabs.trackmyclass.user.dao.UserRoleDao;
import com.marglabs.trackmyclass.user.entity.UserEntity;
import com.marglabs.trackmyclass.user.entity.UserRoleEntity;
import com.marglabs.trackmyclass.user.mapper.UserMapper;
import com.marglabs.trackmyclass.user.model.RoleType;
import com.marglabs.trackmyclass.user.model.User;
import com.marglabs.trackmyclass.user.model.UserRole;
import com.marglabs.trackmyclass.email.service.EmailService;
import com.marglabs.trackmyclass.email.model.EmailTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
public class UserService {
    @Autowired
    private UserDao userDao;

    @Autowired
    private UserRoleDao userRoleDao;

    @Autowired
    private UserMapper mapper;

    @Autowired
    private IdGenerator idGenerator;
    
    @Autowired
    private EmailService emailService;

    /**
     * Create a new user without Cognito integration (for students added by teachers)
     */
    @Transactional
    public User createUserWithoutCognito(String email, String name) {
        // Check if user already exists by email
        Optional<UserEntity> existingUser = userDao.findByEmail(email);
        if (existingUser.isPresent()) {
            throw new GeneralException(HttpStatus.CONFLICT, "USER", "USER_EMAIL_EXISTS",
                    "user_email_already_exists", email);
        }

        // Create new user entity
        UserEntity userEntity = new UserEntity();
        userEntity.setId(idGenerator.generateId());
        userEntity.setEmail(email);
        userEntity.setName(name);
        userEntity.setCognitoId(null); // No Cognito integration

        userEntity = userDao.create(userEntity);

        User user = mapper.toDto(userEntity);
        user.setRoles(List.of()); // New user has no roles yet
        return user;
    }

    /**
     * Find existing user by email or create a new one
     */
    @Transactional
    public User findOrCreateUser(String email, String name) {
        try {
            // Try to find existing user
            return getUserByEmail(email);
        } catch (EntityNotFoundException e) {
            // User doesn't exist, create a new one
            return createUserWithoutCognito(email, name);
        }
    }

    /**
     * Creates or updates a user based on Cognito authentication
     */
    @Transactional
    public User syncUserFromCognito(String cognitoId, String email) {
        Optional<UserEntity> existingUser = userDao.findByCognitoId(cognitoId);

        if (existingUser.isPresent()) {
            // Update existing user
            UserEntity userEntity = existingUser.get();
            userEntity.setEmail(email);
            userEntity = userDao.updateLastLogin(userEntity.getId());

            User user = mapper.toDto(userEntity);
            user.setRoles(getUserRoles(userEntity.getId()));
            return user;
        } else {
            // Create new user
            UserEntity userEntity = new UserEntity();
            userEntity.setId(idGenerator.generateId());
            userEntity.setCognitoId(cognitoId);
            userEntity.setEmail(email);

            userEntity = userDao.create(userEntity);

            User user = mapper.toDto(userEntity);
            user.setRoles(List.of()); // New user has no roles yet
            return user;
        }
    }

    /**
     * Get user by ID with roles
     */
    public User getUserById(String id) {
        UserEntity userEntity = userDao.getById(id);
        User user = mapper.toDto(userEntity);
        user.setRoles(getUserRoles(id));
        return user;
    }

    /**
     * Get user by Cognito ID with roles
     */
    public User getUserByCognitoId(String cognitoId) {
        UserEntity userEntity = userDao.findByCognitoId(cognitoId)
                .orElseThrow(() -> new EntityNotFoundException(HttpStatus.NOT_FOUND, "USER", "USER_NOT_FOUND", "user_not_found_by_cognito_id", cognitoId));

        User user = mapper.toDto(userEntity);
        user.setRoles(getUserRoles(user.getId()));
        return user;
    }

    /**
     * Get user by email with roles
     */
    public User getUserByEmail(String email) {
        UserEntity userEntity = userDao.findByEmail(email)
                .orElseThrow(() -> new EntityNotFoundException(HttpStatus.NOT_FOUND, "USER", "USER_NOT_FOUND", "user_not_found_by_email", email));

        User user = mapper.toDto(userEntity);
        user.setRoles(getUserRoles(user.getId()));
        return user;
    }

    /**
     * Get all users with their roles
     */
    public List<User> getAllUsers() {
        return userDao.getAll().stream()
                .map(entity -> {
                    User user = mapper.toDto(entity);
                    user.setRoles(getUserRoles(entity.getId()));
                    return user;
                })
                .collect(Collectors.toList());
    }


    /**
     * Add a role to a user
     */
    @Transactional
    public UserRole addRoleToUser(String userId, RoleType roleType) {

        // Check if role already exists
        if (userRoleDao.existsByUserIdAndRole(userId, roleType.name())) {
            Optional<UserRoleEntity> byUserIdAndRole = userRoleDao.findByUserIdAndRole(userId, roleType.name());
            return mapper.toRoleDto(byUserIdAndRole.get());
        }else{
            // Create new role

            UserRoleEntity userRoleEntity = new UserRoleEntity();
            userRoleEntity.setId(idGenerator.generateId());
            userRoleEntity.setUserId(userId);
            userRoleEntity.setRole(roleType.name());

            userRoleEntity = userRoleDao.create(userRoleEntity);

            return mapper.toRoleDto(userRoleEntity);
        }


    }
    
    /**
     * Add a role to a user and update name, email and timezone
     */
    @Transactional
    public UserRole addRoleToUserWithName(String userId, RoleType roleType, String name, String email, String timezone) {
        // Update user details
        UserEntity userEntity = userDao.getById(userId);
        userEntity.setName(name);
        userEntity.setEmail(email);
        if (timezone != null && !timezone.isBlank()) {
            userEntity.setTimezone(timezone);
        }
        userDao.update(userEntity);
        
        // Add role
        UserRole userRole = addRoleToUser(userId, roleType);
        
        // Send welcome email
        try {
            Map<String, Object> templateData = Map.of(
                "userName", name,
                "userRole", roleType.name()
            );

        } catch (Exception e) {
            // Log error but don't fail the operation
        }
        
        return userRole;
    }

    /**
     * Remove a role from a user
     */
    @Transactional
    public void removeRoleFromUser(String userId, RoleType roleType) {
        // Check if user exists
        userDao.getById(userId);

        // Remove role
        userRoleDao.deleteByUserIdAndRole(userId, roleType.name());
    }



    /**
     * Get all roles for a user
     */
    public List<UserRole> getUserRoles(String userId) {
        List<UserRoleEntity> byUserId = userRoleDao.getByUserId(userId);
        return byUserId.stream()
                .map(mapper::toRoleDto)
                .collect(Collectors.toList());
    }

    /**
     * Check if a user has a specific role
     */
    public boolean hasRole(String userId, RoleType roleType) {
        return userRoleDao.existsByUserIdAndRole(userId, roleType.name());
    }

    /**
     * Update user profile
     */
    @Transactional
    public User updateUserProfile(String id, Map<String, String> profileData) {
        UserEntity userEntity = userDao.getById(id);
        String name= profileData.get("name");
        String phoneNumber =  profileData.get("phone");
        String profilePicture =  profileData.get("profilepicture");
        String email =  profileData.get("email");

        if (email != null && !email.isBlank()) {
            userEntity.setEmail(name);
        }
        if (name != null && !name.isBlank()) {
            userEntity.setName(name);
        }

        if (phoneNumber != null) {
            userEntity.setPhoneNumber(phoneNumber);
        }

        if (profilePicture != null) {
            userEntity.setProfilePicture(profilePicture);
        }
        
        String timezone = profileData.get("timezone");
        if (timezone != null) {
            userEntity.setTimezone(timezone);
        }

        userEntity = userDao.update(userEntity);

        User user = mapper.toDto(userEntity);
        user.setRoles(getUserRoles(id));
        return user;
    }

    /**
     * Deactivate a user
     */
    @Transactional
    public void deactivateUser(String id) {
        UserEntity userEntity = userDao.getById(id);
        userEntity.setActive(false);
        userDao.update(userEntity);
    }

    /**
     * Activate a user
     */
    @Transactional
    public void activateUser(String id) {
        UserEntity userEntity = userDao.getById(id);
        userEntity.setActive(true);
        userDao.update(userEntity);
    }
}
