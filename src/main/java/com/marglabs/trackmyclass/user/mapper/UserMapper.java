package com.marglabs.trackmyclass.user.mapper;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.marglabs.trackmyclass.user.entity.UserEntity;
import com.marglabs.trackmyclass.user.entity.UserRoleEntity;
import com.marglabs.trackmyclass.user.model.RoleType;
import com.marglabs.trackmyclass.user.model.User;
import com.marglabs.trackmyclass.user.model.UserRole;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.beans.factory.annotation.Autowired;

@Mapper(componentModel = "spring")
public abstract class UserMapper {
    
    @Autowired
    private ObjectMapper objectMapper;
    
    public abstract UserEntity toEntity(User user);
    
    public abstract User                                                                                                                                                        toDto(UserEntity entity);
    
    @Mapping(target = "role", expression = "java(mapToRoleType(entity.getRole()))")
    public abstract UserRole toRoleDto(UserRoleEntity entity);
    

    protected RoleType mapToRoleType(String role) {
        return RoleType.valueOf(role);
    }
    

}
