package com.marglabs.trackmyclass.user.repository;

import com.marglabs.trackmyclass.user.entity.UserEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.rest.core.annotation.RepositoryRestResource;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
@RepositoryRestResource(exported = false)
public interface UserRepository extends JpaRepository<UserEntity, String> {
    Optional<UserEntity> findByCognitoId(String cognitoId);
    Optional<UserEntity> findByEmail(String email);
    boolean existsByEmail(String email);
    boolean existsByCognitoId(String cognitoId);
}
