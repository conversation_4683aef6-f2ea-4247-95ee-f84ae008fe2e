package com.marglabs.trackmyclass.user.repository;

import com.marglabs.trackmyclass.user.entity.UserRoleEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.rest.core.annotation.RepositoryRestResource;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
@RepositoryRestResource(exported = false)
public interface UserRoleRepository extends JpaRepository<UserRoleEntity, String> {
    List<UserRoleEntity> findByUserId(String userId);
    Optional<UserRoleEntity> findByUserIdAndRole(String userId, String role);
    boolean existsByUserIdAndRole(String userId, String role);
    void deleteByUserIdAndRole(String userId, String role);
}
