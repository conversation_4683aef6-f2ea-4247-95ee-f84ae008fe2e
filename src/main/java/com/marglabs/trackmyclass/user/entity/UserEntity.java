package com.marglabs.trackmyclass.user.entity;

import jakarta.persistence.*;
import lombok.Data;

@Entity
@Table(name = "users")
@Data
public class UserEntity {
    @Id
    private String id;

    @Column(name = "name")
    private String name;

    @Column(name = "email")
    private String email;

    @Column(name = "cognito_id", nullable = true, unique = true)
    private String cognitoId;

    @Column(name = "phone_number")
    private String phoneNumber;

    @Column(name = "profile_picture")
    private String profilePicture;

    @Column(name = "created_date")
    private long createdDate;

    @Column(name = "updated_date")
    private long updatedDate;

    @Column(name = "last_login_date")
    private long lastLoginDate;

    @Column(name = "timezone")
    private String timezone;

    @Column
    private boolean active;
}
