package com.marglabs.trackmyclass.attendance.dao;

import com.marglabs.common.rest.error.EntityNotFoundException;
import com.marglabs.trackmyclass.attendance.entity.AttendanceEntity;
import com.marglabs.trackmyclass.attendance.model.AttendanceStatus;
import com.marglabs.trackmyclass.attendance.repository.AttendanceRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

@Component
public class AttendanceDao {

    @Autowired
    private AttendanceRepository repository;

    public AttendanceEntity create(AttendanceEntity entity) {
        // Timestamps are set automatically by @CreationTimestamp and @UpdateTimestamp
        return repository.save(entity);
    }

    public AttendanceEntity update(AttendanceEntity entity) {
        // UpdateTimestamp is set automatically by @UpdateTimestamp
        return repository.save(entity);
    }

    public AttendanceEntity getById(String id) {
        return repository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException(HttpStatus.NOT_FOUND, "ATTENDANCE", "ATTENDANCE_NOT_FOUND",
                        "attendance_not_found_details", id));
    }

    public Optional<AttendanceEntity> findById(String id) {
        return repository.findById(id);
    }

    public List<AttendanceEntity> getAll() {
        return repository.findAll();
    }

    public Page<AttendanceEntity> getAll(Pageable pageable) {
        return repository.findAll(pageable);
    }

    public List<AttendanceEntity> getByClassroom(String classroomId) {
        return repository.findByClassroomIdOrderByAttendanceDateDesc(classroomId);
    }

    public List<AttendanceEntity> getByClassroomAndDate(String classroomId, String attendanceDate) {
        return repository.findByClassroomIdAndAttendanceDate(classroomId, attendanceDate);
    }

    public List<AttendanceEntity> getByStudentAndDateRange(String studentId, String startDate, String endDate) {
        return repository.findByStudentIdAndDateRange(studentId, startDate, endDate);
    }

    public List<AttendanceEntity> getByClassroomAndDateRange(String classroomId, String startDate, String endDate) {
        return repository.findByClassroomIdAndDateRange(classroomId, startDate, endDate);
    }

    public List<AttendanceEntity> getByTeacherAndDateRange(String teacherId, String startDate, String endDate) {
        return repository.findByTeacherIdAndDateRange(teacherId, startDate, endDate);
    }

    public List<AttendanceEntity> getByStudentAndClassroom(String studentId, String classroomId) {
        return repository.findByStudentIdAndClassroomIdOrderByAttendanceDateDesc(studentId, classroomId);
    }

    public List<AttendanceEntity> getByClassroomAndStatus(String classroomId, AttendanceStatus status) {
        return repository.findByClassroomIdAndStatus(classroomId, status);
    }

    public Optional<AttendanceEntity> findExistingAttendance(String classroomId, String studentId, String attendanceDate) {
        return repository.findByClassroomIdAndStudentIdAndAttendanceDate(classroomId, studentId, attendanceDate);
    }

    public List<Object[]> getAttendanceStatsByClassroom(String classroomId, String startDate, String endDate) {
        return repository.getAttendanceStatsByClassroom(classroomId, startDate, endDate);
    }

    public List<Object[]> getAttendanceStatsByStudent(String studentId, String startDate, String endDate) {
        return repository.getAttendanceStatsByStudent(studentId, startDate, endDate);
    }

    public Page<AttendanceEntity> getByClassroomPaginated(String classroomId, Pageable pageable) {
        return repository.findByClassroomIdOrderByAttendanceDateDesc(classroomId, pageable);
    }

    public Page<AttendanceEntity> getByTeacherPaginated(String teacherId, Pageable pageable) {
        return repository.findByTeacherIdOrderByAttendanceDateDesc(teacherId, pageable);
    }

    public long countByClassroom(String classroomId) {
        return repository.countByClassroomId(classroomId);
    }

    public long countByClassroomAndStatus(String classroomId, AttendanceStatus status) {
        return repository.countByClassroomIdAndStatus(classroomId, status);
    }

    public void deleteById(String id) {
        try {
            repository.deleteById(id);
        } catch (EmptyResultDataAccessException e) {
            throw new EntityNotFoundException(HttpStatus.NOT_FOUND, "ATTENDANCE", "ATTENDANCE_NOT_FOUND",
                    "attendance_not_found_details", id);
        }
    }

    public List<String> getDistinctAttendanceDates(String classroomId, String startDate, String endDate) {
        return repository.getDistinctAttendanceDates(classroomId, startDate, endDate);
    }

    public List<Object[]> getAttendanceSummaryByStudent(String classroomId, String startDate, String endDate) {
        return repository.getAttendanceSummaryByStudent(classroomId, startDate, endDate);
    }

    public List<Object[]> getAttendanceByDateAndStatus(String classroomId, String startDate, String endDate) {
        return repository.getAttendanceByDateAndStatus(classroomId, startDate, endDate);
    }

    public List<String> getDistinctStudentIds(String classroomId, String startDate, String endDate) {
        return repository.getDistinctStudentIds(classroomId, startDate, endDate);
    }
    
    public long countDistinctClassesByClassroom(String classroomId) {
        return repository.countDistinctAttendanceDatesByClassroomId(classroomId);
    }
    
    public long countPresentByClassroom(String classroomId) {
        return repository.countByClassroomIdAndStatus(classroomId, AttendanceStatus.PRESENT) +
               repository.countByClassroomIdAndStatus(classroomId, AttendanceStatus.LATE) +
               repository.countByClassroomIdAndStatus(classroomId, AttendanceStatus.PARTIAL);
    }
    
    public long countAbsentByClassroom(String classroomId) {
        return repository.countByClassroomIdAndStatus(classroomId, AttendanceStatus.ABSENT) +
               repository.countByClassroomIdAndStatus(classroomId, AttendanceStatus.EXCUSED);
    }
}
