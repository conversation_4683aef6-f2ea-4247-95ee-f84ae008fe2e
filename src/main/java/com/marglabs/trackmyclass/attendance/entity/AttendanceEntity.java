package com.marglabs.trackmyclass.attendance.entity;

import com.marglabs.trackmyclass.attendance.model.AttendanceStatus;
import jakarta.persistence.*;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.Instant;

@Entity
@Table(name = "attendance")
@Data
@Accessors(chain = true)
public class AttendanceEntity {

    @Id
    private String id;

    @Column(name = "classroom_id", nullable = false)
    private String classroomId;

    @Column(name = "student_id", nullable = false)
    private String studentId;

    @Column(name = "teacher_id", nullable = false)
    private String teacherId;

    @Column(name = "attendance_date", nullable = false)
    private String attendanceDate; // Format: YYYY-MM-DD

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private AttendanceStatus status;

    @Column(name = "notes", length = 500)
    private String notes;

    @Column(name = "marked_by")
    private String markedBy; // Teacher who marked the attendance

    @CreationTimestamp
    @Column(name = "created_date", nullable = false, updatable = false)
    private Instant createdDate;

    @UpdateTimestamp
    @Column(name = "updated_date", nullable = false)
    private Instant updatedDate;

    // Composite unique constraint to prevent duplicate attendance records
    @Table(uniqueConstraints = {
        @UniqueConstraint(columnNames = {"classroom_id", "student_id", "attendance_date"})
    })
    public static class UniqueConstraints {}
}
