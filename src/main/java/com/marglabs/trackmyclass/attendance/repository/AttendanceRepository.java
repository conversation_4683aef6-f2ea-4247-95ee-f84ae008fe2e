package com.marglabs.trackmyclass.attendance.repository;

import com.marglabs.trackmyclass.attendance.entity.AttendanceEntity;
import com.marglabs.trackmyclass.attendance.model.AttendanceStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.data.rest.core.annotation.RepositoryRestResource;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
@RepositoryRestResource(exported = false)
public interface AttendanceRepository extends JpaRepository<AttendanceEntity, String> {

    // Find attendance by classroom
    List<AttendanceEntity> findByClassroomIdOrderByAttendanceDateDesc(String classroomId);

    // Find attendance by classroom and date
    List<AttendanceEntity> findByClassroomIdAndAttendanceDate(String classroomId, String attendanceDate);

    // Find attendance by student and date range
    @Query("SELECT a FROM AttendanceEntity a WHERE a.studentId = :studentId " +
           "AND a.attendanceDate BETWEEN :startDate AND :endDate ORDER BY a.attendanceDate DESC")
    List<AttendanceEntity> findByStudentIdAndDateRange(@Param("studentId") String studentId,
                                                       @Param("startDate") String startDate,
                                                       @Param("endDate") String endDate);

    // Find attendance by classroom and date range
    @Query("SELECT a FROM AttendanceEntity a WHERE a.classroomId = :classroomId " +
           "AND a.attendanceDate BETWEEN :startDate AND :endDate ORDER BY a.attendanceDate DESC")
    List<AttendanceEntity> findByClassroomIdAndDateRange(@Param("classroomId") String classroomId,
                                                         @Param("startDate") String startDate,
                                                         @Param("endDate") String endDate);

    // Find attendance by teacher and date range
    @Query("SELECT a FROM AttendanceEntity a WHERE a.teacherId = :teacherId " +
           "AND a.attendanceDate BETWEEN :startDate AND :endDate ORDER BY a.attendanceDate DESC")
    List<AttendanceEntity> findByTeacherIdAndDateRange(@Param("teacherId") String teacherId,
                                                       @Param("startDate") String startDate,
                                                       @Param("endDate") String endDate);

    // Find attendance by student and classroom
    List<AttendanceEntity> findByStudentIdAndClassroomIdOrderByAttendanceDateDesc(String studentId, String classroomId);

    // Find attendance by status
    List<AttendanceEntity> findByClassroomIdAndStatus(String classroomId, AttendanceStatus status);

    // Check if attendance exists for specific student and date
    Optional<AttendanceEntity> findByClassroomIdAndStudentIdAndAttendanceDate(
            String classroomId, String studentId, String attendanceDate);

    // Get attendance statistics for a classroom
    @Query("SELECT a.status, COUNT(a) FROM AttendanceEntity a WHERE a.classroomId = :classroomId " +
           "AND a.attendanceDate BETWEEN :startDate AND :endDate GROUP BY a.status")
    List<Object[]> getAttendanceStatsByClassroom(@Param("classroomId") String classroomId,
                                                 @Param("startDate") String startDate,
                                                 @Param("endDate") String endDate);

    // Get attendance statistics for a student
    @Query("SELECT a.status, COUNT(a) FROM AttendanceEntity a WHERE a.studentId = :studentId " +
           "AND a.attendanceDate BETWEEN :startDate AND :endDate GROUP BY a.status")
    List<Object[]> getAttendanceStatsByStudent(@Param("studentId") String studentId,
                                               @Param("startDate") String startDate,
                                               @Param("endDate") String endDate);

    // Find attendance with pagination
    Page<AttendanceEntity> findByClassroomIdOrderByAttendanceDateDesc(String classroomId, Pageable pageable);

    // Find attendance by teacher with pagination
    Page<AttendanceEntity> findByTeacherIdOrderByAttendanceDateDesc(String teacherId, Pageable pageable);

    // Count total attendance records for a classroom
    long countByClassroomId(String classroomId);

    // Count attendance by status for a classroom
    long countByClassroomIdAndStatus(String classroomId, AttendanceStatus status);

    // Get distinct attendance dates for a classroom
    @Query("SELECT DISTINCT a.attendanceDate FROM AttendanceEntity a WHERE a.classroomId = :classroomId " +
           "AND a.attendanceDate BETWEEN :startDate AND :endDate ORDER BY a.attendanceDate")
    List<String> getDistinctAttendanceDates(@Param("classroomId") String classroomId,
                                           @Param("startDate") String startDate,
                                           @Param("endDate") String endDate);

    // Get attendance summary by student for a classroom
    @Query("SELECT a.studentId, a.status, COUNT(a) FROM AttendanceEntity a WHERE a.classroomId = :classroomId " +
           "AND a.attendanceDate BETWEEN :startDate AND :endDate GROUP BY a.studentId, a.status")
    List<Object[]> getAttendanceSummaryByStudent(@Param("classroomId") String classroomId,
                                                @Param("startDate") String startDate,
                                                @Param("endDate") String endDate);

    // Get attendance by date for statistics
    @Query("SELECT a.attendanceDate, a.status, COUNT(a) FROM AttendanceEntity a WHERE a.classroomId = :classroomId " +
           "AND a.attendanceDate BETWEEN :startDate AND :endDate GROUP BY a.attendanceDate, a.status ORDER BY a.attendanceDate")
    List<Object[]> getAttendanceByDateAndStatus(@Param("classroomId") String classroomId,
                                               @Param("startDate") String startDate,
                                               @Param("endDate") String endDate);

    // Get distinct student IDs for a classroom in date range
    @Query("SELECT DISTINCT a.studentId FROM AttendanceEntity a WHERE a.classroomId = :classroomId " +
           "AND a.attendanceDate BETWEEN :startDate AND :endDate")
    List<String> getDistinctStudentIds(@Param("classroomId") String classroomId,
                                      @Param("startDate") String startDate,
                                      @Param("endDate") String endDate);
    
    // Count distinct attendance dates (classes held) for a classroom
    @Query("SELECT COUNT(DISTINCT a.attendanceDate) FROM AttendanceEntity a WHERE a.classroomId = :classroomId")
    long countDistinctAttendanceDatesByClassroomId(@Param("classroomId") String classroomId);
}
