package com.marglabs.trackmyclass.attendance.mapper;

import com.marglabs.trackmyclass.attendance.entity.AttendanceEntity;
import com.marglabs.trackmyclass.attendance.model.Attendance;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface AttendanceMapper {

    @Mapping(target = "studentName", ignore = true)
    @Mapping(target = "studentEmail", ignore = true)
    @Mapping(target = "classroomName", ignore = true)
    @Mapping(target = "teacherName", ignore = true)
    @Mapping(target = "markedByName", ignore = true)
    Attendance toDto(AttendanceEntity entity);

    @Mapping(target = "createdDate", ignore = true)
    @Mapping(target = "updatedDate", ignore = true)
    AttendanceEntity toEntity(Attendance attendance);
}
