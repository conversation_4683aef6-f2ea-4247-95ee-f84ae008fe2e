package com.marglabs.trackmyclass.attendance.service;

import com.marglabs.common.rest.error.GeneralException;
import com.marglabs.common.utils.IdGenerator;
import com.marglabs.trackmyclass.attendance.dao.AttendanceDao;
import com.marglabs.trackmyclass.attendance.entity.AttendanceEntity;
import com.marglabs.trackmyclass.attendance.mapper.AttendanceMapper;
import com.marglabs.trackmyclass.attendance.model.*;
import com.marglabs.trackmyclass.attendance.model.ClassroomAttendanceStatistics.StudentAttendanceSummary;
import com.marglabs.trackmyclass.attendance.model.ClassroomAttendanceStatistics.DailyAttendanceRate;
import com.marglabs.trackmyclass.classroom.service.ClassroomService;

import com.marglabs.trackmyclass.user.service.UserService;
import com.marglabs.trackmyclass.user.model.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Transactional
public class AttendanceService {

    private static final Logger logger = LoggerFactory.getLogger(AttendanceService.class);

    @Autowired
    private AttendanceDao attendanceDao;

    @Autowired
    private AttendanceMapper mapper;

    @Autowired
    private IdGenerator idGenerator;

    @Autowired
    private ClassroomService classroomService;



    @Autowired
    private UserService userService;

    /**
     * Record attendance for a single student
     */
    public Attendance recordAttendance(AttendanceCreationRequest request, String classroomId, String teacherId) {
        // Validate the request
        validateAttendanceRequest(request, classroomId, teacherId);

        // Check if attendance already exists
        var existingAttendance = attendanceDao.findExistingAttendance(
                classroomId, request.getStudentId(), request.getAttendanceDate());

        if (existingAttendance.isPresent()) {
            throw new GeneralException(HttpStatus.CONFLICT, "ATTENDANCE", "ATTENDANCE_ALREADY_EXISTS",
                    "attendance_already_recorded", request.getStudentId(), request.getAttendanceDate());
        }

        // Create attendance entity
        AttendanceEntity attendanceEntity = new AttendanceEntity()
                .setId(idGenerator.generateId())
                .setClassroomId(classroomId)
                .setStudentId(request.getStudentId())
                .setTeacherId(teacherId)
                .setAttendanceDate(request.getAttendanceDate())
                .setStatus(request.getStatus())
                .setNotes(request.getNotes())
                .setMarkedBy(teacherId);

        // Save attendance
        attendanceEntity = attendanceDao.create(attendanceEntity);

        return enrichAttendanceWithDetails(mapper.toDto(attendanceEntity));
    }

    /**
     * Record attendance for multiple students at once
     */
    public List<Attendance> recordBulkAttendance(BulkAttendanceRequest request, String classroomId, String teacherId) {
        // Validate classroom and teacher
        var classroom = classroomService.getClassroomById(classroomId);
        if (!classroom.getTeacherId().equals(teacherId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "ATTENDANCE", "UNAUTHORIZED_CLASSROOM_ACCESS",
                    "teacher_not_authorized_for_classroom", classroomId);
        }

        // Validate date
        validateDate(request.getAttendanceDate());

        List<Attendance> recordedAttendances = new ArrayList<>();

        for (var studentRecord : request.getStudentAttendances()) {
            try {
                // Check if user exists
                userService.getUserById(studentRecord.getStudentId());

                // Check if attendance already exists
                var existingAttendance = attendanceDao.findExistingAttendance(
                        classroomId, studentRecord.getStudentId(), request.getAttendanceDate());

                if (existingAttendance.isPresent()) {
                    // Update existing attendance
                    var existing = existingAttendance.get();
                    existing.setStatus(studentRecord.getStatus())
                            .setNotes(studentRecord.getNotes())
                            .setMarkedBy(teacherId);

                    existing = attendanceDao.update(existing);
                    recordedAttendances.add(enrichAttendanceWithDetails(mapper.toDto(existing)));
                } else {
                    // Create new attendance
                    AttendanceEntity attendanceEntity = new AttendanceEntity()
                            .setId(idGenerator.generateId())
                            .setClassroomId(classroomId)
                            .setStudentId(studentRecord.getStudentId())
                            .setTeacherId(teacherId)
                            .setAttendanceDate(request.getAttendanceDate())
                            .setStatus(studentRecord.getStatus())
                            .setNotes(studentRecord.getNotes())
                            .setMarkedBy(teacherId);

                    attendanceEntity = attendanceDao.create(attendanceEntity);
                    recordedAttendances.add(enrichAttendanceWithDetails(mapper.toDto(attendanceEntity)));
                }
            } catch (Exception e) {
                logger.warn("Failed to record attendance for student {}: {}",
                        studentRecord.getStudentId(), e.getMessage());
                // Continue with other students
            }
        }

        return recordedAttendances;
    }

    /**
     * Update existing attendance
     */
    public Attendance updateAttendance(String attendanceId, AttendanceUpdateRequest request, String teacherId) {
        AttendanceEntity existingAttendance = attendanceDao.getById(attendanceId);

        // Verify teacher has access
        if (!existingAttendance.getTeacherId().equals(teacherId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "ATTENDANCE", "UNAUTHORIZED_ATTENDANCE_ACCESS",
                    "teacher_not_authorized_for_attendance", attendanceId);
        }

        // Validate attendance date if provided
        if (request.getAttendanceDate() != null) {
            validateDate(request.getAttendanceDate());

            // Check if changing date would create a duplicate
            if (!request.getAttendanceDate().equals(existingAttendance.getAttendanceDate())) {
                var duplicateCheck = attendanceDao.findExistingAttendance(
                        existingAttendance.getClassroomId(),
                        existingAttendance.getStudentId(),
                        request.getAttendanceDate());

                if (duplicateCheck.isPresent() && !duplicateCheck.get().getId().equals(attendanceId)) {
                    throw new GeneralException(HttpStatus.CONFLICT, "ATTENDANCE", "ATTENDANCE_ALREADY_EXISTS",
                            "attendance_already_recorded", existingAttendance.getStudentId(), request.getAttendanceDate());
                }
            }

            existingAttendance.setAttendanceDate(request.getAttendanceDate());
        }

        // Update other fields
        if (request.getStatus() != null) {
            existingAttendance.setStatus(request.getStatus());
        }
        if (request.getNotes() != null) {
            existingAttendance.setNotes(request.getNotes());
        }

        existingAttendance.setMarkedBy(teacherId);

        AttendanceEntity updatedEntity = attendanceDao.update(existingAttendance);
        return enrichAttendanceWithDetails(mapper.toDto(updatedEntity));
    }

    /**
     * Get attendance by ID
     */
    public Attendance getAttendanceById(String id, String teacherId) {
        AttendanceEntity entity = attendanceDao.getById(id);

        // Verify teacher has access
        if (!entity.getTeacherId().equals(teacherId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "ATTENDANCE", "UNAUTHORIZED_ATTENDANCE_ACCESS",
                    "teacher_not_authorized_for_attendance", id);
        }

        return enrichAttendanceWithDetails(mapper.toDto(entity));
    }

    /**
     * Get attendance for a classroom on a specific date
     */
    public List<Attendance> getAttendanceByClassroomAndDate(String classroomId, String attendanceDate, String teacherId) {
        // Verify teacher owns the classroom
        var classroom = classroomService.getClassroomById(classroomId);
        if (!classroom.getTeacherId().equals(teacherId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "ATTENDANCE", "UNAUTHORIZED_CLASSROOM_ACCESS",
                    "teacher_not_authorized_for_classroom", classroomId);
        }

        List<AttendanceEntity> entities = attendanceDao.getByClassroomAndDate(classroomId, attendanceDate);
        return entities.stream()
                .map(entity -> enrichAttendanceWithDetails(mapper.toDto(entity)))
                .collect(Collectors.toList());
    }

    /**
     * Get attendance for a classroom within a date range
     */
    public List<Attendance> getAttendanceByClassroomAndDateRange(String classroomId, String startDate,
                                                                 String endDate, String teacherId) {
        // Verify teacher owns the classroom
        var classroom = classroomService.getClassroomById(classroomId);
        if (!classroom.getTeacherId().equals(teacherId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "ATTENDANCE", "UNAUTHORIZED_CLASSROOM_ACCESS",
                    "teacher_not_authorized_for_classroom", classroomId);
        }

        List<AttendanceEntity> entities = attendanceDao.getByClassroomAndDateRange(classroomId, startDate, endDate);
        return entities.stream()
                .map(entity -> enrichAttendanceWithDetails(mapper.toDto(entity)))
                .collect(Collectors.toList());
    }

    /**
     * Get attendance for a student within a date range
     */
    public List<Attendance> getAttendanceByStudentAndDateRange(String studentId, String startDate,
                                                               String endDate, String teacherId) {
        // Verify user exists
        userService.getUserById(studentId);

        List<AttendanceEntity> entities = attendanceDao.getByStudentAndDateRange(studentId, startDate, endDate);
        return entities.stream()
                .map(entity -> enrichAttendanceWithDetails(mapper.toDto(entity)))
                .collect(Collectors.toList());
    }

    /**
     * Get attendance statistics for a classroom (all records)
     */
    public AttendanceStatistics getAttendanceStatistics(String classroomId, String teacherId) {
        // Verify teacher owns the classroom
        var classroom = classroomService.getClassroomById(classroomId);
        if (!classroom.getTeacherId().equals(teacherId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "ATTENDANCE", "UNAUTHORIZED_CLASSROOM_ACCESS",
                    "teacher_not_authorized_for_classroom", classroomId);
        }

        // Get all attendance records for the classroom
        List<AttendanceEntity> allAttendance = attendanceDao.getByClassroom(classroomId);

        Map<AttendanceStatus, Long> statusCounts = new HashMap<>();
        long totalRecords = allAttendance.size();

        // Count attendance by status
        for (AttendanceEntity attendance : allAttendance) {
            AttendanceStatus status = attendance.getStatus();
            statusCounts.put(status, statusCounts.getOrDefault(status, 0L) + 1);
        }

        // Get date range from actual data
        String startDate = null;
        String endDate = null;
        if (!allAttendance.isEmpty()) {
            startDate = allAttendance.stream()
                    .map(AttendanceEntity::getAttendanceDate)
                    .min(String::compareTo)
                    .orElse(null);
            endDate = allAttendance.stream()
                    .map(AttendanceEntity::getAttendanceDate)
                    .max(String::compareTo)
                    .orElse(null);
        }

        return new AttendanceStatistics()
                .setClassroomId(classroomId)
                .setStartDate(startDate)
                .setEndDate(endDate)
                .setTotalRecords(totalRecords)
                .setPresentCount(statusCounts.getOrDefault(AttendanceStatus.PRESENT, 0L))
                .setAbsentCount(statusCounts.getOrDefault(AttendanceStatus.ABSENT, 0L))
                .setLateCount(statusCounts.getOrDefault(AttendanceStatus.LATE, 0L))
                .setExcusedCount(statusCounts.getOrDefault(AttendanceStatus.EXCUSED, 0L))
                .setPartialCount(statusCounts.getOrDefault(AttendanceStatus.PARTIAL, 0L));
    }

    /**
     * Get attendance statistics for a classroom within a date range
     */
    public AttendanceStatistics getAttendanceStatisticsByDateRange(String classroomId, String startDate,
                                                                   String endDate, String teacherId) {
        // Verify teacher owns the classroom
        var classroom = classroomService.getClassroomById(classroomId);
        if (!classroom.getTeacherId().equals(teacherId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "ATTENDANCE", "UNAUTHORIZED_CLASSROOM_ACCESS",
                    "teacher_not_authorized_for_classroom", classroomId);
        }

        List<Object[]> stats = attendanceDao.getAttendanceStatsByClassroom(classroomId, startDate, endDate);

        Map<AttendanceStatus, Long> statusCounts = new HashMap<>();
        long totalRecords = 0;

        for (Object[] stat : stats) {
            AttendanceStatus status = (AttendanceStatus) stat[0];
            Long count = (Long) stat[1];
            statusCounts.put(status, count);
            totalRecords += count;
        }

        return new AttendanceStatistics()
                .setClassroomId(classroomId)
                .setStartDate(startDate)
                .setEndDate(endDate)
                .setTotalRecords(totalRecords)
                .setPresentCount(statusCounts.getOrDefault(AttendanceStatus.PRESENT, 0L))
                .setAbsentCount(statusCounts.getOrDefault(AttendanceStatus.ABSENT, 0L))
                .setLateCount(statusCounts.getOrDefault(AttendanceStatus.LATE, 0L))
                .setExcusedCount(statusCounts.getOrDefault(AttendanceStatus.EXCUSED, 0L))
                .setPartialCount(statusCounts.getOrDefault(AttendanceStatus.PARTIAL, 0L));
    }

    /**
     * Get comprehensive classroom attendance statistics
     */
    public ClassroomAttendanceStatistics getComprehensiveAttendanceStatistics(String classroomId, String startDate,
                                                                              String endDate, String teacherId) {
        // Verify teacher owns the classroom
        var classroom = classroomService.getClassroomById(classroomId);
        if (!classroom.getTeacherId().equals(teacherId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "ATTENDANCE", "UNAUTHORIZED_CLASSROOM_ACCESS",
                    "teacher_not_authorized_for_classroom", classroomId);
        }

        // Get distinct attendance dates (total classes)
        List<String> attendanceDates = attendanceDao.getDistinctAttendanceDates(classroomId, startDate, endDate);
        int totalClasses = attendanceDates.size();

        // Get distinct student IDs
        List<String> studentIds = attendanceDao.getDistinctStudentIds(classroomId, startDate, endDate);
        int totalStudents = studentIds.size();

        // Get attendance summary by student
        List<Object[]> studentSummaryData = attendanceDao.getAttendanceSummaryByStudent(classroomId, startDate, endDate);

        // Process student attendance data
        Map<String, StudentAttendanceSummary> studentSummaries = new HashMap<>();

        // Initialize all students
        for (String studentId : studentIds) {
            studentSummaries.put(studentId, new StudentAttendanceSummary()
                    .setStudentId(studentId)
                    .setTotalClasses(totalClasses)
                    .setPresentCount(0)
                    .setAbsentCount(0)
                    .setLateCount(0)
                    .setExcusedCount(0)
                    .setPartialCount(0));
        }

        // Populate attendance counts
        for (Object[] row : studentSummaryData) {
            String studentId = (String) row[0];
            AttendanceStatus status = (AttendanceStatus) row[1];
            Long count = (Long) row[2];

            StudentAttendanceSummary summary = studentSummaries.get(studentId);
            if (summary != null) {
                switch (status) {
                    case PRESENT -> summary.setPresentCount(count.intValue());
                    case ABSENT -> summary.setAbsentCount(count.intValue());
                    case LATE -> summary.setLateCount(count.intValue());
                    case EXCUSED -> summary.setExcusedCount(count.intValue());
                    case PARTIAL -> summary.setPartialCount(count.intValue());
                }
            }
        }

        // Enrich student summaries with names and calculate attendance rates
        List<StudentAttendanceSummary> allStudentSummaries = new ArrayList<>();
        List<StudentAttendanceSummary> poorAttendanceStudents = new ArrayList<>();
        List<StudentAttendanceSummary> excellentAttendanceStudents = new ArrayList<>();

        double totalAttendanceRate = 0.0;
        int studentsBelow75 = 0;
        int studentsAbove95 = 0;

        for (StudentAttendanceSummary summary : studentSummaries.values()) {
            try {
                // Get user details
                var user = userService.getUserById(summary.getStudentId());
                summary.setStudentName(user.getName())
                       .setStudentEmail(user.getEmail());
            } catch (Exception e) {
                logger.warn("Failed to get student details for {}: {}", summary.getStudentId(), e.getMessage());
                summary.setStudentName("Unknown Student")
                       .setStudentEmail("<EMAIL>");
            }

            double attendanceRate = summary.getAttendanceRate();
            totalAttendanceRate += attendanceRate;

            if (attendanceRate < 75.0) {
                studentsBelow75++;
                poorAttendanceStudents.add(summary);
            }

            if (attendanceRate >= 95.0) {
                studentsAbove95++;
                excellentAttendanceStudents.add(summary);
            }

            allStudentSummaries.add(summary);
        }

        // Calculate average attendance rate
        double averageAttendanceRate = totalStudents > 0 ? totalAttendanceRate / totalStudents : 0.0;

        // Get daily attendance rates
        List<DailyAttendanceRate> dailyRates = calculateDailyAttendanceRates(classroomId, startDate, endDate, attendanceDates);

        // Get overall statistics
        List<Object[]> overallStats = attendanceDao.getAttendanceStatsByClassroom(classroomId, startDate, endDate);
        long totalRecords = 0;
        long presentRecords = 0;
        long absentRecords = 0;
        long lateRecords = 0;
        long excusedRecords = 0;
        long partialRecords = 0;

        for (Object[] stat : overallStats) {
            AttendanceStatus status = (AttendanceStatus) stat[0];
            Long count = (Long) stat[1];
            totalRecords += count;

            switch (status) {
                case PRESENT -> presentRecords = count;
                case ABSENT -> absentRecords = count;
                case LATE -> lateRecords = count;
                case EXCUSED -> excusedRecords = count;
                case PARTIAL -> partialRecords = count;
            }
        }

        // Sort students by attendance rate
        poorAttendanceStudents.sort((a, b) -> Double.compare(a.getAttendanceRate(), b.getAttendanceRate()));
        excellentAttendanceStudents.sort((a, b) -> Double.compare(b.getAttendanceRate(), a.getAttendanceRate()));

        return new ClassroomAttendanceStatistics()
                .setClassroomId(classroomId)
                .setClassroomName(classroom.getClassName())
                .setStartDate(startDate)
                .setEndDate(endDate)
                .setTotalClasses(totalClasses)
                .setTotalStudents(totalStudents)
                .setAverageAttendanceRate(averageAttendanceRate)
                .setTotalAttendanceRecords(totalRecords)
                .setTotalPresentRecords(presentRecords)
                .setTotalAbsentRecords(absentRecords)
                .setTotalLateRecords(lateRecords)
                .setTotalExcusedRecords(excusedRecords)
                .setTotalPartialRecords(partialRecords)
                .setStudentsBelow75Percent(studentsBelow75)
                .setPoorAttendanceStudents(poorAttendanceStudents)
                .setStudentsAbove95Percent(studentsAbove95)
                .setExcellentAttendanceStudents(excellentAttendanceStudents)
                .setDailyAttendanceRates(dailyRates);
    }

    private List<DailyAttendanceRate> calculateDailyAttendanceRates(String classroomId, String startDate,
                                                                   String endDate, List<String> attendanceDates) {
        List<Object[]> dailyData = attendanceDao.getAttendanceByDateAndStatus(classroomId, startDate, endDate);

        Map<String, DailyAttendanceRate> dailyRatesMap = new HashMap<>();

        // Initialize all dates
        for (String date : attendanceDates) {
            dailyRatesMap.put(date, new DailyAttendanceRate()
                    .setDate(date)
                    .setTotalStudents(0)
                    .setPresentStudents(0)
                    .setAbsentStudents(0)
                    .setLateStudents(0)
                    .setExcusedStudents(0)
                    .setPartialStudents(0));
        }

        // Populate daily data
        for (Object[] row : dailyData) {
            String date = (String) row[0];
            AttendanceStatus status = (AttendanceStatus) row[1];
            Long count = (Long) row[2];

            DailyAttendanceRate dailyRate = dailyRatesMap.get(date);
            if (dailyRate != null) {
                dailyRate.setTotalStudents(dailyRate.getTotalStudents() + count.intValue());

                switch (status) {
                    case PRESENT -> dailyRate.setPresentStudents(count.intValue());
                    case ABSENT -> dailyRate.setAbsentStudents(count.intValue());
                    case LATE -> dailyRate.setLateStudents(count.intValue());
                    case EXCUSED -> dailyRate.setExcusedStudents(count.intValue());
                    case PARTIAL -> dailyRate.setPartialStudents(count.intValue());
                }
            }
        }

        return dailyRatesMap.values().stream()
                .sorted((a, b) -> a.getDate().compareTo(b.getDate()))
                .collect(Collectors.toList());
    }

    /**
     * Delete attendance record
     */
    public void deleteAttendance(String attendanceId, String teacherId) {
        AttendanceEntity attendance = attendanceDao.getById(attendanceId);

        // Verify teacher has access
        if (!attendance.getTeacherId().equals(teacherId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "ATTENDANCE", "UNAUTHORIZED_ATTENDANCE_ACCESS",
                    "teacher_not_authorized_for_attendance", attendanceId);
        }

        attendanceDao.deleteById(attendanceId);
        logger.info("Attendance record {} deleted by teacher {}", attendanceId, teacherId);
    }

    // Private helper methods

    private void validateAttendanceRequest(AttendanceCreationRequest request, String classroomId, String teacherId) {
        // Verify classroom exists and teacher owns it
        var classroom = classroomService.getClassroomById(classroomId);
        if (!classroom.getTeacherId().equals(teacherId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "ATTENDANCE", "UNAUTHORIZED_CLASSROOM_ACCESS",
                    "teacher_not_authorized_for_classroom", classroomId);
        }

        // Verify user exists
        userService.getUserById(request.getStudentId());

        // Validate date
        validateDate(request.getAttendanceDate());
    }

    private void validateDate(String dateString) {
        try {
            LocalDate date = LocalDate.parse(dateString, DateTimeFormatter.ISO_LOCAL_DATE);
            LocalDate today = LocalDate.now();

            // Don't allow future dates beyond today
            if (date.isAfter(today)) {
                throw new GeneralException(HttpStatus.BAD_REQUEST, "ATTENDANCE", "INVALID_ATTENDANCE_DATE",
                        "attendance_date_cannot_be_future");
            }

            // Don't allow dates too far in the past (e.g., more than 1 year)
            if (date.isBefore(today.minusYears(1))) {
                throw new GeneralException(HttpStatus.BAD_REQUEST, "ATTENDANCE", "INVALID_ATTENDANCE_DATE",
                        "attendance_date_too_old");
            }
        } catch (DateTimeParseException e) {
            throw new GeneralException(HttpStatus.BAD_REQUEST, "ATTENDANCE", "INVALID_ATTENDANCE_DATE",
                    "invalid_attendance_date_format");
        }
    }

    private Attendance enrichAttendanceWithDetails(Attendance attendance) {
        try {
            // Get user details
            var user = userService.getUserById(attendance.getStudentId());
            attendance.setStudentName(user.getName());
            attendance.setStudentEmail(user.getEmail());

            // Get classroom details
            var classroom = classroomService.getClassroomById(attendance.getClassroomId());
            attendance.setClassroomName(classroom.getClassName());

            // Get teacher details
            var teacher = userService.getUserById(attendance.getTeacherId());
            attendance.setTeacherName(teacher.getName() != null ? teacher.getName() : teacher.getEmail());

            // Get marked by details
            if (attendance.getMarkedBy() != null) {
                var markedByUser = userService.getUserById(attendance.getMarkedBy());
                attendance.setMarkedByName(markedByUser.getName() != null ? markedByUser.getName() : markedByUser.getEmail());
            }

        } catch (Exception e) {
            logger.warn("Failed to enrich attendance with details: {}", e.getMessage());
        }

        return attendance;
    }

    // Inner class for statistics
    public static class AttendanceStatistics {
        private String classroomId;
        private String startDate;
        private String endDate;
        private long totalRecords;
        private long presentCount;
        private long absentCount;
        private long lateCount;
        private long excusedCount;
        private long partialCount;

        // Getters and setters
        public String getClassroomId() { return classroomId; }
        public AttendanceStatistics setClassroomId(String classroomId) { this.classroomId = classroomId; return this; }

        public String getStartDate() { return startDate; }
        public AttendanceStatistics setStartDate(String startDate) { this.startDate = startDate; return this; }

        public String getEndDate() { return endDate; }
        public AttendanceStatistics setEndDate(String endDate) { this.endDate = endDate; return this; }

        public long getTotalRecords() { return totalRecords; }
        public AttendanceStatistics setTotalRecords(long totalRecords) { this.totalRecords = totalRecords; return this; }

        public long getPresentCount() { return presentCount; }
        public AttendanceStatistics setPresentCount(long presentCount) { this.presentCount = presentCount; return this; }

        public long getAbsentCount() { return absentCount; }
        public AttendanceStatistics setAbsentCount(long absentCount) { this.absentCount = absentCount; return this; }

        public long getLateCount() { return lateCount; }
        public AttendanceStatistics setLateCount(long lateCount) { this.lateCount = lateCount; return this; }

        public long getExcusedCount() { return excusedCount; }
        public AttendanceStatistics setExcusedCount(long excusedCount) { this.excusedCount = excusedCount; return this; }

        public long getPartialCount() { return partialCount; }
        public AttendanceStatistics setPartialCount(long partialCount) { this.partialCount = partialCount; return this; }

        // Calculated properties
        public double getAttendanceRate() {
            if (totalRecords == 0) return 0.0;
            return (double) (presentCount + lateCount + partialCount) / totalRecords * 100;
        }

        public double getAbsenceRate() {
            if (totalRecords == 0) return 0.0;
            return (double) (absentCount) / totalRecords * 100;
        }
    }

    /**
     * Get comprehensive attendance report for a user (student)
     */
    public StudentAttendanceReport getStudentAttendanceReport(String userId, String startDate, String endDate, String teacherId) {
        logger.info("Generating comprehensive attendance report for user {} from {} to {}", userId, startDate, endDate);

        // Verify user exists and get user details
        User user = userService.getUserById(userId);

        // Validate dates
        validateDate(startDate);
        validateDate(endDate);

        // Get all attendance records for the user in the date range
        List<AttendanceEntity> allAttendanceRecords = attendanceDao.getByStudentAndDateRange(userId, startDate, endDate);

        // Get user's classroom enrollments to calculate total scheduled classes
        List<String> enrolledClassrooms = getStudentEnrolledClassrooms(userId, teacherId);

        // Calculate overall statistics
        StudentAttendanceReport report = new StudentAttendanceReport()
                .setStudentId(userId)
                .setStudentName(user.getName())
                .setStudentEmail(user.getEmail())
                .setStartDate(startDate)
                .setEndDate(endDate)
                .setReportGeneratedDate(java.time.LocalDate.now().toString());

        // Calculate overall attendance statistics
        calculateOverallStatistics(report, allAttendanceRecords, enrolledClassrooms, startDate, endDate, teacherId);

        // Generate classroom-wise breakdown
        report.setClassroomSummaries(generateClassroomSummaries(userId, enrolledClassrooms, startDate, endDate, teacherId));

        // Generate monthly breakdown
        report.setMonthlyBreakdown(generateMonthlyBreakdown(allAttendanceRecords, startDate, endDate));

        // Get recent attendance records (last 30 days)
        String recentStartDate = java.time.LocalDate.now().minusDays(30).toString();
        String today = java.time.LocalDate.now().toString();
        List<AttendanceEntity> recentRecords = attendanceDao.getByStudentAndDateRange(userId, recentStartDate, today);
        report.setRecentAttendanceRecords(recentRecords.stream()
                .map(entity -> enrichAttendanceWithDetails(mapper.toDto(entity)))
                .collect(Collectors.toList()));

        // Calculate attendance trends
        report.setTrends(calculateAttendanceTrends(allAttendanceRecords, report.getMonthlyBreakdown()));

        // Generate recommendations
        report.setRecommendations(generateRecommendations(report));

        logger.info("Successfully generated attendance report for user {}", userId);
        return report;
    }

    private List<String> getStudentEnrolledClassrooms(String studentId, String teacherId) {
        try {
            // Get user's enrollments through the user service
            var user = userService.getUserById(studentId);
            // For now, get all classrooms where this student has attendance records
            // In a real implementation, you'd get this from an enrollment service
            List<AttendanceEntity> studentAttendance = attendanceDao.getByStudentAndDateRange(studentId, "2020-01-01", java.time.LocalDate.now().toString());
            return studentAttendance.stream()
                    .map(AttendanceEntity::getClassroomId)
                    .distinct()
                    .collect(Collectors.toList());
        } catch (Exception e) {
            logger.warn("Failed to get enrolled classrooms for student {}: {}", studentId, e.getMessage());
            return new ArrayList<>();
        }
    }

    private void calculateOverallStatistics(StudentAttendanceReport report, List<AttendanceEntity> attendanceRecords,
                                           List<String> enrolledClassrooms, String startDate, String endDate, String teacherId) {

        // Count attendance by status
        int presentCount = 0, absentCount = 0, lateCount = 0, excusedCount = 0, partialCount = 0;

        for (AttendanceEntity record : attendanceRecords) {
            switch (record.getStatus()) {
                case PRESENT -> presentCount++;
                case ABSENT -> absentCount++;
                case LATE -> lateCount++;
                case EXCUSED -> excusedCount++;
                case PARTIAL -> partialCount++;
            }
        }

        int totalClassesAttended = presentCount + lateCount + partialCount;
        int totalRecords = attendanceRecords.size();

        // Calculate total scheduled classes (this is an approximation)
        int totalScheduledClasses = calculateTotalScheduledClasses(enrolledClassrooms, startDate, endDate, teacherId);

        double attendanceRate = totalScheduledClasses > 0 ? (double) totalClassesAttended / totalScheduledClasses * 100 : 0.0;

        report.setTotalClassesScheduled(totalScheduledClasses)
              .setTotalClassesAttended(totalClassesAttended)
              .setTotalPresentDays(presentCount)
              .setTotalAbsentDays(absentCount)
              .setTotalLateDays(lateCount)
              .setTotalExcusedDays(excusedCount)
              .setTotalPartialDays(partialCount)
              .setOverallAttendanceRate(attendanceRate)
              .setAttendanceGrade(calculateAttendanceGrade(attendanceRate))
              .setAttendanceAtRisk(attendanceRate < 75.0)
              .setExcellentAttendance(attendanceRate >= 95.0)
              .setConsecutiveAbsentDays(calculateConsecutiveAbsentDays(attendanceRecords))
              .setLastAttendanceDate(getLastAttendanceDate(attendanceRecords));
    }

    private int calculateTotalScheduledClasses(List<String> enrolledClassrooms, String startDate, String endDate, String teacherId) {
        // This is a simplified calculation - in a real implementation, you'd check the schedule
        // For now, we'll estimate based on attendance records and assume some missed classes
        int totalAttendanceRecords = 0;
        for (String classroomId : enrolledClassrooms) {
            List<String> distinctDates = attendanceDao.getDistinctAttendanceDates(classroomId, startDate, endDate);
            totalAttendanceRecords += distinctDates.size();
        }
        return Math.max(totalAttendanceRecords, (int) (totalAttendanceRecords * 1.1)); // Assume 10% more classes were scheduled
    }

    private String calculateAttendanceGrade(double attendanceRate) {
        if (attendanceRate >= 95.0) return "EXCELLENT";
        if (attendanceRate >= 85.0) return "GOOD";
        if (attendanceRate >= 75.0) return "SATISFACTORY";
        if (attendanceRate >= 60.0) return "NEEDS_IMPROVEMENT";
        return "POOR";
    }

    private int calculateConsecutiveAbsentDays(List<AttendanceEntity> attendanceRecords) {
        // Sort by date descending to check recent consecutive absences
        List<AttendanceEntity> sortedRecords = attendanceRecords.stream()
                .sorted((a, b) -> b.getAttendanceDate().compareTo(a.getAttendanceDate()))
                .collect(Collectors.toList());

        int consecutiveAbsent = 0;
        for (AttendanceEntity record : sortedRecords) {
            if (record.getStatus() == AttendanceStatus.ABSENT) {
                consecutiveAbsent++;
            } else {
                break;
            }
        }
        return consecutiveAbsent;
    }

    private String getLastAttendanceDate(List<AttendanceEntity> attendanceRecords) {
        return attendanceRecords.stream()
                .filter(record -> record.getStatus() == AttendanceStatus.PRESENT ||
                                record.getStatus() == AttendanceStatus.LATE ||
                                record.getStatus() == AttendanceStatus.PARTIAL)
                .map(AttendanceEntity::getAttendanceDate)
                .max(String::compareTo)
                .orElse("N/A");
    }

    private List<StudentAttendanceReport.ClassroomAttendanceSummary> generateClassroomSummaries(
            String studentId, List<String> enrolledClassrooms, String startDate, String endDate, String teacherId) {

        List<StudentAttendanceReport.ClassroomAttendanceSummary> summaries = new ArrayList<>();

        for (String classroomId : enrolledClassrooms) {
            try {
                var classroom = classroomService.getClassroomById(classroomId);
                List<AttendanceEntity> classroomAttendance = attendanceDao.getByStudentAndDateRange(studentId, startDate, endDate)
                        .stream()
                        .filter(record -> record.getClassroomId().equals(classroomId))
                        .collect(Collectors.toList());

                int presentCount = 0, absentCount = 0, lateCount = 0, excusedCount = 0, partialCount = 0;

                for (AttendanceEntity record : classroomAttendance) {
                    switch (record.getStatus()) {
                        case PRESENT -> presentCount++;
                        case ABSENT -> absentCount++;
                        case LATE -> lateCount++;
                        case EXCUSED -> excusedCount++;
                        case PARTIAL -> partialCount++;
                    }
                }

                int totalClasses = classroomAttendance.size();
                double attendanceRate = totalClasses > 0 ? (double) (presentCount + lateCount + partialCount) / totalClasses * 100 : 0.0;

                StudentAttendanceReport.ClassroomAttendanceSummary summary = new StudentAttendanceReport.ClassroomAttendanceSummary()
                        .setClassroomId(classroomId)
                        .setClassroomName(classroom.getClassName())
                        .setTeacherName(classroom.getTeacherName())
                        .setTotalClasses(totalClasses)
                        .setPresentCount(presentCount)
                        .setAbsentCount(absentCount)
                        .setLateCount(lateCount)
                        .setExcusedCount(excusedCount)
                        .setPartialCount(partialCount)
                        .setAttendanceRate(attendanceRate)
                        .setAttendanceGrade(calculateAttendanceGrade(attendanceRate));

                summaries.add(summary);
            } catch (Exception e) {
                logger.warn("Failed to generate summary for classroom {}: {}", classroomId, e.getMessage());
            }
        }

        return summaries;
    }

    private List<StudentAttendanceReport.MonthlyAttendanceSummary> generateMonthlyBreakdown(
            List<AttendanceEntity> attendanceRecords, String startDate, String endDate) {

        Map<String, StudentAttendanceReport.MonthlyAttendanceSummary> monthlyMap = new HashMap<>();

        for (AttendanceEntity record : attendanceRecords) {
            String monthKey = record.getAttendanceDate().substring(0, 7); // YYYY-MM

            StudentAttendanceReport.MonthlyAttendanceSummary summary = monthlyMap.computeIfAbsent(monthKey,
                k -> new StudentAttendanceReport.MonthlyAttendanceSummary()
                    .setMonth(k)
                    .setMonthName(formatMonthName(k))
                    .setTotalClasses(0)
                    .setPresentCount(0)
                    .setAbsentCount(0)
                    .setLateCount(0)
                    .setExcusedCount(0)
                    .setPartialCount(0));

            summary.setTotalClasses(summary.getTotalClasses() + 1);

            switch (record.getStatus()) {
                case PRESENT -> summary.setPresentCount(summary.getPresentCount() + 1);
                case ABSENT -> summary.setAbsentCount(summary.getAbsentCount() + 1);
                case LATE -> summary.setLateCount(summary.getLateCount() + 1);
                case EXCUSED -> summary.setExcusedCount(summary.getExcusedCount() + 1);
                case PARTIAL -> summary.setPartialCount(summary.getPartialCount() + 1);
            }
        }

        // Calculate attendance rates and trends
        List<StudentAttendanceReport.MonthlyAttendanceSummary> monthlyList = new ArrayList<>(monthlyMap.values());
        monthlyList.sort((a, b) -> a.getMonth().compareTo(b.getMonth()));

        for (int i = 0; i < monthlyList.size(); i++) {
            StudentAttendanceReport.MonthlyAttendanceSummary summary = monthlyList.get(i);
            int totalClasses = summary.getTotalClasses();
            double attendanceRate = totalClasses > 0 ?
                (double) (summary.getPresentCount() + summary.getLateCount() + summary.getPartialCount()) / totalClasses * 100 : 0.0;
            summary.setAttendanceRate(attendanceRate);

            // Calculate trend
            if (i > 0) {
                double previousRate = monthlyList.get(i - 1).getAttendanceRate();
                if (attendanceRate > previousRate + 5) {
                    summary.setTrend("IMPROVING");
                } else if (attendanceRate < previousRate - 5) {
                    summary.setTrend("DECLINING");
                } else {
                    summary.setTrend("STABLE");
                }
            } else {
                summary.setTrend("STABLE");
            }
        }

        return monthlyList;
    }

    private String formatMonthName(String monthKey) {
        try {
            java.time.YearMonth yearMonth = java.time.YearMonth.parse(monthKey);
            return yearMonth.getMonth().name().substring(0, 1) +
                   yearMonth.getMonth().name().substring(1).toLowerCase() + " " + yearMonth.getYear();
        } catch (Exception e) {
            return monthKey;
        }
    }

    private StudentAttendanceReport.AttendanceTrends calculateAttendanceTrends(
            List<AttendanceEntity> attendanceRecords, List<StudentAttendanceReport.MonthlyAttendanceSummary> monthlyBreakdown) {

        StudentAttendanceReport.AttendanceTrends trends = new StudentAttendanceReport.AttendanceTrends();

        if (monthlyBreakdown.size() < 2) {
            trends.setOverallTrend("STABLE")
                  .setTrendPercentage(0.0)
                  .setBestMonth("N/A")
                  .setWorstMonth("N/A")
                  .setBestMonthRate(0.0)
                  .setWorstMonthRate(0.0)
                  .setFrequentAbsentDays(new ArrayList<>())
                  .setMostCommonAbsentReason("N/A");
            return trends;
        }

        // Calculate overall trend
        double firstMonthRate = monthlyBreakdown.get(0).getAttendanceRate();
        double lastMonthRate = monthlyBreakdown.get(monthlyBreakdown.size() - 1).getAttendanceRate();
        double trendPercentage = lastMonthRate - firstMonthRate;

        String overallTrend;
        if (trendPercentage > 5) {
            overallTrend = "IMPROVING";
        } else if (trendPercentage < -5) {
            overallTrend = "DECLINING";
        } else {
            overallTrend = "STABLE";
        }

        // Find best and worst months
        StudentAttendanceReport.MonthlyAttendanceSummary bestMonth = monthlyBreakdown.stream()
                .max((a, b) -> Double.compare(a.getAttendanceRate(), b.getAttendanceRate()))
                .orElse(monthlyBreakdown.get(0));

        StudentAttendanceReport.MonthlyAttendanceSummary worstMonth = monthlyBreakdown.stream()
                .min((a, b) -> Double.compare(a.getAttendanceRate(), b.getAttendanceRate()))
                .orElse(monthlyBreakdown.get(0));

        // Analyze frequent absent days (simplified)
        Map<String, Long> dayOfWeekAbsences = attendanceRecords.stream()
                .filter(record -> record.getStatus() == AttendanceStatus.ABSENT)
                .collect(Collectors.groupingBy(
                    record -> getDayOfWeek(record.getAttendanceDate()),
                    Collectors.counting()
                ));

        List<String> frequentAbsentDays = dayOfWeekAbsences.entrySet().stream()
                .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
                .limit(3)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        trends.setOverallTrend(overallTrend)
              .setTrendPercentage(trendPercentage)
              .setBestMonth(bestMonth.getMonthName())
              .setWorstMonth(worstMonth.getMonthName())
              .setBestMonthRate(bestMonth.getAttendanceRate())
              .setWorstMonthRate(worstMonth.getAttendanceRate())
              .setFrequentAbsentDays(frequentAbsentDays)
              .setMostCommonAbsentReason("Personal/Health"); // Simplified

        return trends;
    }

    private String getDayOfWeek(String dateString) {
        try {
            java.time.LocalDate date = java.time.LocalDate.parse(dateString);
            return date.getDayOfWeek().name();
        } catch (Exception e) {
            return "UNKNOWN";
        }
    }

    private List<String> generateRecommendations(StudentAttendanceReport report) {
        List<String> recommendations = new ArrayList<>();

        double attendanceRate = report.getOverallAttendanceRate();

        if (attendanceRate >= 95.0) {
            recommendations.add("Excellent attendance! Keep up the great work.");
            recommendations.add("Consider helping peers who may be struggling with attendance.");
        } else if (attendanceRate >= 85.0) {
            recommendations.add("Good attendance overall. Aim for 95% or higher for excellent performance.");
            recommendations.add("Try to minimize late arrivals to maximize learning time.");
        } else if (attendanceRate >= 75.0) {
            recommendations.add("Attendance is satisfactory but has room for improvement.");
            recommendations.add("Focus on consistent daily attendance to avoid falling behind.");
            if (report.getTotalLateDays() > 0) {
                recommendations.add("Work on punctuality - arriving on time is as important as attending.");
            }
        } else if (attendanceRate >= 60.0) {
            recommendations.add("Attendance needs significant improvement. Consider speaking with a counselor.");
            recommendations.add("Develop a daily routine to support consistent school attendance.");
            recommendations.add("Identify and address any barriers preventing regular attendance.");
        } else {
            recommendations.add("Immediate intervention required. Please contact school administration.");
            recommendations.add("Consider developing an attendance improvement plan with school counselors.");
            recommendations.add("Address any underlying issues affecting school attendance.");
        }

        // Specific recommendations based on patterns
        if (report.getConsecutiveAbsentDays() >= 3) {
            recommendations.add("Recent consecutive absences detected. Please catch up on missed work promptly.");
        }

        if (report.getTrends() != null && "DECLINING".equals(report.getTrends().getOverallTrend())) {
            recommendations.add("Attendance trend is declining. Take proactive steps to improve consistency.");
        }

        if (report.getTrends() != null && !report.getTrends().getFrequentAbsentDays().isEmpty()) {
            String frequentDay = report.getTrends().getFrequentAbsentDays().get(0);
            recommendations.add("Most absences occur on " + frequentDay + "s. Focus on attendance on this day.");
        }

        return recommendations;
    }

    /**
     * Get student attendance dashboard - for student's own view
     */
    public StudentAttendanceDashboard getStudentAttendanceDashboard(String studentId, String startDate, String endDate) {
        logger.info("Generating student attendance dashboard for student {} from {} to {}", studentId, startDate, endDate);

        // Validate dates
        validateDate(startDate);
        validateDate(endDate);

        // Get student information (simplified - in real implementation, get from student service)
        // For now, we'll get basic info from attendance records
        List<AttendanceEntity> allAttendanceRecords = attendanceDao.getByStudentAndDateRange(studentId, startDate, endDate);

        if (allAttendanceRecords.isEmpty()) {
            throw new GeneralException(HttpStatus.NOT_FOUND, "ATTENDANCE", "NO_ATTENDANCE_RECORDS",
                    "no_attendance_records_found", studentId);
        }

        // Get student's enrolled classrooms
        List<String> enrolledClassrooms = getStudentEnrolledClassroomsForStudent(studentId);

        // Create dashboard
        StudentAttendanceDashboard dashboard = new StudentAttendanceDashboard()
                .setStudentId(studentId)
                .setStudentName("Student") // In real implementation, get from student service
                .setStudentEmail("<EMAIL>") // In real implementation, get from student service
                .setCurrentDate(java.time.LocalDate.now().toString())
                .setAcademicYear(getCurrentAcademicYear());

        // Calculate overall summary
        dashboard.setOverallSummary(calculateStudentOverallSummary(allAttendanceRecords, enrolledClassrooms, startDate, endDate));

        // Generate class-wise details
        dashboard.setClassAttendanceDetails(generateStudentClassDetails(studentId, enrolledClassrooms, startDate, endDate));

        // Get recent attendance (last 10 records)
        List<AttendanceEntity> recentRecords = allAttendanceRecords.stream()
                .sorted((a, b) -> b.getAttendanceDate().compareTo(a.getAttendanceDate()))
                .limit(10)
                .collect(Collectors.toList());
        dashboard.setRecentAttendance(recentRecords.stream()
                .map(entity -> enrichAttendanceWithDetails(mapper.toDto(entity)))
                .collect(Collectors.toList()));

        // Generate monthly statistics
        dashboard.setMonthlyStatistics(generateStudentMonthlyStats(allAttendanceRecords));

        // Generate attendance calendar for current month
        dashboard.setAttendanceCalendar(generateAttendanceCalendar(studentId, java.time.LocalDate.now().withDayOfMonth(1).toString(),
                java.time.LocalDate.now().toString()));

        // Generate performance insights
        dashboard.setInsights(generateStudentPerformanceInsights(allAttendanceRecords, dashboard.getClassAttendanceDetails()));

        logger.info("Successfully generated student attendance dashboard for student {}", studentId);
        return dashboard;
    }

    private List<String> getStudentEnrolledClassroomsForStudent(String studentId) {
        // Get all classrooms where this student has attendance records
        List<AttendanceEntity> studentAttendance = attendanceDao.getByStudentAndDateRange(studentId, "2020-01-01", java.time.LocalDate.now().toString());
        return studentAttendance.stream()
                .map(AttendanceEntity::getClassroomId)
                .distinct()
                .collect(Collectors.toList());
    }

    private String getCurrentAcademicYear() {
        java.time.LocalDate now = java.time.LocalDate.now();
        int year = now.getYear();
        // Assume academic year starts in September
        if (now.getMonthValue() >= 9) {
            return year + "-" + (year + 1);
        } else {
            return (year - 1) + "-" + year;
        }
    }

    private StudentAttendanceDashboard.AttendanceOverview calculateStudentOverallSummary(
            List<AttendanceEntity> attendanceRecords, List<String> enrolledClassrooms, String startDate, String endDate) {

        // Count attendance by status
        int presentCount = 0, absentCount = 0, lateCount = 0, excusedCount = 0, partialCount = 0;

        for (AttendanceEntity record : attendanceRecords) {
            switch (record.getStatus()) {
                case PRESENT -> presentCount++;
                case ABSENT -> absentCount++;
                case LATE -> lateCount++;
                case EXCUSED -> excusedCount++;
                case PARTIAL -> partialCount++;
            }
        }

        int totalClassesAttended = presentCount + lateCount + partialCount;
        int totalRecords = attendanceRecords.size();

        // Estimate total classes enrolled (simplified)
        int totalClassesEnrolled = Math.max(totalRecords, (int) (totalRecords * 1.1));

        double attendancePercentage = totalClassesEnrolled > 0 ? (double) totalClassesAttended / totalClassesEnrolled * 100 : 0.0;

        String attendanceStatus = calculateStudentAttendanceStatus(attendancePercentage);
        boolean isOnTrack = attendancePercentage >= 75.0; // Minimum requirement
        int daysUntilRisk = calculateDaysUntilRisk(attendancePercentage, totalClassesEnrolled);

        return new StudentAttendanceDashboard.AttendanceOverview()
                .setTotalClassesEnrolled(totalClassesEnrolled)
                .setTotalClassesAttended(totalClassesAttended)
                .setTotalPresent(presentCount)
                .setTotalAbsent(absentCount)
                .setTotalLate(lateCount)
                .setTotalExcused(excusedCount)
                .setTotalPartial(partialCount)
                .setOverallAttendancePercentage(attendancePercentage)
                .setAttendanceStatus(attendanceStatus)
                .setOnTrack(isOnTrack)
                .setDaysUntilRisk(daysUntilRisk);
    }

    private String calculateStudentAttendanceStatus(double attendancePercentage) {
        if (attendancePercentage >= 95.0) return "EXCELLENT";
        if (attendancePercentage >= 85.0) return "GOOD";
        if (attendancePercentage >= 75.0) return "AVERAGE";
        return "POOR";
    }

    private int calculateDaysUntilRisk(double currentPercentage, int totalClasses) {
        if (currentPercentage < 75.0) return 0; // Already at risk

        // Calculate how many more absences until falling below 75%
        double targetPercentage = 75.0;
        int maxAbsences = (int) ((100 - targetPercentage) * totalClasses / 100);
        int currentAbsences = (int) ((100 - currentPercentage) * totalClasses / 100);

        return Math.max(0, maxAbsences - currentAbsences);
    }

    private List<StudentAttendanceDashboard.ClassAttendanceDetail> generateStudentClassDetails(
            String studentId, List<String> enrolledClassrooms, String startDate, String endDate) {

        List<StudentAttendanceDashboard.ClassAttendanceDetail> classDetails = new ArrayList<>();

        for (String classroomId : enrolledClassrooms) {
            try {
                var classroom = classroomService.getClassroomById(classroomId);
                List<AttendanceEntity> classAttendance = attendanceDao.getByStudentAndDateRange(studentId, startDate, endDate)
                        .stream()
                        .filter(record -> record.getClassroomId().equals(classroomId))
                        .collect(Collectors.toList());

                int presentCount = 0, absentCount = 0, lateCount = 0, excusedCount = 0, partialCount = 0;

                for (AttendanceEntity record : classAttendance) {
                    switch (record.getStatus()) {
                        case PRESENT -> presentCount++;
                        case ABSENT -> absentCount++;
                        case LATE -> lateCount++;
                        case EXCUSED -> excusedCount++;
                        case PARTIAL -> partialCount++;
                    }
                }

                int totalClassesHeld = classAttendance.size();
                int classesAttended = presentCount + lateCount + partialCount;
                double attendancePercentage = totalClassesHeld > 0 ? (double) classesAttended / totalClassesHeld * 100 : 0.0;

                String lastAttendedDate = classAttendance.stream()
                        .filter(record -> record.getStatus() == AttendanceStatus.PRESENT ||
                                        record.getStatus() == AttendanceStatus.LATE ||
                                        record.getStatus() == AttendanceStatus.PARTIAL)
                        .map(AttendanceEntity::getAttendanceDate)
                        .max(String::compareTo)
                        .orElse("N/A");

                int consecutiveAbsences = calculateConsecutiveAbsencesForClass(classAttendance);
                String trendDirection = calculateClassTrend(classAttendance);

                StudentAttendanceDashboard.ClassAttendanceDetail detail = new StudentAttendanceDashboard.ClassAttendanceDetail()
                        .setClassroomId(classroomId)
                        .setClassName(classroom.getClassName())
                        .setSubjectName(classroom.getSubjectName())
                        .setTeacherName(classroom.getTeacherName())
                        .setClassCode(classroom.getJoinCode())
                        .setTotalClassesHeld(totalClassesHeld)
                        .setClassesAttended(classesAttended)
                        .setPresentCount(presentCount)
                        .setAbsentCount(absentCount)
                        .setLateCount(lateCount)
                        .setExcusedCount(excusedCount)
                        .setPartialCount(partialCount)
                        .setAttendancePercentage(attendancePercentage)
                        .setClassAttendanceGrade(calculateStudentAttendanceStatus(attendancePercentage))
                        .setNextClassDate("TBD") // In real implementation, get from schedule
                        .setNextClassTime("TBD") // In real implementation, get from schedule
                        .setLastAttendedDate(lastAttendedDate)
                        .setMeetingRequirement(attendancePercentage >= 75.0)
                        .setConsecutiveAbsences(consecutiveAbsences)
                        .setTrendDirection(trendDirection);

                classDetails.add(detail);
            } catch (Exception e) {
                logger.warn("Failed to generate class detail for classroom {}: {}", classroomId, e.getMessage());
            }
        }

        return classDetails;
    }

    private int calculateConsecutiveAbsencesForClass(List<AttendanceEntity> classAttendance) {
        List<AttendanceEntity> sortedRecords = classAttendance.stream()
                .sorted((a, b) -> b.getAttendanceDate().compareTo(a.getAttendanceDate()))
                .collect(Collectors.toList());

        int consecutiveAbsent = 0;
        for (AttendanceEntity record : sortedRecords) {
            if (record.getStatus() == AttendanceStatus.ABSENT) {
                consecutiveAbsent++;
            } else {
                break;
            }
        }
        return consecutiveAbsent;
    }

    private String calculateClassTrend(List<AttendanceEntity> classAttendance) {
        if (classAttendance.size() < 4) return "STABLE";

        // Compare recent vs older attendance
        List<AttendanceEntity> sortedRecords = classAttendance.stream()
                .sorted((a, b) -> a.getAttendanceDate().compareTo(b.getAttendanceDate()))
                .collect(Collectors.toList());

        int halfSize = sortedRecords.size() / 2;
        List<AttendanceEntity> olderHalf = sortedRecords.subList(0, halfSize);
        List<AttendanceEntity> recentHalf = sortedRecords.subList(halfSize, sortedRecords.size());

        double olderRate = calculateAttendanceRateForRecords(olderHalf);
        double recentRate = calculateAttendanceRateForRecords(recentHalf);

        double difference = recentRate - olderRate;

        if (difference > 10) return "IMPROVING";
        if (difference < -10) return "DECLINING";
        return "STABLE";
    }

    private double calculateAttendanceRateForRecords(List<AttendanceEntity> records) {
        if (records.isEmpty()) return 0.0;

        long attendedCount = records.stream()
                .filter(record -> record.getStatus() == AttendanceStatus.PRESENT ||
                                record.getStatus() == AttendanceStatus.LATE ||
                                record.getStatus() == AttendanceStatus.PARTIAL)
                .count();

        return (double) attendedCount / records.size() * 100;
    }

    private List<StudentAttendanceDashboard.MonthlyStats> generateStudentMonthlyStats(List<AttendanceEntity> attendanceRecords) {
        Map<String, StudentAttendanceDashboard.MonthlyStats> monthlyMap = new HashMap<>();

        for (AttendanceEntity record : attendanceRecords) {
            String monthKey = record.getAttendanceDate().substring(0, 7); // YYYY-MM

            StudentAttendanceDashboard.MonthlyStats stats = monthlyMap.computeIfAbsent(monthKey,
                k -> new StudentAttendanceDashboard.MonthlyStats()
                    .setMonth(k)
                    .setMonthName(formatMonthName(k))
                    .setTotalClasses(0)
                    .setAttended(0)
                    .setMissed(0));

            stats.setTotalClasses(stats.getTotalClasses() + 1);

            if (record.getStatus() == AttendanceStatus.PRESENT ||
                record.getStatus() == AttendanceStatus.LATE ||
                record.getStatus() == AttendanceStatus.PARTIAL) {
                stats.setAttended(stats.getAttended() + 1);
            } else {
                stats.setMissed(stats.getMissed() + 1);
            }
        }

        // Calculate rates and performance
        List<StudentAttendanceDashboard.MonthlyStats> monthlyList = new ArrayList<>(monthlyMap.values());
        monthlyList.sort((a, b) -> a.getMonth().compareTo(b.getMonth()));

        for (StudentAttendanceDashboard.MonthlyStats stats : monthlyList) {
            double attendanceRate = stats.getTotalClasses() > 0 ?
                (double) stats.getAttended() / stats.getTotalClasses() * 100 : 0.0;
            stats.setAttendanceRate(attendanceRate);
            stats.setPerformance(calculateStudentAttendanceStatus(attendanceRate));
        }

        return monthlyList;
    }

    private List<StudentAttendanceDashboard.AttendanceCalendarDay> generateAttendanceCalendar(
            String studentId, String startDate, String endDate) {

        List<StudentAttendanceDashboard.AttendanceCalendarDay> calendar = new ArrayList<>();
        List<AttendanceEntity> attendanceRecords = attendanceDao.getByStudentAndDateRange(studentId, startDate, endDate);

        // Group by date
        Map<String, List<AttendanceEntity>> recordsByDate = attendanceRecords.stream()
                .collect(Collectors.groupingBy(AttendanceEntity::getAttendanceDate));

        // Generate calendar days
        java.time.LocalDate start = java.time.LocalDate.parse(startDate);
        java.time.LocalDate end = java.time.LocalDate.parse(endDate);

        for (java.time.LocalDate date = start; !date.isAfter(end); date = date.plusDays(1)) {
            String dateStr = date.toString();
            List<AttendanceEntity> dayRecords = recordsByDate.getOrDefault(dateStr, new ArrayList<>());

            List<StudentAttendanceDashboard.ClassDayStatus> classStatuses = new ArrayList<>();
            for (AttendanceEntity record : dayRecords) {
                try {
                    var classroom = classroomService.getClassroomById(record.getClassroomId());
                    StudentAttendanceDashboard.ClassDayStatus status = new StudentAttendanceDashboard.ClassDayStatus()
                            .setClassroomId(record.getClassroomId())
                            .setClassName(classroom.getClassName())
                            .setStatus(record.getStatus().toString())
                            .setTime("TBD") // In real implementation, get from schedule
                            .setRemarks(record.getNotes());
                    classStatuses.add(status);
                } catch (Exception e) {
                    logger.warn("Failed to get classroom info for calendar day: {}", e.getMessage());
                }
            }

            String overallDayStatus = calculateOverallDayStatus(classStatuses);

            StudentAttendanceDashboard.AttendanceCalendarDay calendarDay = new StudentAttendanceDashboard.AttendanceCalendarDay()
                    .setDate(dateStr)
                    .setDayOfWeek(date.getDayOfWeek().toString())
                    .setClassStatuses(classStatuses)
                    .setOverallDayStatus(overallDayStatus);

            calendar.add(calendarDay);
        }

        return calendar;
    }

    private String calculateOverallDayStatus(List<StudentAttendanceDashboard.ClassDayStatus> classStatuses) {
        if (classStatuses.isEmpty()) return "NO_CLASSES";

        boolean hasPresent = classStatuses.stream().anyMatch(status ->
            "PRESENT".equals(status.getStatus()) || "LATE".equals(status.getStatus()) || "PARTIAL".equals(status.getStatus()));
        boolean hasAbsent = classStatuses.stream().anyMatch(status -> "ABSENT".equals(status.getStatus()));

        if (hasPresent && !hasAbsent) return "FULL_ATTENDANCE";
        if (hasPresent && hasAbsent) return "PARTIAL_ATTENDANCE";
        if (hasAbsent && !hasPresent) return "NO_ATTENDANCE";

        return "NO_CLASSES";
    }

    private StudentAttendanceDashboard.StudentPerformanceInsights generateStudentPerformanceInsights(
            List<AttendanceEntity> attendanceRecords, List<StudentAttendanceDashboard.ClassAttendanceDetail> classDetails) {

        // Calculate overall trend
        String overallTrend = "STABLE";
        double trendPercentage = 0.0;

        if (attendanceRecords.size() >= 8) {
            List<AttendanceEntity> sortedRecords = attendanceRecords.stream()
                    .sorted((a, b) -> a.getAttendanceDate().compareTo(b.getAttendanceDate()))
                    .collect(Collectors.toList());

            int halfSize = sortedRecords.size() / 2;
            List<AttendanceEntity> olderHalf = sortedRecords.subList(0, halfSize);
            List<AttendanceEntity> recentHalf = sortedRecords.subList(halfSize, sortedRecords.size());

            double olderRate = calculateAttendanceRateForRecords(olderHalf);
            double recentRate = calculateAttendanceRateForRecords(recentHalf);

            trendPercentage = recentRate - olderRate;

            if (trendPercentage > 5) overallTrend = "IMPROVING";
            else if (trendPercentage < -5) overallTrend = "DECLINING";
        }

        // Find best and worst performing classes
        String bestPerformingClass = classDetails.stream()
                .max((a, b) -> Double.compare(a.getAttendancePercentage(), b.getAttendancePercentage()))
                .map(StudentAttendanceDashboard.ClassAttendanceDetail::getClassName)
                .orElse("N/A");

        String needsAttentionClass = classDetails.stream()
                .filter(detail -> detail.getAttendancePercentage() < 80.0)
                .min((a, b) -> Double.compare(a.getAttendancePercentage(), b.getAttendancePercentage()))
                .map(StudentAttendanceDashboard.ClassAttendanceDetail::getClassName)
                .orElse("None");

        // Generate strengths and improvement areas
        List<String> strengths = new ArrayList<>();
        List<String> improvementAreas = new ArrayList<>();

        long excellentClasses = classDetails.stream()
                .filter(detail -> detail.getAttendancePercentage() >= 95.0)
                .count();

        if (excellentClasses > 0) {
            strengths.add("Excellent attendance in " + excellentClasses + " class(es)");
        }

        long punctualClasses = classDetails.stream()
                .filter(detail -> detail.getLateCount() <= 2)
                .count();

        if (punctualClasses == classDetails.size() && !classDetails.isEmpty()) {
            strengths.add("Consistently punctual across all classes");
        }

        long poorClasses = classDetails.stream()
                .filter(detail -> detail.getAttendancePercentage() < 75.0)
                .count();

        if (poorClasses > 0) {
            improvementAreas.add("Attendance below 75% in " + poorClasses + " class(es)");
        }

        long lateClasses = classDetails.stream()
                .filter(detail -> detail.getLateCount() > 5)
                .count();

        if (lateClasses > 0) {
            improvementAreas.add("Frequent tardiness in " + lateClasses + " class(es)");
        }

        // Calculate perfect attendance days
        Map<String, List<AttendanceEntity>> recordsByDate = attendanceRecords.stream()
                .collect(Collectors.groupingBy(AttendanceEntity::getAttendanceDate));

        int perfectAttendanceDays = (int) recordsByDate.entrySet().stream()
                .filter(entry -> entry.getValue().stream()
                        .allMatch(record -> record.getStatus() == AttendanceStatus.PRESENT))
                .count();

        // Calculate longest attendance streak
        String longestStreak = calculateLongestAttendanceStreak(attendanceRecords);

        // Generate achievements
        List<String> achievements = new ArrayList<>();
        if (perfectAttendanceDays >= 30) {
            achievements.add("🏆 Perfect Attendance Champion (30+ days)");
        } else if (perfectAttendanceDays >= 15) {
            achievements.add("⭐ Perfect Attendance Star (15+ days)");
        }

        double overallRate = classDetails.stream()
                .mapToDouble(StudentAttendanceDashboard.ClassAttendanceDetail::getAttendancePercentage)
                .average().orElse(0.0);

        if (overallRate >= 95.0) {
            achievements.add("🎯 Attendance Excellence Award");
        }

        return new StudentAttendanceDashboard.StudentPerformanceInsights()
                .setOverallTrend(overallTrend)
                .setTrendPercentage(trendPercentage)
                .setBestPerformingClass(bestPerformingClass)
                .setNeedsAttentionClass(needsAttentionClass)
                .setStrengths(strengths)
                .setImprovementAreas(improvementAreas)
                .setUpcomingClasses(List.of("Math - Tomorrow 9:00 AM", "Science - Tomorrow 2:00 PM")) // Simplified
                .setPerfectAttendanceDays(perfectAttendanceDays)
                .setLongestAttendanceStreak(longestStreak)
                .setAchievements(achievements);
    }

    private String calculateLongestAttendanceStreak(List<AttendanceEntity> attendanceRecords) {
        if (attendanceRecords.isEmpty()) return "0 days";

        // Group by date and check for consecutive present days
        Map<String, Boolean> attendanceByDate = attendanceRecords.stream()
                .collect(Collectors.groupingBy(
                    AttendanceEntity::getAttendanceDate,
                    Collectors.collectingAndThen(
                        Collectors.toList(),
                        list -> list.stream().anyMatch(record ->
                            record.getStatus() == AttendanceStatus.PRESENT ||
                            record.getStatus() == AttendanceStatus.LATE ||
                            record.getStatus() == AttendanceStatus.PARTIAL)
                    )
                ));

        List<String> sortedDates = attendanceByDate.keySet().stream()
                .sorted()
                .collect(Collectors.toList());

        int maxStreak = 0;
        int currentStreak = 0;

        for (String date : sortedDates) {
            if (attendanceByDate.get(date)) {
                currentStreak++;
                maxStreak = Math.max(maxStreak, currentStreak);
            } else {
                currentStreak = 0;
            }
        }

        return maxStreak + " days";
    }

    /**
     * Get detailed attendance information for a specific class from student's perspective
     */
    public StudentClassAttendanceDetail getStudentClassAttendanceDetail(String studentId, String classroomId, String startDate, String endDate) {
        logger.info("Generating class attendance detail for student {} in classroom {} from {} to {}", studentId, classroomId, startDate, endDate);

        // Validate dates
        validateDate(startDate);
        validateDate(endDate);

        // Get classroom information
        var classroom = classroomService.getClassroomById(classroomId);

        // Get attendance records for this student in this class
        List<AttendanceEntity> classAttendanceRecords = attendanceDao.getByStudentAndDateRange(studentId, startDate, endDate)
                .stream()
                .filter(record -> record.getClassroomId().equals(classroomId))
                .collect(Collectors.toList());

        if (classAttendanceRecords.isEmpty()) {
            throw new GeneralException(HttpStatus.NOT_FOUND, "ATTENDANCE", "NO_CLASS_ATTENDANCE_RECORDS",
                    "no_attendance_records_found_for_class", classroomId);
        }

        // Create class attendance detail
        StudentClassAttendanceDetail detail = new StudentClassAttendanceDetail()
                .setClassroomId(classroomId)
                .setClassName(classroom.getClassName())
                .setSubjectName(classroom.getSubjectName())
                .setTeacherName(classroom.getTeacherName())
                .setClassCode(classroom.getJoinCode())
                .setDescription(classroom.getDescription())
                .setStudentId(studentId)
                .setStudentName("Student") // In real implementation, get from student service
                .setStartDate(startDate)
                .setEndDate(endDate)
                .setReportGeneratedDate(java.time.LocalDate.now().toString());

        // Calculate class statistics
        detail.setStats(calculateClassAttendanceStats(classAttendanceRecords));

        // Get detailed attendance records
        detail.setAttendanceRecords(classAttendanceRecords.stream()
                .map(entity -> enrichAttendanceWithDetails(mapper.toDto(entity)))
                .sorted((a, b) -> b.getAttendanceDate().compareTo(a.getAttendanceDate()))
                .collect(Collectors.toList()));

        // Generate monthly breakdown for this class
        detail.setMonthlyBreakdown(generateClassMonthlyBreakdown(classAttendanceRecords));

        // Generate performance insights for this class
        detail.setInsights(generateClassPerformanceInsights(classAttendanceRecords, detail.getMonthlyBreakdown()));

        // Generate schedule information (simplified)
        detail.setScheduleInfo(generateClassScheduleInfo(classroomId, startDate, endDate));

        logger.info("Successfully generated class attendance detail for student {} in classroom {}", studentId, classroomId);
        return detail;
    }

    private StudentClassAttendanceDetail.ClassAttendanceStats calculateClassAttendanceStats(List<AttendanceEntity> classAttendanceRecords) {
        int presentCount = 0, absentCount = 0, lateCount = 0, excusedCount = 0, partialCount = 0;

        for (AttendanceEntity record : classAttendanceRecords) {
            switch (record.getStatus()) {
                case PRESENT -> presentCount++;
                case ABSENT -> absentCount++;
                case LATE -> lateCount++;
                case EXCUSED -> excusedCount++;
                case PARTIAL -> partialCount++;
            }
        }

        int totalClassesHeld = classAttendanceRecords.size();
        int totalClassesAttended = presentCount + lateCount + partialCount;
        double attendancePercentage = totalClassesHeld > 0 ? (double) totalClassesAttended / totalClassesHeld * 100 : 0.0;

        String lastAttendedDate = classAttendanceRecords.stream()
                .filter(record -> record.getStatus() == AttendanceStatus.PRESENT ||
                                record.getStatus() == AttendanceStatus.LATE ||
                                record.getStatus() == AttendanceStatus.PARTIAL)
                .map(AttendanceEntity::getAttendanceDate)
                .max(String::compareTo)
                .orElse("N/A");

        String firstAttendedDate = classAttendanceRecords.stream()
                .filter(record -> record.getStatus() == AttendanceStatus.PRESENT ||
                                record.getStatus() == AttendanceStatus.LATE ||
                                record.getStatus() == AttendanceStatus.PARTIAL)
                .map(AttendanceEntity::getAttendanceDate)
                .min(String::compareTo)
                .orElse("N/A");

        int consecutiveAbsences = calculateConsecutiveAbsencesForClass(classAttendanceRecords);

        return new StudentClassAttendanceDetail.ClassAttendanceStats()
                .setTotalClassesHeld(totalClassesHeld)
                .setTotalClassesAttended(totalClassesAttended)
                .setPresentCount(presentCount)
                .setAbsentCount(absentCount)
                .setLateCount(lateCount)
                .setExcusedCount(excusedCount)
                .setPartialCount(partialCount)
                .setAttendancePercentage(attendancePercentage)
                .setAttendanceGrade(calculateStudentAttendanceStatus(attendancePercentage))
                .setMeetingRequirement(attendancePercentage >= 75.0)
                .setConsecutiveAbsences(consecutiveAbsences)
                .setLastAttendedDate(lastAttendedDate)
                .setFirstAttendedDate(firstAttendedDate);
    }

    private List<StudentClassAttendanceDetail.MonthlyClassStats> generateClassMonthlyBreakdown(List<AttendanceEntity> classAttendanceRecords) {
        Map<String, StudentClassAttendanceDetail.MonthlyClassStats> monthlyMap = new HashMap<>();

        for (AttendanceEntity record : classAttendanceRecords) {
            String monthKey = record.getAttendanceDate().substring(0, 7); // YYYY-MM

            StudentClassAttendanceDetail.MonthlyClassStats stats = monthlyMap.computeIfAbsent(monthKey,
                k -> new StudentClassAttendanceDetail.MonthlyClassStats()
                    .setMonth(k)
                    .setMonthName(formatMonthName(k))
                    .setTotalClasses(0)
                    .setAttended(0)
                    .setMissed(0)
                    .setMissedDates(new ArrayList<>()));

            stats.setTotalClasses(stats.getTotalClasses() + 1);

            if (record.getStatus() == AttendanceStatus.PRESENT ||
                record.getStatus() == AttendanceStatus.LATE ||
                record.getStatus() == AttendanceStatus.PARTIAL) {
                stats.setAttended(stats.getAttended() + 1);
            } else {
                stats.setMissed(stats.getMissed() + 1);
                stats.getMissedDates().add(record.getAttendanceDate());
            }
        }

        // Calculate rates and performance
        List<StudentClassAttendanceDetail.MonthlyClassStats> monthlyList = new ArrayList<>(monthlyMap.values());
        monthlyList.sort((a, b) -> a.getMonth().compareTo(b.getMonth()));

        for (StudentClassAttendanceDetail.MonthlyClassStats stats : monthlyList) {
            double attendanceRate = stats.getTotalClasses() > 0 ?
                (double) stats.getAttended() / stats.getTotalClasses() * 100 : 0.0;
            stats.setAttendanceRate(attendanceRate);
            stats.setPerformance(calculateStudentAttendanceStatus(attendanceRate));
        }

        return monthlyList;
    }

    private StudentClassAttendanceDetail.ClassPerformanceInsights generateClassPerformanceInsights(
            List<AttendanceEntity> classAttendanceRecords, List<StudentClassAttendanceDetail.MonthlyClassStats> monthlyBreakdown) {

        // Calculate overall trend
        String overallTrend = "STABLE";
        double trendPercentage = 0.0;

        if (monthlyBreakdown.size() >= 2) {
            double firstMonthRate = monthlyBreakdown.get(0).getAttendanceRate();
            double lastMonthRate = monthlyBreakdown.get(monthlyBreakdown.size() - 1).getAttendanceRate();
            trendPercentage = lastMonthRate - firstMonthRate;

            if (trendPercentage > 10) overallTrend = "IMPROVING";
            else if (trendPercentage < -10) overallTrend = "DECLINING";
        }

        // Find best and worst months
        String bestMonth = monthlyBreakdown.stream()
                .max((a, b) -> Double.compare(a.getAttendanceRate(), b.getAttendanceRate()))
                .map(StudentClassAttendanceDetail.MonthlyClassStats::getMonthName)
                .orElse("N/A");

        String worstMonth = monthlyBreakdown.stream()
                .min((a, b) -> Double.compare(a.getAttendanceRate(), b.getAttendanceRate()))
                .map(StudentClassAttendanceDetail.MonthlyClassStats::getMonthName)
                .orElse("N/A");

        // Analyze frequent absent days
        Map<String, Long> dayOfWeekAbsences = classAttendanceRecords.stream()
                .filter(record -> record.getStatus() == AttendanceStatus.ABSENT)
                .collect(Collectors.groupingBy(
                    record -> getDayOfWeek(record.getAttendanceDate()),
                    Collectors.counting()
                ));

        List<String> frequentAbsentDays = dayOfWeekAbsences.entrySet().stream()
                .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
                .limit(3)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        // Generate strengths and improvement areas
        List<String> strengths = new ArrayList<>();
        List<String> improvementAreas = new ArrayList<>();

        double overallRate = classAttendanceRecords.stream()
                .mapToDouble(record -> record.getStatus() == AttendanceStatus.PRESENT ||
                                     record.getStatus() == AttendanceStatus.LATE ||
                                     record.getStatus() == AttendanceStatus.PARTIAL ? 1.0 : 0.0)
                .average().orElse(0.0) * 100;

        if (overallRate >= 95.0) {
            strengths.add("Excellent attendance record in this class");
        } else if (overallRate >= 85.0) {
            strengths.add("Good attendance consistency");
        }

        long lateCount = classAttendanceRecords.stream()
                .filter(record -> record.getStatus() == AttendanceStatus.LATE)
                .count();

        if (lateCount <= 2) {
            strengths.add("Consistently punctual");
        } else if (lateCount > 5) {
            improvementAreas.add("Frequent tardiness - work on punctuality");
        }

        if (overallRate < 75.0) {
            improvementAreas.add("Attendance below minimum requirement (75%)");
        }

        // Generate recommendations
        List<String> recommendations = generateClassRecommendations(overallRate, lateCount, frequentAbsentDays);

        // Calculate perfect attendance days
        int perfectAttendanceDays = (int) classAttendanceRecords.stream()
                .filter(record -> record.getStatus() == AttendanceStatus.PRESENT)
                .count();

        // Calculate streaks
        String longestStreak = calculateLongestAttendanceStreakForClass(classAttendanceRecords);
        String currentStreak = calculateCurrentAttendanceStreak(classAttendanceRecords);

        return new StudentClassAttendanceDetail.ClassPerformanceInsights()
                .setOverallTrend(overallTrend)
                .setTrendPercentage(trendPercentage)
                .setBestMonth(bestMonth)
                .setWorstMonth(worstMonth)
                .setFrequentAbsentDays(frequentAbsentDays)
                .setStrengths(strengths)
                .setImprovementAreas(improvementAreas)
                .setRecommendations(recommendations)
                .setPerfectAttendanceDays(perfectAttendanceDays)
                .setLongestAttendanceStreak(longestStreak)
                .setCurrentStreak(currentStreak);
    }

    private List<String> generateClassRecommendations(double overallRate, long lateCount, List<String> frequentAbsentDays) {
        List<String> recommendations = new ArrayList<>();

        if (overallRate >= 95.0) {
            recommendations.add("Outstanding performance! Keep up the excellent work.");
        } else if (overallRate >= 85.0) {
            recommendations.add("Good attendance. Aim for 95% to achieve excellence.");
        } else if (overallRate >= 75.0) {
            recommendations.add("Meeting minimum requirements. Focus on consistency to improve.");
        } else {
            recommendations.add("Attendance below requirements. Immediate improvement needed.");
            recommendations.add("Consider speaking with your teacher about catching up.");
        }

        if (lateCount > 3) {
            recommendations.add("Work on punctuality - plan to arrive 10 minutes early.");
        }

        if (!frequentAbsentDays.isEmpty()) {
            String mostFrequentDay = frequentAbsentDays.get(0);
            recommendations.add("Most absences occur on " + mostFrequentDay + "s. Pay special attention to this day.");
        }

        return recommendations;
    }

    private String calculateLongestAttendanceStreakForClass(List<AttendanceEntity> classAttendanceRecords) {
        if (classAttendanceRecords.isEmpty()) return "0 days";

        List<AttendanceEntity> sortedRecords = classAttendanceRecords.stream()
                .sorted((a, b) -> a.getAttendanceDate().compareTo(b.getAttendanceDate()))
                .collect(Collectors.toList());

        int maxStreak = 0;
        int currentStreak = 0;

        for (AttendanceEntity record : sortedRecords) {
            if (record.getStatus() == AttendanceStatus.PRESENT ||
                record.getStatus() == AttendanceStatus.LATE ||
                record.getStatus() == AttendanceStatus.PARTIAL) {
                currentStreak++;
                maxStreak = Math.max(maxStreak, currentStreak);
            } else {
                currentStreak = 0;
            }
        }

        return maxStreak + " classes";
    }

    private String calculateCurrentAttendanceStreak(List<AttendanceEntity> classAttendanceRecords) {
        if (classAttendanceRecords.isEmpty()) return "0 classes";

        List<AttendanceEntity> sortedRecords = classAttendanceRecords.stream()
                .sorted((a, b) -> b.getAttendanceDate().compareTo(a.getAttendanceDate()))
                .collect(Collectors.toList());

        int currentStreak = 0;
        for (AttendanceEntity record : sortedRecords) {
            if (record.getStatus() == AttendanceStatus.PRESENT ||
                record.getStatus() == AttendanceStatus.LATE ||
                record.getStatus() == AttendanceStatus.PARTIAL) {
                currentStreak++;
            } else {
                break;
            }
        }

        return currentStreak + " classes";
    }

    private StudentClassAttendanceDetail.ClassScheduleInfo generateClassScheduleInfo(String classroomId, String startDate, String endDate) {
        // In a real implementation, this would integrate with the schedule service
        // For now, we'll provide simplified/mock data

        return new StudentClassAttendanceDetail.ClassScheduleInfo()
                .setNextClassDate("TBD")
                .setNextClassTime("TBD")
                .setClassFrequency("Weekly")
                .setClassDays(List.of("Monday", "Wednesday", "Friday"))
                .setClassTime("10:00 AM - 11:30 AM")
                .setClassLocation("Room 101")
                .setTotalScheduledClasses(50) // Simplified
                .setRemainingClasses(10); // Simplified
    }
}
