package com.marglabs.trackmyclass.attendance.rest.controller;

import com.marglabs.trackmyclass.attendance.model.*;
import com.marglabs.trackmyclass.attendance.service.AttendanceService;
import com.marglabs.trackmyclass.user.model.User;
import com.marglabs.trackmyclass.user.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/attendanceManagement/v1")
@Tag(name = "Attendance Management", description = "APIs for managing student attendance")
public class AttendanceController {

    @Autowired
    private AttendanceService attendanceService;

    @Autowired
    private UserService userService;

    @Operation(summary = "Record attendance for a student", description = "Teachers can record attendance for individual students")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Attendance recorded successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request data"),
        @ApiResponse(responseCode = "403", description = "Not authorized to record attendance for this classroom"),
        @ApiResponse(responseCode = "409", description = "Attendance already recorded for this student and session"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @PostMapping(value = "/classrooms/{classroomId}/attendance", produces = "application/json")
    @ResponseStatus(HttpStatus.CREATED)
    public Attendance recordAttendance(
            @Parameter(description = "Classroom ID") @PathVariable String classroomId,
            @Valid @RequestBody AttendanceCreationRequest request,
            @AuthenticationPrincipal Jwt userPrincipal) {

        // Get teacher from Cognito token
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());

        return attendanceService.recordAttendance(request, classroomId, teacher.getId());
    }

    @Operation(summary = "Record bulk attendance", description = "Teachers can record attendance for multiple students at once")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Bulk attendance recorded successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request data"),
        @ApiResponse(responseCode = "403", description = "Not authorized to record attendance for this classroom"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @PostMapping(value = "/classrooms/{classroomId}/attendance/bulk", produces = "application/json")
    @ResponseStatus(HttpStatus.CREATED)
    public List<Attendance> recordBulkAttendance(
            @Parameter(description = "Classroom ID") @PathVariable String classroomId,
            @Valid @RequestBody BulkAttendanceRequest request,
            @AuthenticationPrincipal Jwt userPrincipal) {

        // Get teacher from Cognito token
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());

        return attendanceService.recordBulkAttendance(request, classroomId, teacher.getId());
    }

    @Operation(summary = "Update attendance record", description = "Update an existing attendance record")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Attendance updated successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request data"),
        @ApiResponse(responseCode = "404", description = "Attendance record not found"),
        @ApiResponse(responseCode = "403", description = "Not authorized to update this attendance record"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @PutMapping(value = "/attendance/{attendanceId}", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public Attendance updateAttendance(
            @Parameter(description = "Attendance ID") @PathVariable String attendanceId,
            @Valid @RequestBody AttendanceUpdateRequest request,
            @AuthenticationPrincipal Jwt userPrincipal) {

        // Get teacher from Cognito token
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());

        return attendanceService.updateAttendance(attendanceId, request, teacher.getId());
    }

    @Operation(summary = "Get attendance by ID", description = "Retrieve a specific attendance record")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Attendance retrieved successfully"),
        @ApiResponse(responseCode = "404", description = "Attendance record not found"),
        @ApiResponse(responseCode = "403", description = "Not authorized to view this attendance record"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/attendance/{attendanceId}", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public Attendance getAttendance(
            @Parameter(description = "Attendance ID") @PathVariable String attendanceId,
            @AuthenticationPrincipal Jwt userPrincipal) {

        // Get teacher from Cognito token
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());

        return attendanceService.getAttendanceById(attendanceId, teacher.getId());
    }

    @Operation(summary = "Get attendance by classroom and date", description = "Retrieve attendance records for a classroom on a specific date")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Attendance records retrieved successfully"),
        @ApiResponse(responseCode = "403", description = "Not authorized to view attendance for this classroom"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/classrooms/{classroomId}/attendance", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<Attendance> getAttendanceByClassroomAndDate(
            @Parameter(description = "Classroom ID") @PathVariable String classroomId,
            @Parameter(description = "Attendance date (YYYY-MM-DD)") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") String date,
            @AuthenticationPrincipal Jwt userPrincipal) {

        // Get teacher from Cognito token
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());

        return attendanceService.getAttendanceByClassroomAndDate(classroomId, date, teacher.getId());
    }

    @Operation(summary = "Get attendance by classroom and date range", description = "Retrieve attendance records for a classroom within a date range")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Attendance records retrieved successfully"),
        @ApiResponse(responseCode = "403", description = "Not authorized to view attendance for this classroom"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/classrooms/{classroomId}/attendance/range", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<Attendance> getAttendanceByClassroomAndDateRange(
            @Parameter(description = "Classroom ID") @PathVariable String classroomId,
            @Parameter(description = "Start date (YYYY-MM-DD)") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") String startDate,
            @Parameter(description = "End date (YYYY-MM-DD)") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") String endDate,
            @AuthenticationPrincipal Jwt userPrincipal) {

        // Get teacher from Cognito token
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());

        return attendanceService.getAttendanceByClassroomAndDateRange(classroomId, startDate, endDate, teacher.getId());
    }

    @Operation(summary = "Get attendance by student and date range", description = "Retrieve attendance records for a student within a date range")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Attendance records retrieved successfully"),
        @ApiResponse(responseCode = "403", description = "Not authorized to view attendance for this student"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/students/{studentId}/attendance", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<Attendance> getAttendanceByStudentAndDateRange(
            @Parameter(description = "Student ID") @PathVariable String studentId,
            @Parameter(description = "Start date (YYYY-MM-DD)") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") String startDate,
            @Parameter(description = "End date (YYYY-MM-DD)") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") String endDate,
            @AuthenticationPrincipal Jwt userPrincipal) {

        // Get teacher from Cognito token
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());

        return attendanceService.getAttendanceByStudentAndDateRange(studentId, startDate, endDate, teacher.getId());
    }

    @Operation(summary = "Get attendance statistics", description = "Get attendance statistics for a classroom (all records)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Statistics retrieved successfully"),
        @ApiResponse(responseCode = "403", description = "Not authorized to view statistics for this classroom"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/classrooms/{classroomId}/attendance/statistics", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public AttendanceService.AttendanceStatistics getAttendanceStatistics(
            @Parameter(description = "Classroom ID") @PathVariable String classroomId,
            @AuthenticationPrincipal Jwt userPrincipal) {

        // Get teacher from Cognito token
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());

        return attendanceService.getAttendanceStatistics(classroomId, teacher.getId());
    }

    @Operation(summary = "Get attendance statistics by date range", description = "Get attendance statistics for a classroom within a specific date range")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Statistics retrieved successfully"),
        @ApiResponse(responseCode = "403", description = "Not authorized to view statistics for this classroom"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/classrooms/{classroomId}/attendance/statistics/range", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public AttendanceService.AttendanceStatistics getAttendanceStatisticsByDateRange(
            @Parameter(description = "Classroom ID") @PathVariable String classroomId,
            @Parameter(description = "Start date (YYYY-MM-DD)") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") String startDate,
            @Parameter(description = "End date (YYYY-MM-DD)") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") String endDate,
            @AuthenticationPrincipal Jwt userPrincipal) {

        // Get teacher from Cognito token
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());

        return attendanceService.getAttendanceStatisticsByDateRange(classroomId, startDate, endDate, teacher.getId());
    }

    @Operation(summary = "Get comprehensive attendance statistics", description = "Get detailed attendance statistics including average attendance, total classes, and students below 75% attendance")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Comprehensive statistics retrieved successfully"),
        @ApiResponse(responseCode = "403", description = "Not authorized to view statistics for this classroom"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/classrooms/{classroomId}/attendance/comprehensive-statistics", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public ClassroomAttendanceStatistics getComprehensiveAttendanceStatistics(
            @Parameter(description = "Classroom ID") @PathVariable String classroomId,
            @Parameter(description = "Start date (YYYY-MM-DD)") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") String startDate,
            @Parameter(description = "End date (YYYY-MM-DD)") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") String endDate,
            @AuthenticationPrincipal Jwt userPrincipal) {

        // Get teacher from Cognito token
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());

        return attendanceService.getComprehensiveAttendanceStatistics(classroomId, startDate, endDate, teacher.getId());
    }

    @Operation(summary = "Delete attendance record", description = "Delete an attendance record")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "Attendance record deleted successfully"),
        @ApiResponse(responseCode = "404", description = "Attendance record not found"),
        @ApiResponse(responseCode = "403", description = "Not authorized to delete this attendance record"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @DeleteMapping(value = "/attendance/{attendanceId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void deleteAttendance(
            @Parameter(description = "Attendance ID") @PathVariable String attendanceId,
            @AuthenticationPrincipal Jwt userPrincipal) {

        // Get teacher from Cognito token
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());

        attendanceService.deleteAttendance(attendanceId, teacher.getId());
    }

    @Operation(summary = "Get comprehensive student attendance report",
               description = "Generate a detailed attendance report for a specific student including statistics, trends, and recommendations")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Student attendance report generated successfully",
                content = @Content(schema = @Schema(implementation = StudentAttendanceReport.class))),
        @ApiResponse(responseCode = "404", description = "Student not found"),
        @ApiResponse(responseCode = "403", description = "Not authorized to view this student's attendance"),
        @ApiResponse(responseCode = "400", description = "Invalid date format or range"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/students/{studentId}/attendance/comprehensive-report", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public StudentAttendanceReport getStudentAttendanceReport(
            @Parameter(description = "Student ID") @PathVariable String studentId,
            @Parameter(description = "Start date (YYYY-MM-DD)") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") String startDate,
            @Parameter(description = "End date (YYYY-MM-DD)") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") String endDate,
            @AuthenticationPrincipal Jwt userPrincipal) {

        // Get teacher from Cognito token
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());

        return attendanceService.getStudentAttendanceReport(studentId, startDate, endDate, teacher.getId());
    }

    @Operation(summary = "Get student attendance dashboard",
               description = "Get comprehensive attendance dashboard for student's own view with statistics, calendar, and insights")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Student attendance dashboard retrieved successfully",
                content = @Content(schema = @Schema(implementation = StudentAttendanceDashboard.class))),
        @ApiResponse(responseCode = "404", description = "No attendance records found"),
        @ApiResponse(responseCode = "400", description = "Invalid date format or range"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/dashboard", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public StudentAttendanceDashboard getStudentAttendanceDashboard(
            @Parameter(description = "Start date (YYYY-MM-DD)") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") String startDate,
            @Parameter(description = "End date (YYYY-MM-DD)") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") String endDate,
            @AuthenticationPrincipal Jwt userPrincipal) {

        // Get student from Cognito token
        // Note: This assumes the student is logged in directly
        // In a real implementation, you'd have a student service to get student info from JWT
        String studentId = userPrincipal.getSubject(); // Simplified - use Cognito ID as student ID

        return attendanceService.getStudentAttendanceDashboard(studentId, startDate, endDate);
    }

    @Operation(summary = "Get student class attendance details",
               description = "Get detailed attendance information for a specific class from student's perspective")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Class attendance details retrieved successfully"),
        @ApiResponse(responseCode = "404", description = "Class not found or no attendance records"),
        @ApiResponse(responseCode = "403", description = "Not enrolled in this class"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/classes/{classroomId}/attendance", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public StudentClassAttendanceDetail getStudentClassAttendanceDetail(
            @Parameter(description = "Classroom ID") @PathVariable String classroomId,
            @Parameter(description = "Start date (YYYY-MM-DD)") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") String startDate,
            @Parameter(description = "End date (YYYY-MM-DD)") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") String endDate,
            @AuthenticationPrincipal Jwt userPrincipal) {

        // Get student from Cognito token
        String studentId = userPrincipal.getSubject(); // Simplified - use Cognito ID as student ID

        return attendanceService.getStudentClassAttendanceDetail(studentId, classroomId, startDate, endDate);
    }
}
