package com.marglabs.trackmyclass.attendance.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class StudentClassAttendanceDetail {
    
    // Class Information
    private String classroomId;
    private String className;
    private String subjectName;
    private String teacherName;
    private String classCode;
    private String description;
    
    // Student Information
    private String studentId;
    private String studentName;
    
    // Report Period
    private String startDate;
    private String endDate;
    private String reportGeneratedDate;
    
    // Class Statistics
    private ClassAttendanceStats stats;
    
    // Detailed attendance records
    private List<Attendance> attendanceRecords;
    
    // Monthly breakdown for this class
    private List<MonthlyClassStats> monthlyBreakdown;
    
    // Performance insights for this class
    private ClassPerformanceInsights insights;
    
    // Schedule information
    private ClassScheduleInfo scheduleInfo;
    
    @Data
    @Accessors(chain = true)
    public static class ClassAttendanceStats {
        private int totalClassesHeld;
        private int totalClassesAttended;
        private int presentCount;
        private int absentCount;
        private int lateCount;
        private int excusedCount;
        private int partialCount;
        private double attendancePercentage;
        private String attendanceGrade; // EXCELLENT, GOOD, AVERAGE, POOR
        private boolean meetingRequirement; // Above 75%
        private int consecutiveAbsences;
        private String lastAttendedDate;
        private String firstAttendedDate;
    }
    
    @Data
    @Accessors(chain = true)
    public static class MonthlyClassStats {
        private String month; // YYYY-MM
        private String monthName; // January 2024
        private int totalClasses;
        private int attended;
        private int missed;
        private double attendanceRate;
        private String performance; // EXCELLENT, GOOD, AVERAGE, POOR
        private List<String> missedDates;
    }
    
    @Data
    @Accessors(chain = true)
    public static class ClassPerformanceInsights {
        private String overallTrend; // IMPROVING, STABLE, DECLINING
        private double trendPercentage;
        private String bestMonth;
        private String worstMonth;
        private List<String> frequentAbsentDays; // Monday, Tuesday, etc.
        private List<String> strengths;
        private List<String> improvementAreas;
        private List<String> recommendations;
        private int perfectAttendanceDays;
        private String longestAttendanceStreak;
        private String currentStreak;
    }
    
    @Data
    @Accessors(chain = true)
    public static class ClassScheduleInfo {
        private String nextClassDate;
        private String nextClassTime;
        private String classFrequency; // Daily, Weekly, etc.
        private List<String> classDays; // Monday, Wednesday, Friday
        private String classTime;
        private String classLocation;
        private int totalScheduledClasses;
        private int remainingClasses;
    }
}
