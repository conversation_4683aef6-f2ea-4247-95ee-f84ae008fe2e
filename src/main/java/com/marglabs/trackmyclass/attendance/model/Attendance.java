package com.marglabs.trackmyclass.attendance.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.Instant;

@Data
@Accessors(chain = true)
public class Attendance {

    private String id;
    private String classroomId;
    private String studentId;
    private String teacherId;
    private String attendanceDate; // Format: YYYY-MM-DD
    private AttendanceStatus status;
    private String notes;
    private String markedBy;
    private Instant createdDate;
    private Instant updatedDate;

    // Transient fields for display
    private String studentName;
    private String studentEmail;
    private String classroomName;
    private String teacherName;
    private String markedByName;
}
