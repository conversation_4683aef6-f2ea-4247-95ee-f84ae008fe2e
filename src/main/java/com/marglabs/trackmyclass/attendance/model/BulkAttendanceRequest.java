package com.marglabs.trackmyclass.attendance.model;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

import java.util.List;

@Data
public class BulkAttendanceRequest {

    @NotBlank(message = "Attendance date is required")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "Attendance date must be in format YYYY-MM-DD")
    private String attendanceDate;

    @NotEmpty(message = "At least one student attendance record is required")
    @Valid
    private List<StudentAttendanceRecord> studentAttendances;

    @Data
    public static class StudentAttendanceRecord {
        @NotBlank(message = "Student ID is required")
        private String studentId;

        private AttendanceStatus status = AttendanceStatus.PRESENT; // Default to present

        private String notes;
    }
}
