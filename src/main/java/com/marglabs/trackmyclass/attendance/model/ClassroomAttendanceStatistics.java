package com.marglabs.trackmyclass.attendance.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class ClassroomAttendanceStatistics {
    
    private String classroomId;
    private String classroomName;
    private String startDate;
    private String endDate;
    
    // Overall statistics
    private int totalClasses;
    private int totalStudents;
    private double averageAttendanceRate;
    
    // Attendance breakdown
    private long totalAttendanceRecords;
    private long totalPresentRecords;
    private long totalAbsentRecords;
    private long totalLateRecords;
    private long totalExcusedRecords;
    private long totalPartialRecords;
    
    // Students with poor attendance (below 75%)
    private int studentsBelow75Percent;
    private List<StudentAttendanceSummary> poorAttendanceStudents;
    
    // Students with excellent attendance (95% and above)
    private int studentsAbove95Percent;
    private List<StudentAttendanceSummary> excellentAttendanceStudents;
    
    // Daily attendance rates
    private List<DailyAttendanceRate> dailyAttendanceRates;
    
    @Data
    @Accessors(chain = true)
    public static class StudentAttendanceSummary {
        private String studentId;
        private String studentName;
        private String studentEmail;
        private int totalClasses;
        private int presentCount;
        private int absentCount;
        private int lateCount;
        private int excusedCount;
        private int partialCount;
        private double attendanceRate;
        
        public double getAttendanceRate() {
            if (totalClasses == 0) return 0.0;
            return (double) (presentCount + lateCount + partialCount) / totalClasses * 100;
        }
    }
    
    @Data
    @Accessors(chain = true)
    public static class DailyAttendanceRate {
        private String date;
        private int totalStudents;
        private int presentStudents;
        private int absentStudents;
        private int lateStudents;
        private int excusedStudents;
        private int partialStudents;
        private double attendanceRate;
        
        public double getAttendanceRate() {
            if (totalStudents == 0) return 0.0;
            return (double) (presentStudents + lateStudents + partialStudents) / totalStudents * 100;
        }
    }
}
