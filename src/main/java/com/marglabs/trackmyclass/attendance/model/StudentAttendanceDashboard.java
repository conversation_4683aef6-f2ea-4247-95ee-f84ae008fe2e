package com.marglabs.trackmyclass.attendance.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class StudentAttendanceDashboard {

    // Student Information
    private String studentId;
    private String studentName;
    private String studentEmail;

    // Dashboard Period
    private String currentDate;
    private String academicYear;

    // Overall Summary
    private AttendanceOverview overallSummary;

    // Class-wise breakdown
    private List<ClassAttendanceDetail> classAttendanceDetails;

    // Recent attendance (last 10 records)
    private List<Attendance> recentAttendance;

    // Monthly statistics
    private List<MonthlyStats> monthlyStatistics;

    // Attendance calendar (for visual representation)
    private List<AttendanceCalendarDay> attendanceCalendar;

    // Performance insights
    private StudentPerformanceInsights insights;

    @Data
    @Accessors(chain = true)
    public static class AttendanceOverview {
        private int totalClassesEnrolled;
        private int totalClassesAttended;
        private int totalPresent;
        private int totalAbsent;
        private int totalLate;
        private int totalExcused;
        private int totalPartial;
        private double overallAttendancePercentage;
        private String attendanceStatus; // EXCELLENT, GOOD, AVERAGE, POOR
        private boolean onTrack; // Meeting minimum attendance requirement
        private int daysUntilRisk; // Days until falling below minimum threshold
    }

    @Data
    @Accessors(chain = true)
    public static class ClassAttendanceDetail {
        private String classroomId;
        private String className;
        private String subjectName;
        private String teacherName;
        private String classCode;

        // Class-specific statistics
        private int totalClassesHeld;
        private int classesAttended;
        private int presentCount;
        private int absentCount;
        private int lateCount;
        private int excusedCount;
        private int partialCount;
        private double attendancePercentage;
        private String classAttendanceGrade;

        // Class schedule info
        private String nextClassDate;
        private String nextClassTime;
        private String lastAttendedDate;

        // Performance indicators
        private boolean meetingRequirement; // Above minimum threshold for this class
        private int consecutiveAbsences;
        private String trendDirection; // IMPROVING, STABLE, DECLINING
    }

    @Data
    @Accessors(chain = true)
    public static class MonthlyStats {
        private String month; // YYYY-MM
        private String monthName; // January 2024
        private int totalClasses;
        private int attended;
        private int missed;
        private double attendanceRate;
        private String performance; // EXCELLENT, GOOD, AVERAGE, POOR
    }

    @Data
    @Accessors(chain = true)
    public static class AttendanceCalendarDay {
        private String date; // YYYY-MM-DD
        private String dayOfWeek;
        private List<ClassDayStatus> classStatuses;
        private String overallDayStatus; // FULL_ATTENDANCE, PARTIAL_ATTENDANCE, NO_ATTENDANCE, NO_CLASSES
    }

    @Data
    @Accessors(chain = true)
    public static class ClassDayStatus {
        private String classroomId;
        private String className;
        private String status; // PRESENT, ABSENT, LATE, EXCUSED, PARTIAL, NO_CLASS
        private String time;
        private String remarks;
    }

    @Data
    @Accessors(chain = true)
    public static class StudentPerformanceInsights {
        private String overallTrend; // IMPROVING, STABLE, DECLINING
        private double trendPercentage;
        private String bestPerformingClass;
        private String needsAttentionClass;
        private List<String> strengths;
        private List<String> improvementAreas;
        private List<String> upcomingClasses; // Next 3 days
        private int perfectAttendanceDays;
        private String longestAttendanceStreak;
        private List<String> achievements; // Attendance milestones
    }
}
