package com.marglabs.trackmyclass.attendance.model;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
public class AttendanceCreationRequest {

    @NotBlank(message = "Student ID is required")
    private String studentId;

    @NotBlank(message = "Attendance date is required")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "Attendance date must be in format YYYY-MM-DD")
    private String attendanceDate;

    @NotNull(message = "Attendance status is required")
    private AttendanceStatus status;

    @Size(max = 500, message = "Notes cannot exceed 500 characters")
    private String notes;
}
