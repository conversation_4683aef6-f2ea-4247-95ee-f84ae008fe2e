package com.marglabs.trackmyclass.attendance.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class StudentAttendanceReport {

    // Student Information
    private String studentId;
    private String studentName;
    private String studentEmail;

    // Report Period
    private String startDate;
    private String endDate;
    private String reportGeneratedDate;

    // Overall Statistics
    private int totalClassesScheduled;
    private int totalClassesAttended;
    private int totalPresentDays;
    private int totalAbsentDays;
    private int totalLateDays;
    private int totalExcusedDays;
    private int totalPartialDays;
    private double overallAttendanceRate;

    // Performance Indicators
    private String attendanceGrade; // EXCELLENT, GOOD, SATISFACTORY, NEEDS_IMPROVEMENT, POOR
    private boolean attendanceAtRisk; // Below 75%
    private boolean excellentAttendance; // Above 95%
    private int consecutiveAbsentDays;
    private String lastAttendanceDate;

    // Classroom-wise breakdown
    private List<ClassroomAttendanceSummary> classroomSummaries;

    // Monthly breakdown
    private List<MonthlyAttendanceSummary> monthlyBreakdown;

    // Recent attendance (last 30 days)
    private List<Attendance> recentAttendanceRecords;

    // Attendance trends
    private AttendanceTrends trends;

    // Recommendations
    private List<String> recommendations;

    @Data
    @Accessors(chain = true)
    public static class ClassroomAttendanceSummary {
        private String classroomId;
        private String classroomName;
        private String teacherName;
        private int totalClasses;
        private int presentCount;
        private int absentCount;
        private int lateCount;
        private int excusedCount;
        private int partialCount;
        private double attendanceRate;
        private String attendanceGrade;
    }

    @Data
    @Accessors(chain = true)
    public static class MonthlyAttendanceSummary {
        private String month; // YYYY-MM format
        private String monthName; // January 2024
        private int totalClasses;
        private int presentCount;
        private int absentCount;
        private int lateCount;
        private int excusedCount;
        private int partialCount;
        private double attendanceRate;
        private String trend; // IMPROVING, DECLINING, STABLE
    }

    @Data
    @Accessors(chain = true)
    public static class AttendanceTrends {
        private String overallTrend; // IMPROVING, DECLINING, STABLE
        private double trendPercentage; // Percentage change over time
        private String bestMonth;
        private String worstMonth;
        private double bestMonthRate;
        private double worstMonthRate;
        private List<String> frequentAbsentDays; // Monday, Tuesday, etc.
        private String mostCommonAbsentReason;
    }
}
