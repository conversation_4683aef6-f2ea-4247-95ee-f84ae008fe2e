package com.marglabs.trackmyclass.assignment.service.llm;

import com.marglabs.trackmyclass.assignment.config.LLMProviderConfig;
import com.marglabs.trackmyclass.assignment.model.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Service
public class GroqProviderService implements LLMProviderService {
    
    private static final Logger logger = LoggerFactory.getLogger(GroqProviderService.class);
    private final RestTemplate restTemplate = new RestTemplate();
    
    @Override
    public LLMProvider getProvider() {
        return LLMProvider.GROQ;
    }
    
    @Override
    public boolean isAvailable(LLMProviderConfig config) {
        return config.isEnabled() && 
               config.getApiKey() != null && 
               !config.getApiKey().isEmpty() &&
               config.getApiUrl() != null &&
               config.getModel() != null;
    }
    
    @Override
    public List<AssignmentQuestion> generateQuestions(AIGenerationRequest request, String assignmentId, LLMProviderConfig config) {
        logger.info("Generating {} questions using Groq for topic: {}", request.getNumberOfQuestions(), request.getTopic());
        
        try {
            String prompt = buildPrompt(request);
            String response = callLLMAPI(prompt, config);
            return parseResponse(response, request, assignmentId);
        } catch (Exception e) {
            logger.error("Failed to generate questions using Groq: {}", e.getMessage(), e);
            throw new RuntimeException("Groq question generation failed: " + e.getMessage());
        }
    }
    
    @Override
    public String buildPrompt(AIGenerationRequest request) {
        StringBuilder prompt = new StringBuilder();
        
        prompt.append("You are an expert educator. Generate ").append(request.getNumberOfQuestions())
              .append(" high-quality educational questions about '").append(request.getTopic()).append("'.\n\n");
        
        prompt.append("Requirements:\n");
        prompt.append("- Difficulty level: ").append(request.getDifficultyLevel()).append("\n");
        
        if (request.getGradeLevel() != null) {
            prompt.append("- Grade level: ").append(request.getGradeLevel()).append("\n");
        }
        
        prompt.append("- Question types: ");
        for (QuestionType type : request.getQuestionTypes()) {
            prompt.append(type.name()).append(", ");
        }
        prompt.append("\n");
        
        if (request.getAdditionalInstructions() != null) {
            prompt.append("- Additional instructions: ").append(request.getAdditionalInstructions()).append("\n");
        }
        
        prompt.append("\nFormat each question EXACTLY as follows (including the separators):\n");
        prompt.append("QUESTION_TYPE: [MULTIPLE_CHOICE|SHORT_ANSWER|LONG_ANSWER|TRUE_FALSE]\n");
        prompt.append("QUESTION: [Clear, concise question text]\n");
        prompt.append("OPTIONS: [For multiple choice only, separate options with |]\n");
        prompt.append("ANSWER: [Correct answer]\n");
        prompt.append("EXPLANATION: [Brief explanation of the answer]\n");
        prompt.append("MARKS: ").append(request.getMarksPerQuestion()).append("\n");
        prompt.append("---\n");
        
        prompt.append("\nGenerate ").append(request.getNumberOfQuestions()).append(" questions now:");
        
        return prompt.toString();
    }
    
    @Override
    public String callLLMAPI(String prompt, LLMProviderConfig config) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(config.getApiKey());
        
        Map<String, Object> requestBody = Map.of(
            "model", config.getModel(),
            "messages", List.of(
                Map.of("role", "system", "content", "You are an expert educator who creates high-quality educational questions."),
                Map.of("role", "user", "content", prompt)
            ),
            "max_tokens", config.getMaxTokens(),
            "temperature", config.getTemperature(),
            "top_p", 0.9,
            "stream", false
        );
        
        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
        
        ResponseEntity<Map> response = restTemplate.exchange(
            config.getApiUrl(), HttpMethod.POST, entity, Map.class);
        
        if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
            Map<String, Object> responseBody = response.getBody();
            List<Map<String, Object>> choices = (List<Map<String, Object>>) responseBody.get("choices");
            if (!choices.isEmpty()) {
                Map<String, Object> message = (Map<String, Object>) choices.get(0).get("message");
                return (String) message.get("content");
            }
        }
        
        throw new RuntimeException("Failed to get response from Groq API");
    }
    
    @Override
    public List<AssignmentQuestion> parseResponse(String response, AIGenerationRequest request, String assignmentId) {
        List<AssignmentQuestion> questions = new ArrayList<>();
        String[] questionBlocks = response.split("---");
        
        int questionNumber = 1;
        for (String block : questionBlocks) {
            if (block.trim().isEmpty()) continue;
            
            try {
                AssignmentQuestion question = parseQuestionBlock(block.trim(), assignmentId, questionNumber++, request.getMarksPerQuestion());
                if (question != null) {
                    questions.add(question);
                }
            } catch (Exception e) {
                logger.warn("Failed to parse question block: {}", e.getMessage());
            }
        }
        
        return questions;
    }
    
    @Override
    public List<String> getRecommendedModels() {
        return List.of(
            "mixtral-8x7b-32768",
            "llama2-70b-4096",
            "gemma-7b-it",
            "llama3-8b-8192",
            "llama3-70b-8192"
        );
    }
    
    @Override
    public boolean validateConfig(LLMProviderConfig config) {
        return config.getApiKey() != null && 
               config.getApiKey().startsWith("gsk_") &&
               config.getModel() != null &&
               getRecommendedModels().contains(config.getModel());
    }
    
    private AssignmentQuestion parseQuestionBlock(String block, String assignmentId, int questionNumber, int defaultMarks) {
        String[] lines = block.split("\n");
        
        AssignmentQuestion question = new AssignmentQuestion();
        question.setAssignmentId(assignmentId);
        question.setQuestionNumber(questionNumber);
        question.setAiGenerated(true);
        question.setAiModel("Groq");
        question.setCreatedDate(System.currentTimeMillis());
        question.setUpdatedDate(System.currentTimeMillis());
        question.setMarks(defaultMarks); // Set default marks
        
        for (String line : lines) {
            line = line.trim();
            if (line.startsWith("QUESTION_TYPE:")) {
                String type = line.substring("QUESTION_TYPE:".length()).trim();
                try {
                    question.setType(QuestionType.valueOf(type));
                } catch (IllegalArgumentException e) {
                    logger.warn("Invalid question type: {}, defaulting to SHORT_ANSWER", type);
                    question.setType(QuestionType.SHORT_ANSWER);
                }
            } else if (line.startsWith("QUESTION:")) {
                question.setQuestionText(line.substring("QUESTION:".length()).trim());
            } else if (line.startsWith("OPTIONS:")) {
                String optionsStr = line.substring("OPTIONS:".length()).trim();
                if (!optionsStr.isEmpty()) {
                    question.setOptions(Arrays.asList(optionsStr.split("\\|")));
                }
            } else if (line.startsWith("ANSWER:")) {
                question.setCorrectAnswer(line.substring("ANSWER:".length()).trim());
            } else if (line.startsWith("EXPLANATION:")) {
                question.setExplanation(line.substring("EXPLANATION:".length()).trim());
            } else if (line.startsWith("MARKS:")) {
                try {
                    question.setMarks(Integer.parseInt(line.substring("MARKS:".length()).trim()));
                } catch (NumberFormatException e) {
                    question.setMarks(defaultMarks);
                }
            }
        }
        
        // Validate required fields
        if (question.getQuestionText() == null || question.getQuestionText().isEmpty() ||
            question.getType() == null || 
            question.getCorrectAnswer() == null || question.getCorrectAnswer().isEmpty()) {
            logger.warn("Incomplete question parsed, skipping: {}", block.substring(0, Math.min(100, block.length())));
            return null;
        }
        
        return question;
    }
}
