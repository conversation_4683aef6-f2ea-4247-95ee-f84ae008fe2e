package com.marglabs.trackmyclass.assignment.service;

import com.marglabs.trackmyclass.assignment.config.LLMProviderConfig;
import com.marglabs.trackmyclass.assignment.model.*;
import com.marglabs.trackmyclass.assignment.service.llm.LLMProviderService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class LLMService {

    private static final Logger logger = LoggerFactory.getLogger(LLMService.class);

    @Autowired
    private List<LLMProviderService> providerServices;

    private Map<LLMProvider, LLMProviderService> providerMap = new HashMap<>();
    private Map<LLMProvider, LLMProviderConfig> providerConfigs = new HashMap<>();

    // Configuration properties for each provider
    @Value("${llm.primary.provider:GOOGLE}")
    private String primaryProvider;

    // OpenAI Configuration
    @Value("${llm.openai.enabled:false}")
    private boolean openaiEnabled;

    @Value("${llm.openai.api.key:********************************************************}")
    private String openaiApiKey;

    @Value("${llm.openai.model:gpt-3.5-turbo}")
    private String openaiModel;

    // Groq Configuration
    @Value("${llm.groq.enabled:false}")
    private boolean groqEnabled;

    @Value("${llm.groq.api.key:********************************************************}")
    private String groqApiKey;

    @Value("${llm.groq.model:llama-3.3-70b-versatile}")
    private String groqModel;

    // Google Gemini Configuration
    @Value("${llm.google.enabled:true}")
    private boolean googleEnabled;

    @Value("${llm.google.api.key:AIzaSyDf0bm4U7p6yEhwD1GnuDZqY1_OEttvEZ8}")
    private String googleApiKey;

    @Value("${llm.google.model:gemini-2.0-flash}")
    private String googleModel;

    // Anthropic Configuration
    @Value("${llm.anthropic.enabled:false}")
    private boolean anthropicEnabled;

    @Value("${llm.anthropic.api.key:}")
    private String anthropicApiKey;

    @Value("${llm.anthropic.model:claude-3-sonnet-20240229}")
    private String anthropicModel;

    @Value("${llm.anthropic.version:2023-06-01}")
    private String anthropicVersion;

    // Ollama Configuration
    @Value("${llm.ollama.enabled:false}")
    private boolean ollamaEnabled;

    @Value("${llm.ollama.api.url:http://localhost:11434/api/generate}")
    private String ollamaApiUrl;

    @Value("${llm.ollama.model:deepseek-r1:latest}")
    private String ollamaModel;

    @PostConstruct
    public void initializeProviders() {
        logger.info("Initializing LLM providers...");

        // Map provider services
        for (LLMProviderService service : providerServices) {
            providerMap.put(service.getProvider(), service);
        }

        // Initialize provider configurations
        initializeProviderConfigs();

        logger.info("Initialized {} LLM providers. Primary provider: {}", providerMap.size(), primaryProvider);
        logProviderStatus();
    }

    private void initializeProviderConfigs() {
        // OpenAI Configuration
        LLMProviderConfig openaiConfig = LLMProviderConfig.createOpenAIConfig()
                .setEnabled(openaiEnabled)
                .setApiKey(openaiApiKey)
                .setModel(openaiModel);
        providerConfigs.put(LLMProvider.OPENAI, openaiConfig);

        // Groq Configuration
        LLMProviderConfig groqConfig = LLMProviderConfig.createGroqConfig()
                .setEnabled(groqEnabled)
                .setApiKey(groqApiKey)
                .setModel(groqModel);
        providerConfigs.put(LLMProvider.GROQ, groqConfig);

        // Google Gemini Configuration
        LLMProviderConfig googleConfig = LLMProviderConfig.createGoogleConfig()
                .setEnabled(googleEnabled)
                .setApiKey(googleApiKey)
                .setModel(googleModel);
        providerConfigs.put(LLMProvider.GOOGLE, googleConfig);

        // Anthropic Configuration
        LLMProviderConfig anthropicConfig = LLMProviderConfig.createAnthropicConfig()
                .setEnabled(anthropicEnabled)
                .setApiKey(anthropicApiKey)
                .setModel(anthropicModel)
                .setVersion(anthropicVersion);
        providerConfigs.put(LLMProvider.ANTHROPIC, anthropicConfig);

        // Ollama Configuration
        LLMProviderConfig ollamaConfig = LLMProviderConfig.createOllamaConfig()
                .setEnabled(ollamaEnabled)
                .setApiUrl(ollamaApiUrl)
                .setModel(ollamaModel);
        providerConfigs.put(LLMProvider.OLLAMA, ollamaConfig);
    }

    private void logProviderStatus() {
        logger.info("LLM Provider Status:");
        for (Map.Entry<LLMProvider, LLMProviderConfig> entry : providerConfigs.entrySet()) {
            LLMProvider provider = entry.getKey();
            LLMProviderConfig config = entry.getValue();
            LLMProviderService service = providerMap.get(provider);

            boolean available = service != null && service.isAvailable(config);
            logger.info("  {} ({}): {} - Model: {}",
                       provider.getDisplayName(),
                       provider.name(),
                       available ? "AVAILABLE" : "DISABLED",
                       config.getModel());
        }
    }

    /**
     * Call primary LLM with a simple text prompt
     */
    public String callPrimaryLLM(String prompt) {
        logger.info("Calling primary LLM with prompt length: {}", prompt.length());
        
        try {
            LLMProvider primary = LLMProvider.valueOf(primaryProvider);
            return callLLMWithProvider(prompt, primary);
        } catch (Exception e) {
            logger.warn("Primary provider {} failed: {}", primaryProvider, e.getMessage());
            
            // Try fallback providers
            List<LLMProvider> fallbackProviders = getAvailableProviders().stream()
                    .filter(provider -> !provider.name().equals(primaryProvider))
                    .collect(Collectors.toList());
            
            for (LLMProvider provider : fallbackProviders) {
                try {
                    return callLLMWithProvider(prompt, provider);
                } catch (Exception ex) {
                    logger.warn("Fallback provider {} failed: {}", provider.getDisplayName(), ex.getMessage());
                }
            }
            
            throw new RuntimeException("All LLM providers failed");
        }
    }
    
    private String callLLMWithProvider(String prompt, LLMProvider provider) {
        LLMProviderService service = providerMap.get(provider);
        LLMProviderConfig config = providerConfigs.get(provider);
        
        if (service == null || !service.isAvailable(config)) {
            throw new RuntimeException("Provider not available: " + provider.getDisplayName());
        }
        
        return service.callLLMAPI(prompt, config);
    }

    /**
     * Generate questions using the primary LLM provider with fallback
     */
    public List<AssignmentQuestion> generateQuestions(AIGenerationRequest request, String assignmentId) {
        logger.info("Generating {} questions for topic: {} using primary provider: {}",
                   request.getNumberOfQuestions(), request.getTopic(), primaryProvider);

        // Try primary provider first
        try {
            LLMProvider primary = LLMProvider.valueOf(primaryProvider);
            List<AssignmentQuestion> questions = generateWithProvider(request, assignmentId, primary);
            if (!questions.isEmpty()) {
                logger.info("Successfully generated {} questions using {}", questions.size(), primary.getDisplayName());
                return questions;
            }
        } catch (Exception e) {
            logger.warn("Primary provider {} failed: {}", primaryProvider, e.getMessage());
        }

        // Try fallback providers
        List<LLMProvider> fallbackProviders = getAvailableProviders().stream()
                .filter(provider -> !provider.name().equals(primaryProvider))
                .collect(Collectors.toList());

        for (LLMProvider provider : fallbackProviders) {
            try {
                List<AssignmentQuestion> questions = generateWithProvider(request, assignmentId, provider);
                if (!questions.isEmpty()) {
                    logger.info("Successfully generated {} questions using fallback provider {}",
                               questions.size(), provider.getDisplayName());
                    return questions;
                }
            } catch (Exception e) {
                logger.warn("Fallback provider {} failed: {}", provider.getDisplayName(), e.getMessage());
            }
        }

        // Final fallback to sample questions
        logger.warn("All LLM providers failed, generating fallback questions");
        return generateFallbackQuestions(request, assignmentId);
    }

    /**
     * Generate questions using a specific provider
     */
    public List<AssignmentQuestion> generateWithProvider(AIGenerationRequest request, String assignmentId, LLMProvider provider) {
        logger.info("Generating questions using provider: {}", provider.getDisplayName());

        LLMProviderService service = providerMap.get(provider);
        LLMProviderConfig config = providerConfigs.get(provider);

        if (service == null) {
            throw new RuntimeException("Provider service not found for: " + provider);
        }

        if (!service.isAvailable(config)) {
            throw new RuntimeException("Provider not available: " + provider.getDisplayName());
        }

        return service.generateQuestions(request, assignmentId, config);
    }

    /**
     * Get list of available providers
     */
    public List<LLMProvider> getAvailableProviders() {
        return providerConfigs.entrySet().stream()
                .filter(entry -> {
                    LLMProviderService service = providerMap.get(entry.getKey());
                    return service != null && service.isAvailable(entry.getValue());
                })
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
    }

    /**
     * Get provider configuration
     */
    public LLMProviderConfig getProviderConfig(LLMProvider provider) {
        return providerConfigs.get(provider);
    }

    /**
     * Update provider configuration
     */
    public void updateProviderConfig(LLMProvider provider, LLMProviderConfig config) {
        providerConfigs.put(provider, config);
        logger.info("Updated configuration for provider: {}", provider.getDisplayName());
    }

    /**
     * Enable/disable a provider
     */
    public void setProviderEnabled(LLMProvider provider, boolean enabled) {
        LLMProviderConfig config = providerConfigs.get(provider);
        if (config != null) {
            config.setEnabled(enabled);
            logger.info("{} provider: {}", enabled ? "Enabled" : "Disabled", provider.getDisplayName());
        }
    }

    /**
     * Set primary provider
     */
    public void setPrimaryProvider(LLMProvider provider) {
        this.primaryProvider = provider.name();
        logger.info("Set primary provider to: {}", provider.getDisplayName());
    }

    /**
     * Get provider status information
     */
    public Map<String, Object> getProviderStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("primaryProvider", primaryProvider);

        Map<String, Object> providers = new HashMap<>();
        for (Map.Entry<LLMProvider, LLMProviderConfig> entry : providerConfigs.entrySet()) {
            LLMProvider provider = entry.getKey();
            LLMProviderConfig config = entry.getValue();
            LLMProviderService service = providerMap.get(provider);

            Map<String, Object> providerInfo = new HashMap<>();
            providerInfo.put("enabled", config.isEnabled());
            providerInfo.put("available", service != null && service.isAvailable(config));
            providerInfo.put("model", config.getModel());
            providerInfo.put("description", config.getDescription());

            providers.put(provider.name(), providerInfo);
        }
        status.put("providers", providers);

        return status;
    }



    /**
     * Generate fallback questions when all LLM providers fail
     */
    private List<AssignmentQuestion> generateFallbackQuestions(AIGenerationRequest request, String assignmentId) {
        logger.info("Generating fallback questions for topic: {}", request.getTopic());
        
        List<AssignmentQuestion> questions = new ArrayList<>();
        
        for (int i = 1; i <= Math.min(request.getNumberOfQuestions(), 5); i++) {
            AssignmentQuestion question = new AssignmentQuestion();
            question.setAssignmentId(assignmentId);
            question.setQuestionNumber(i);
            question.setQuestionText("Sample question " + i + " about " + request.getTopic());
            question.setType(QuestionType.SHORT_ANSWER);
            question.setMarks(request.getMarksPerQuestion());
            question.setCorrectAnswer("Sample answer for question " + i);
            question.setExplanation("This is a sample question generated as fallback.");
            question.setAiGenerated(false);
            question.setCreatedDate(System.currentTimeMillis());
            question.setUpdatedDate(System.currentTimeMillis());
            
            questions.add(question);
        }
        
        return questions;
    }
}
