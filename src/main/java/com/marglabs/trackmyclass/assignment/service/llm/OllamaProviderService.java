package com.marglabs.trackmyclass.assignment.service.llm;

import com.marglabs.trackmyclass.assignment.config.LLMProviderConfig;
import com.marglabs.trackmyclass.assignment.model.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Service
public class OllamaProviderService implements LLMProviderService {
    
    private static final Logger logger = LoggerFactory.getLogger(OllamaProviderService.class);
    private final RestTemplate restTemplate = new RestTemplate();
    
    @Override
    public LLMProvider getProvider() {
        return LLMProvider.OLLAMA;
    }
    
    @Override
    public boolean isAvailable(LLMProviderConfig config) {
        if (!config.isEnabled() || config.getApiUrl() == null || config.getModel() == null) {
            return false;
        }
        
        try {
            // Test Ollama connection by checking if the model is available
            return testOllamaConnection(config);
        } catch (Exception e) {
            logger.warn("Ollama connection test failed: {}", e.getMessage());
            return false;
        }
    }
    
    @Override
    public List<AssignmentQuestion> generateQuestions(AIGenerationRequest request, String assignmentId, LLMProviderConfig config) {
        logger.info("Generating {} questions using Ollama (DeepSeek) for topic: {}", 
                   request.getNumberOfQuestions(), request.getTopic());
        
        try {
            String prompt = buildPrompt(request);
            String response = callLLMAPI(prompt, config);
            return parseResponse(response, request, assignmentId);
        } catch (Exception e) {
            logger.error("Failed to generate questions using Ollama: {}", e.getMessage(), e);
            throw new RuntimeException("Ollama question generation failed: " + e.getMessage());
        }
    }
    
    @Override
    public String buildPrompt(AIGenerationRequest request) {
        StringBuilder prompt = new StringBuilder();
        
        prompt.append("You are DeepSeek, an advanced AI assistant specialized in education. ");
        prompt.append("Generate ").append(request.getNumberOfQuestions())
              .append(" high-quality educational questions about '").append(request.getTopic()).append("'.\n\n");
        
        prompt.append("Requirements:\n");
        prompt.append("- Difficulty level: ").append(request.getDifficultyLevel()).append("\n");
        
        if (request.getGradeLevel() != null) {
            prompt.append("- Target grade level: ").append(request.getGradeLevel()).append("\n");
        }
        
        prompt.append("- Question types to generate: ");
        for (QuestionType type : request.getQuestionTypes()) {
            prompt.append(type.name()).append(", ");
        }
        prompt.append("\n");
        
        if (request.getAdditionalInstructions() != null) {
            prompt.append("- Special instructions: ").append(request.getAdditionalInstructions()).append("\n");
        }
        
        prompt.append("\nIMPORTANT: Format each question EXACTLY as shown below (including separators):\n\n");
        prompt.append("QUESTION_TYPE: [MULTIPLE_CHOICE|SHORT_ANSWER|LONG_ANSWER|TRUE_FALSE]\n");
        prompt.append("QUESTION: [Clear, educational question text]\n");
        prompt.append("OPTIONS: [For multiple choice only, separate options with |]\n");
        prompt.append("ANSWER: [Correct answer]\n");
        prompt.append("EXPLANATION: [Brief explanation of why this is correct]\n");
        prompt.append("MARKS: ").append(request.getMarksPerQuestion()).append("\n");
        prompt.append("---\n\n");
        
        prompt.append("Generate ").append(request.getNumberOfQuestions())
              .append(" questions now, following the exact format above:");
        
        return prompt.toString();
    }
    
    @Override
    public String callLLMAPI(String prompt, LLMProviderConfig config) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        
        // Ollama API request format
        Map<String, Object> requestBody = Map.of(
            "model", config.getModel(),
            "prompt", prompt,
            "stream", true,
            "options", Map.of(
                "temperature", config.getTemperature(),
                "top_p", 0.9,
                "top_k", 40,
                "num_predict", config.getMaxTokens()
            )
        );
        
        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
        
        try {
            ResponseEntity<Map> response = restTemplate.exchange(
                config.getApiUrl(), HttpMethod.POST, entity, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                String responseText = (String) responseBody.get("response");
                
                if (responseText != null && !responseText.trim().isEmpty()) {
                    return responseText;
                }
            }
            
            throw new RuntimeException("Empty or invalid response from Ollama");
            
        } catch (Exception e) {
            logger.error("Ollama API call failed: {}", e.getMessage());
            throw new RuntimeException("Failed to call Ollama API: " + e.getMessage());
        }
    }
    
    @Override
    public List<AssignmentQuestion> parseResponse(String response, AIGenerationRequest request, String assignmentId) {
        List<AssignmentQuestion> questions = new ArrayList<>();
        
        // Clean up the response - remove any markdown formatting
        response = response.replaceAll("```[a-zA-Z]*", "").replaceAll("```", "");
        
        String[] questionBlocks = response.split("---");
        
        int questionNumber = 1;
        for (String block : questionBlocks) {
            if (block.trim().isEmpty()) continue;
            
            try {
                AssignmentQuestion question = parseQuestionBlock(block.trim(), assignmentId, questionNumber++, request.getMarksPerQuestion());
                if (question != null) {
                    questions.add(question);
                }
            } catch (Exception e) {
                logger.warn("Failed to parse question block: {}", e.getMessage());
            }
        }
        
        logger.info("Successfully parsed {} questions from Ollama response", questions.size());
        return questions;
    }
    
    @Override
    public List<String> getRecommendedModels() {
        return List.of(
            "deepseek-coder:6.7b",
            "deepseek-coder:1.3b",
            "deepseek-coder:33b",
            "deepseek-llm:7b",
            "deepseek-llm:67b",
            "deepseek-r1:latest",
            "llama3:8b",
            "llama3:70b",
            "mistral:7b",
            "codellama:7b",
            "phi3:3.8b"
        );
    }
    
    @Override
    public boolean validateConfig(LLMProviderConfig config) {
        return config.getApiUrl() != null && 
               config.getModel() != null &&
               config.getApiUrl().contains("ollama") &&
               testOllamaConnection(config);
    }
    
    /**
     * Test Ollama connection and model availability
     */
    private boolean testOllamaConnection(LLMProviderConfig config) {
        try {
            // Test with a simple prompt
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            Map<String, Object> testRequest = Map.of(
                "model", config.getModel(),
                "prompt", "Hello, are you working?",
                "stream", false,
                "options", Map.of("num_predict", 10)
            );
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(testRequest, headers);
            
            ResponseEntity<Map> response = restTemplate.exchange(
                config.getApiUrl(), HttpMethod.POST, entity, Map.class);
            
            return response.getStatusCode() == HttpStatus.OK;
            
        } catch (Exception e) {
            logger.debug("Ollama connection test failed: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * Parse individual question block from Ollama response
     */
    private AssignmentQuestion parseQuestionBlock(String block, String assignmentId, int questionNumber, int defaultMarks) {
        String[] lines = block.split("\n");
        
        AssignmentQuestion question = new AssignmentQuestion();
        question.setAssignmentId(assignmentId);
        question.setQuestionNumber(questionNumber);
        question.setAiGenerated(true);
        question.setAiModel("Ollama-DeepSeek");
        question.setCreatedDate(System.currentTimeMillis());
        question.setUpdatedDate(System.currentTimeMillis());
        question.setMarks(defaultMarks);
        
        for (String line : lines) {
            line = line.trim();
            if (line.startsWith("QUESTION_TYPE:")) {
                String type = line.substring("QUESTION_TYPE:".length()).trim();
                try {
                    question.setType(QuestionType.valueOf(type));
                } catch (IllegalArgumentException e) {
                    logger.warn("Invalid question type: {}, defaulting to SHORT_ANSWER", type);
                    question.setType(QuestionType.SHORT_ANSWER);
                }
            } else if (line.startsWith("QUESTION:")) {
                question.setQuestionText(line.substring("QUESTION:".length()).trim());
            } else if (line.startsWith("OPTIONS:")) {
                String optionsStr = line.substring("OPTIONS:".length()).trim();
                if (!optionsStr.isEmpty() && !optionsStr.equals("N/A")) {
                    question.setOptions(Arrays.asList(optionsStr.split("\\|")));
                }
            } else if (line.startsWith("ANSWER:")) {
                question.setCorrectAnswer(line.substring("ANSWER:".length()).trim());
            } else if (line.startsWith("EXPLANATION:")) {
                question.setExplanation(line.substring("EXPLANATION:".length()).trim());
            } else if (line.startsWith("MARKS:")) {
                try {
                    question.setMarks(Integer.parseInt(line.substring("MARKS:".length()).trim()));
                } catch (NumberFormatException e) {
                    question.setMarks(defaultMarks);
                }
            }
        }
        
        // Validate required fields
        if (question.getQuestionText() == null || question.getQuestionText().isEmpty() ||
            question.getType() == null || 
            question.getCorrectAnswer() == null || question.getCorrectAnswer().isEmpty()) {
            
            logger.warn("Incomplete question parsed from Ollama, skipping: {}", 
                       block.substring(0, Math.min(100, block.length())));
            return null;
        }
        
        // Set AI prompt for tracking
        question.setAiPrompt("Generated via Ollama with DeepSeek model: " + question.getQuestionText().substring(0, Math.min(50, question.getQuestionText().length())));
        
        return question;
    }
    
    /**
     * Get available models from Ollama instance
     */
    public List<String> getAvailableModels(LLMProviderConfig config) {
        try {
            String tagsUrl = config.getApiUrl().replace("/api/generate", "/api/tags");
            
            ResponseEntity<Map> response = restTemplate.exchange(
                tagsUrl, HttpMethod.GET, null, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                List<Map<String, Object>> models = (List<Map<String, Object>>) responseBody.get("models");
                
                return models.stream()
                        .map(model -> (String) model.get("name"))
                        .toList();
            }
        } catch (Exception e) {
            logger.warn("Failed to get available models from Ollama: {}", e.getMessage());
        }
        
        return getRecommendedModels();
    }
}
