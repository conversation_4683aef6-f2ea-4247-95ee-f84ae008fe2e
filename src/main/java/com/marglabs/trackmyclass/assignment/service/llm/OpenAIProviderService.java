package com.marglabs.trackmyclass.assignment.service.llm;

import com.marglabs.trackmyclass.assignment.config.LLMProviderConfig;
import com.marglabs.trackmyclass.assignment.model.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Service
public class OpenAIProviderService implements LLMProviderService {
    
    private static final Logger logger = LoggerFactory.getLogger(OpenAIProviderService.class);
    private final RestTemplate restTemplate = new RestTemplate();
    
    @Override
    public LLMProvider getProvider() {
        return LLMProvider.OPENAI;
    }
    
    @Override
    public boolean isAvailable(LLMProviderConfig config) {
        return config.isEnabled() && 
               config.getApiKey() != null && 
               !config.getApiKey().isEmpty() &&
               config.getApiUrl() != null &&
               config.getModel() != null;
    }
    
    @Override
    public List<AssignmentQuestion> generateQuestions(AIGenerationRequest request, String assignmentId, LLMProviderConfig config) {
        logger.info("Generating {} questions using OpenAI for topic: {}", request.getNumberOfQuestions(), request.getTopic());
        
        try {
            String prompt = buildPrompt(request);
            String response = callLLMAPI(prompt, config);
            return parseResponse(response, request, assignmentId);
        } catch (Exception e) {
            logger.error("Failed to generate questions using OpenAI: {}", e.getMessage(), e);
            throw new RuntimeException("OpenAI question generation failed: " + e.getMessage());
        }
    }
    
    @Override
    public String buildPrompt(AIGenerationRequest request) {
        StringBuilder prompt = new StringBuilder();
        
        prompt.append("Generate ").append(request.getNumberOfQuestions())
              .append(" educational questions about '").append(request.getTopic()).append("'.\n");
        
        prompt.append("Difficulty level: ").append(request.getDifficultyLevel()).append("\n");
        
        if (request.getGradeLevel() != null) {
            prompt.append("Grade level: ").append(request.getGradeLevel()).append("\n");
        }
        
        prompt.append("Question types to include: ");
        for (QuestionType type : request.getQuestionTypes()) {
            prompt.append(type.name()).append(", ");
        }
        prompt.append("\n");
        
        if (request.getAdditionalInstructions() != null) {
            prompt.append("Additional instructions: ").append(request.getAdditionalInstructions()).append("\n");
        }
        
        prompt.append("\nFormat each question as follows:\n");
        prompt.append("QUESTION_TYPE: [MULTIPLE_CHOICE|SHORT_ANSWER|LONG_ANSWER|TRUE_FALSE]\n");
        prompt.append("QUESTION: [Question text]\n");
        prompt.append("OPTIONS: [For multiple choice only, separate with |]\n");
        prompt.append("ANSWER: [Correct answer]\n");
        prompt.append("EXPLANATION: [Brief explanation]\n");
        prompt.append("MARKS: ").append(request.getMarksPerQuestion()).append("\n");
        prompt.append("---\n");
        
        return prompt.toString();
    }
    
    @Override
    public String callLLMAPI(String prompt, LLMProviderConfig config) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(config.getApiKey());
        
        if (config.getOrganizationId() != null) {
            headers.set("OpenAI-Organization", config.getOrganizationId());
        }
        
        Map<String, Object> requestBody = Map.of(
            "model", config.getModel(),
            "messages", List.of(
                Map.of("role", "user", "content", prompt)
            ),
            "max_tokens", config.getMaxTokens(),
            "temperature", config.getTemperature()
        );
        
        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
        
        ResponseEntity<Map> response = restTemplate.exchange(
            config.getApiUrl(), HttpMethod.POST, entity, Map.class);
        
        if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
            Map<String, Object> responseBody = response.getBody();
            List<Map<String, Object>> choices = (List<Map<String, Object>>) responseBody.get("choices");
            if (!choices.isEmpty()) {
                Map<String, Object> message = (Map<String, Object>) choices.get(0).get("message");
                return (String) message.get("content");
            }
        }
        
        throw new RuntimeException("Failed to get response from OpenAI API");
    }
    
    @Override
    public List<AssignmentQuestion> parseResponse(String response, AIGenerationRequest request, String assignmentId) {
        List<AssignmentQuestion> questions = new ArrayList<>();
        String[] questionBlocks = response.split("---");
        
        int questionNumber = 1;
        for (String block : questionBlocks) {
            if (block.trim().isEmpty()) continue;
            
            try {
                AssignmentQuestion question = parseQuestionBlock(block.trim(), assignmentId, questionNumber++);
                if (question != null) {
                    questions.add(question);
                }
            } catch (Exception e) {
                logger.warn("Failed to parse question block: {}", e.getMessage());
            }
        }
        
        return questions;
    }
    
    @Override
    public List<String> getRecommendedModels() {
        return List.of(
            "gpt-4",
            "gpt-4-turbo-preview",
            "gpt-3.5-turbo",
            "gpt-3.5-turbo-16k"
        );
    }
    
    @Override
    public boolean validateConfig(LLMProviderConfig config) {
        return config.getApiKey() != null && 
               config.getApiKey().startsWith("sk-") &&
               config.getModel() != null &&
               getRecommendedModels().contains(config.getModel());
    }
    
    private AssignmentQuestion parseQuestionBlock(String block, String assignmentId, int questionNumber) {
        String[] lines = block.split("\n");
        
        AssignmentQuestion question = new AssignmentQuestion();
        question.setAssignmentId(assignmentId);
        question.setQuestionNumber(questionNumber);
        question.setAiGenerated(true);
        question.setAiModel("OpenAI-" + questionNumber);
        question.setCreatedDate(System.currentTimeMillis());
        question.setUpdatedDate(System.currentTimeMillis());
        
        for (String line : lines) {
            line = line.trim();
            if (line.startsWith("QUESTION_TYPE:")) {
                String type = line.substring("QUESTION_TYPE:".length()).trim();
                question.setType(QuestionType.valueOf(type));
            } else if (line.startsWith("QUESTION:")) {
                question.setQuestionText(line.substring("QUESTION:".length()).trim());
            } else if (line.startsWith("OPTIONS:")) {
                String optionsStr = line.substring("OPTIONS:".length()).trim();
                question.setOptions(Arrays.asList(optionsStr.split("\\|")));
            } else if (line.startsWith("ANSWER:")) {
                question.setCorrectAnswer(line.substring("ANSWER:".length()).trim());
            } else if (line.startsWith("EXPLANATION:")) {
                question.setExplanation(line.substring("EXPLANATION:".length()).trim());
            } else if (line.startsWith("MARKS:")) {
                try {
                    question.setMarks(Integer.parseInt(line.substring("MARKS:".length()).trim()));
                } catch (NumberFormatException e) {
                    question.setMarks(1);
                }
            }
        }
        
        if (question.getQuestionText() == null || question.getType() == null || question.getCorrectAnswer() == null) {
            return null;
        }
        
        return question;
    }
}
