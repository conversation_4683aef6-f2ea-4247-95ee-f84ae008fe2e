package com.marglabs.trackmyclass.assignment.service.llm;

import com.marglabs.trackmyclass.assignment.config.LLMProviderConfig;
import com.marglabs.trackmyclass.assignment.model.AIGenerationRequest;
import com.marglabs.trackmyclass.assignment.model.AssignmentQuestion;
import com.marglabs.trackmyclass.assignment.model.LLMProvider;

import java.util.List;

public interface LLMProviderService {
    
    /**
     * Get the provider type this service handles
     */
    LLMProvider getProvider();
    
    /**
     * Check if this provider is available and configured
     */
    boolean isAvailable(LLMProviderConfig config);
    
    /**
     * Generate questions using this LLM provider
     */
    List<AssignmentQuestion> generateQuestions(AIGenerationRequest request, String assignmentId, LLMProviderConfig config);
    
    /**
     * Build provider-specific prompt
     */
    String buildPrompt(AIGenerationRequest request);
    
    /**
     * Call the LLM API
     */
    String callLLMAPI(String prompt, LLMProviderConfig config);
    
    /**
     * Parse the response from the LLM
     */
    List<AssignmentQuestion> parseResponse(String response, AIGenerationRequest request, String assignmentId);
    
    /**
     * Get provider-specific model recommendations
     */
    List<String> getRecommendedModels();
    
    /**
     * Validate provider-specific configuration
     */
    boolean validateConfig(LLMProviderConfig config);
}
