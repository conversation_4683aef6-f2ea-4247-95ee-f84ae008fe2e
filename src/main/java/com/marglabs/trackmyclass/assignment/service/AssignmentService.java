package com.marglabs.trackmyclass.assignment.service;

import com.marglabs.common.utils.IdGenerator;
import com.marglabs.trackmyclass.assignment.model.*;
import com.marglabs.trackmyclass.classroom.service.ClassroomService;
import com.marglabs.trackmyclass.enrollment.service.EnrollmentService;
import com.marglabs.trackmyclass.user.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
public class AssignmentService {
    
    private static final Logger logger = LoggerFactory.getLogger(AssignmentService.class);
    
    @Autowired
    private LLMService llmService;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private ClassroomService classroomService;
    
    @Autowired
    private EnrollmentService enrollmentService;
    
    @Autowired
    private IdGenerator idGenerator;
    
    /**
     * Create a new assignment
     */
    @Transactional
    public Assignment createAssignment(AssignmentCreationRequest request, String teacherId) {
        logger.info("Creating assignment '{}' for teacher {} in classroom {}", 
                   request.getTitle(), teacherId, request.getClassroomId());
        
        // Verify teacher exists and classroom belongs to teacher
        var teacher = userService.getUserById(teacherId);
        var classroom = classroomService.getClassroomById(request.getClassroomId());
        
        // Create assignment
        Assignment assignment = new Assignment();
        assignment.setId(idGenerator.generateId());
        assignment.setTeacherId(teacherId);
        assignment.setTeacherName(teacher.getName());
        assignment.setClassroomId(request.getClassroomId());
        assignment.setClassName(classroom.getClassName());
        assignment.setTitle(request.getTitle());
        assignment.setDescription(request.getDescription());
        assignment.setSubject(request.getSubject());
        assignment.setTopic(request.getTopic());
        assignment.setType(request.getType());
        assignment.setStatus(AssignmentStatus.DRAFT);
        assignment.setDueDate(request.getDueDate());
        assignment.setActive(true);
        assignment.setCreatedDate(System.currentTimeMillis());
        assignment.setUpdatedDate(System.currentTimeMillis());
        
        // Generate or create questions
        List<AssignmentQuestion> questions = new ArrayList<>();
        
        if (request.getType() == AssignmentType.AI_GENERATED && request.getAiRequest() != null) {
            // Generate questions using AI
            questions = llmService.generateQuestions(request.getAiRequest(), assignment.getId());
        } else if (request.getQuestions() != null) {
            // Create manual questions
            int questionNumber = 1;
            for (QuestionCreationRequest questionReq : request.getQuestions()) {
                AssignmentQuestion question = new AssignmentQuestion();
                question.setId(idGenerator.generateId());
                question.setAssignmentId(assignment.getId());
                question.setQuestionNumber(questionNumber++);
                question.setQuestionText(questionReq.getQuestionText());
                question.setType(questionReq.getType());
                question.setMarks(questionReq.getMarks());
                question.setCorrectAnswer(questionReq.getCorrectAnswer());
                question.setExplanation(questionReq.getExplanation());
                question.setOptions(questionReq.getOptions());
                question.setAiGenerated(false);
                question.setCreatedDate(System.currentTimeMillis());
                question.setUpdatedDate(System.currentTimeMillis());
                
                questions.add(question);
            }
        }
        
        // Set assignment statistics
        assignment.setQuestions(questions);
        assignment.setTotalQuestions(questions.size());
        assignment.setTotalMarks(questions.stream().mapToInt(AssignmentQuestion::getMarks).sum());
        assignment.setTotalSubmissions(0);
        assignment.setGradedSubmissions(0);
        assignment.setAverageScore(0.0);
        
        logger.info("Successfully created assignment '{}' with {} questions", assignment.getTitle(), questions.size());
        return assignment;
    }
    
    /**
     * Get assignment by ID
     */
    public Assignment getAssignmentById(String assignmentId, String userId) {
        logger.info("Getting assignment {} for user {}", assignmentId, userId);
        
        // For now, return a mock assignment
        // In a real implementation, this would fetch from database
        Assignment assignment = new Assignment();
        assignment.setId(assignmentId);
        assignment.setTitle("Sample Assignment");
        assignment.setDescription("This is a sample assignment");
        assignment.setStatus(AssignmentStatus.PUBLISHED);
        assignment.setQuestions(new ArrayList<>());
        
        return assignment;
    }
    
    /**
     * Get assignments by teacher
     */
    public List<Assignment> getAssignmentsByTeacher(String teacherId) {
        logger.info("Getting assignments for teacher {}", teacherId);
        
        // Mock implementation
        return new ArrayList<>();
    }
    
    /**
     * Get assignments by classroom
     */
    public List<Assignment> getAssignmentsByClassroom(String classroomId, String teacherId) {
        logger.info("Getting assignments for classroom {} by teacher {}", classroomId, teacherId);
        
        // Mock implementation
        return new ArrayList<>();
    }
    
    /**
     * Update assignment status
     */
    @Transactional
    public Assignment updateAssignmentStatus(String assignmentId, AssignmentStatus status, String teacherId) {
        logger.info("Updating assignment {} status to {} by teacher {}", assignmentId, status, teacherId);
        
        // Mock implementation
        Assignment assignment = getAssignmentById(assignmentId, teacherId);
        assignment.setStatus(status);
        assignment.setUpdatedDate(System.currentTimeMillis());
        
        return assignment;
    }
    
    /**
     * Delete assignment
     */
    @Transactional
    public void deleteAssignment(String assignmentId, String teacherId) {
        logger.info("Deleting assignment {} by teacher {}", assignmentId, teacherId);
        
        // Mock implementation - would delete from database
    }
    
    /**
     * Get assignment submissions
     */
    public List<AssignmentSubmission> getAssignmentSubmissions(String assignmentId, String teacherId) {
        logger.info("Getting submissions for assignment {} by teacher {}", assignmentId, teacherId);
        
        // Mock implementation
        return new ArrayList<>();
    }
    
    /**
     * Mark assignment submission
     */
    @Transactional
    public AssignmentSubmission markSubmission(MarkingRequest request, String teacherId) {
        logger.info("Marking submission {} by teacher {}", request.getSubmissionId(), teacherId);
        
        // Mock implementation
        AssignmentSubmission submission = new AssignmentSubmission();
        submission.setId(request.getSubmissionId());
        submission.setStatus(SubmissionStatus.GRADED);
        submission.setTeacherFeedback(request.getOverallFeedback());
        submission.setGradedDate(java.time.LocalDateTime.now().toString());
        
        return submission;
    }
    
    /**
     * Get student assignments
     */
    public List<Assignment> getStudentAssignments(String studentId) {
        logger.info("Getting assignments for student {}", studentId);
        
        // Mock implementation
        return new ArrayList<>();
    }
    
    /**
     * Get student submissions
     */
    public List<AssignmentSubmission> getStudentSubmissions(String studentId) {
        logger.info("Getting submissions for student {}", studentId);
        
        // Mock implementation
        return new ArrayList<>();
    }
}
