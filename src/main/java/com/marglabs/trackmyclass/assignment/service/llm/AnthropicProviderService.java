package com.marglabs.trackmyclass.assignment.service.llm;

import com.marglabs.trackmyclass.assignment.config.LLMProviderConfig;
import com.marglabs.trackmyclass.assignment.model.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Service
public class AnthropicProviderService implements LLMProviderService {
    
    private static final Logger logger = LoggerFactory.getLogger(AnthropicProviderService.class);
    private final RestTemplate restTemplate = new RestTemplate();
    
    @Override
    public LLMProvider getProvider() {
        return LLMProvider.ANTHROPIC;
    }
    
    @Override
    public boolean isAvailable(LLMProviderConfig config) {
        return config.isEnabled() && 
               config.getApiKey() != null && 
               !config.getApiKey().isEmpty() &&
               config.getApiUrl() != null &&
               config.getModel() != null &&
               config.getVersion() != null;
    }
    
    @Override
    public List<AssignmentQuestion> generateQuestions(AIGenerationRequest request, String assignmentId, LLMProviderConfig config) {
        logger.info("Generating {} questions using Anthropic Claude for topic: {}", request.getNumberOfQuestions(), request.getTopic());
        
        try {
            String prompt = buildPrompt(request);
            String response = callLLMAPI(prompt, config);
            return parseResponse(response, request, assignmentId);
        } catch (Exception e) {
            logger.error("Failed to generate questions using Anthropic: {}", e.getMessage(), e);
            throw new RuntimeException("Anthropic question generation failed: " + e.getMessage());
        }
    }
    
    @Override
    public String buildPrompt(AIGenerationRequest request) {
        StringBuilder prompt = new StringBuilder();
        
        prompt.append("I need you to create ").append(request.getNumberOfQuestions())
              .append(" educational questions about '").append(request.getTopic()).append("'.\n\n");
        
        prompt.append("Specifications:\n");
        prompt.append("- Difficulty: ").append(request.getDifficultyLevel()).append("\n");
        
        if (request.getGradeLevel() != null) {
            prompt.append("- Target grade: ").append(request.getGradeLevel()).append("\n");
        }
        
        prompt.append("- Question types needed: ");
        for (QuestionType type : request.getQuestionTypes()) {
            prompt.append(type.name()).append(", ");
        }
        prompt.append("\n");
        
        if (request.getAdditionalInstructions() != null) {
            prompt.append("- Special requirements: ").append(request.getAdditionalInstructions()).append("\n");
        }
        
        prompt.append("\nPlease format each question using this exact structure:\n");
        prompt.append("QUESTION_TYPE: [MULTIPLE_CHOICE|SHORT_ANSWER|LONG_ANSWER|TRUE_FALSE]\n");
        prompt.append("QUESTION: [The question text]\n");
        prompt.append("OPTIONS: [For multiple choice, separate with |]\n");
        prompt.append("ANSWER: [The correct answer]\n");
        prompt.append("EXPLANATION: [Why this is the correct answer]\n");
        prompt.append("MARKS: ").append(request.getMarksPerQuestion()).append("\n");
        prompt.append("---\n");
        
        prompt.append("\nPlease create ").append(request.getNumberOfQuestions()).append(" questions following this format.");
        
        return prompt.toString();
    }
    
    @Override
    public String callLLMAPI(String prompt, LLMProviderConfig config) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("x-api-key", config.getApiKey());
        headers.set("anthropic-version", config.getVersion());
        
        Map<String, Object> requestBody = Map.of(
            "model", config.getModel(),
            "max_tokens", config.getMaxTokens(),
            "temperature", config.getTemperature(),
            "messages", List.of(
                Map.of("role", "user", "content", prompt)
            )
        );
        
        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
        
        ResponseEntity<Map> response = restTemplate.exchange(
            config.getApiUrl(), HttpMethod.POST, entity, Map.class);
        
        if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
            Map<String, Object> responseBody = response.getBody();
            List<Map<String, Object>> content = (List<Map<String, Object>>) responseBody.get("content");
            if (!content.isEmpty()) {
                return (String) content.get(0).get("text");
            }
        }
        
        throw new RuntimeException("Failed to get response from Anthropic API");
    }
    
    @Override
    public List<AssignmentQuestion> parseResponse(String response, AIGenerationRequest request, String assignmentId) {
        List<AssignmentQuestion> questions = new ArrayList<>();
        String[] questionBlocks = response.split("---");
        
        int questionNumber = 1;
        for (String block : questionBlocks) {
            if (block.trim().isEmpty()) continue;
            
            try {
                AssignmentQuestion question = parseQuestionBlock(block.trim(), assignmentId, questionNumber++, request.getMarksPerQuestion());
                if (question != null) {
                    questions.add(question);
                }
            } catch (Exception e) {
                logger.warn("Failed to parse question block: {}", e.getMessage());
            }
        }
        
        return questions;
    }
    
    @Override
    public List<String> getRecommendedModels() {
        return List.of(
            "claude-3-opus-20240229",
            "claude-3-sonnet-20240229",
            "claude-3-haiku-20240307",
            "claude-2.1",
            "claude-2.0"
        );
    }
    
    @Override
    public boolean validateConfig(LLMProviderConfig config) {
        return config.getApiKey() != null && 
               config.getApiKey().startsWith("sk-ant-") &&
               config.getModel() != null &&
               config.getVersion() != null &&
               getRecommendedModels().contains(config.getModel());
    }
    
    private AssignmentQuestion parseQuestionBlock(String block, String assignmentId, int questionNumber, int defaultMarks) {
        String[] lines = block.split("\n");
        
        AssignmentQuestion question = new AssignmentQuestion();
        question.setAssignmentId(assignmentId);
        question.setQuestionNumber(questionNumber);
        question.setAiGenerated(true);
        question.setAiModel("Claude");
        question.setCreatedDate(System.currentTimeMillis());
        question.setUpdatedDate(System.currentTimeMillis());
        question.setMarks(defaultMarks);
        
        for (String line : lines) {
            line = line.trim();
            if (line.startsWith("QUESTION_TYPE:")) {
                String type = line.substring("QUESTION_TYPE:".length()).trim();
                try {
                    question.setType(QuestionType.valueOf(type));
                } catch (IllegalArgumentException e) {
                    question.setType(QuestionType.SHORT_ANSWER);
                }
            } else if (line.startsWith("QUESTION:")) {
                question.setQuestionText(line.substring("QUESTION:".length()).trim());
            } else if (line.startsWith("OPTIONS:")) {
                String optionsStr = line.substring("OPTIONS:".length()).trim();
                if (!optionsStr.isEmpty()) {
                    question.setOptions(Arrays.asList(optionsStr.split("\\|")));
                }
            } else if (line.startsWith("ANSWER:")) {
                question.setCorrectAnswer(line.substring("ANSWER:".length()).trim());
            } else if (line.startsWith("EXPLANATION:")) {
                question.setExplanation(line.substring("EXPLANATION:".length()).trim());
            } else if (line.startsWith("MARKS:")) {
                try {
                    question.setMarks(Integer.parseInt(line.substring("MARKS:".length()).trim()));
                } catch (NumberFormatException e) {
                    question.setMarks(defaultMarks);
                }
            }
        }
        
        if (question.getQuestionText() == null || question.getQuestionText().isEmpty() ||
            question.getType() == null || 
            question.getCorrectAnswer() == null || question.getCorrectAnswer().isEmpty()) {
            return null;
        }
        
        return question;
    }
}
