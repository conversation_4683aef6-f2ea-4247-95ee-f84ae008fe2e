package com.marglabs.trackmyclass.assignment.entity;

import com.marglabs.trackmyclass.assignment.model.SubmissionStatus;
import jakarta.persistence.*;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.Instant;
import java.util.List;

@Entity
@Table(name = "assignment_submissions", 
       uniqueConstraints = @UniqueConstraint(columnNames = {"assignment_id", "student_id"}))
@Data
@Accessors(chain = true)
public class AssignmentSubmissionEntity {
    
    @Id
    @Column(name = "id")
    private String id;
    
    @Column(name = "assignment_id", nullable = false)
    private String assignmentId;
    
    @Column(name = "student_id", nullable = false)
    private String studentId;
    
    @Column(name = "classroom_id", nullable = false)
    private String classroomId;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private SubmissionStatus status;
    
    @Column(name = "total_marks")
    private int totalMarks;
    
    @Column(name = "obtained_marks")
    private int obtainedMarks;
    
    @Column(name = "percentage")
    private double percentage;
    
    @Column(name = "grade", length = 5)
    private String grade;
    
    @Column(name = "teacher_feedback", length = 2000)
    private String teacherFeedback;
    
    @Column(name = "submitted_date")
    private String submittedDate; // YYYY-MM-DD HH:mm:ss format
    
    @Column(name = "graded_date")
    private String gradedDate; // YYYY-MM-DD HH:mm:ss format
    
    @CreationTimestamp
    @Column(name = "created_date", nullable = false, updatable = false)
    private Instant createdDate;
    
    @UpdateTimestamp
    @Column(name = "updated_date", nullable = false)
    private Instant updatedDate;
    
    // Many-to-one relationship with assignment
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "assignment_id", insertable = false, updatable = false)
    private AssignmentEntity assignment;
    
    // One-to-many relationship with student answers
    @OneToMany(mappedBy = "submission", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<StudentAnswerEntity> answers;
}
