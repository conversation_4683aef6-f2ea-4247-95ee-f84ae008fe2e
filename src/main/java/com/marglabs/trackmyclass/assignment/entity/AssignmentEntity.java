package com.marglabs.trackmyclass.assignment.entity;

import com.marglabs.trackmyclass.assignment.model.AssignmentStatus;
import com.marglabs.trackmyclass.assignment.model.AssignmentType;
import jakarta.persistence.*;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.Instant;
import java.util.List;

@Entity
@Table(name = "assignments")
@Data
@Accessors(chain = true)
public class AssignmentEntity {
    
    @Id
    @Column(name = "id")
    private String id;
    
    @Column(name = "teacher_id", nullable = false)
    private String teacherId;
    
    @Column(name = "classroom_id", nullable = false)
    private String classroomId;
    
    @Column(name = "title", nullable = false, length = 200)
    private String title;
    
    @Column(name = "description", length = 1000)
    private String description;
    
    @Column(name = "subject", nullable = false, length = 100)
    private String subject;
    
    @Column(name = "topic", length = 200)
    private String topic;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "type", nullable = false)
    private AssignmentType type;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private AssignmentStatus status;
    
    @Column(name = "total_questions")
    private int totalQuestions;
    
    @Column(name = "total_marks")
    private int totalMarks;
    
    @Column(name = "due_date")
    private String dueDate; // YYYY-MM-DD format
    
    @Column(name = "is_active", nullable = false)
    private boolean isActive = true;
    
    @CreationTimestamp
    @Column(name = "created_date", nullable = false, updatable = false)
    private Instant createdDate;
    
    @UpdateTimestamp
    @Column(name = "updated_date", nullable = false)
    private Instant updatedDate;
    
    // One-to-many relationship with questions
    @OneToMany(mappedBy = "assignment", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<AssignmentQuestionEntity> questions;
    
    // One-to-many relationship with submissions
    @OneToMany(mappedBy = "assignment", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<AssignmentSubmissionEntity> submissions;
}
