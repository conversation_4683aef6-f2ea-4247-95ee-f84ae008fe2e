package com.marglabs.trackmyclass.assignment.entity;

import com.marglabs.trackmyclass.assignment.model.QuestionType;
import jakarta.persistence.*;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.Instant;
import java.util.List;

@Entity
@Table(name = "assignment_questions")
@Data
@Accessors(chain = true)
public class AssignmentQuestionEntity {
    
    @Id
    @Column(name = "id")
    private String id;
    
    @Column(name = "assignment_id", nullable = false)
    private String assignmentId;
    
    @Column(name = "question_number", nullable = false)
    private int questionNumber;
    
    @Column(name = "question_text", nullable = false, length = 2000)
    private String questionText;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "type", nullable = false)
    private QuestionType type;
    
    @Column(name = "marks", nullable = false)
    private int marks;
    
    @Column(name = "correct_answer", nullable = false, length = 1000)
    private String correctAnswer;
    
    @Column(name = "explanation", length = 2000)
    private String explanation;
    
    @Column(name = "is_ai_generated", nullable = false)
    private boolean isAiGenerated = false;
    
    @Column(name = "ai_model", length = 100)
    private String aiModel;
    
    @Column(name = "ai_prompt", length = 2000)
    private String aiPrompt;
    
    @CreationTimestamp
    @Column(name = "created_date", nullable = false, updatable = false)
    private Instant createdDate;
    
    @UpdateTimestamp
    @Column(name = "updated_date", nullable = false)
    private Instant updatedDate;
    
    // Many-to-one relationship with assignment
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "assignment_id", insertable = false, updatable = false)
    private AssignmentEntity assignment;
    
    // One-to-many relationship with question options
    @OneToMany(mappedBy = "question", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<QuestionOptionEntity> options;
    
    // One-to-many relationship with student answers
    @OneToMany(mappedBy = "question", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<StudentAnswerEntity> studentAnswers;
}
