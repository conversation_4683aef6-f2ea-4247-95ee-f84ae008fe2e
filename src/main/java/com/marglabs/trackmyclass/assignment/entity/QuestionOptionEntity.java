package com.marglabs.trackmyclass.assignment.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.Instant;

@Entity
@Table(name = "question_options")
@Data
@Accessors(chain = true)
public class QuestionOptionEntity {
    
    @Id
    @Column(name = "id")
    private String id;
    
    @Column(name = "question_id", nullable = false)
    private String questionId;
    
    @Column(name = "option_text", nullable = false, length = 500)
    private String optionText;
    
    @Column(name = "option_order", nullable = false)
    private int optionOrder;
    
    @CreationTimestamp
    @Column(name = "created_date", nullable = false, updatable = false)
    private Instant createdDate;
    
    @UpdateTimestamp
    @Column(name = "updated_date", nullable = false)
    private Instant updatedDate;
    
    // Many-to-one relationship with question
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "question_id", insertable = false, updatable = false)
    private AssignmentQuestionEntity question;
}
