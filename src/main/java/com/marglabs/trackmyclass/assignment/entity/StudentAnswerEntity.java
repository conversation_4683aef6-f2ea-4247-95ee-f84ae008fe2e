package com.marglabs.trackmyclass.assignment.entity;

import com.marglabs.trackmyclass.assignment.model.MarkingStatus;
import jakarta.persistence.*;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.Instant;

@Entity
@Table(name = "student_answers")
@Data
@Accessors(chain = true)
public class StudentAnswerEntity {
    
    @Id
    @Column(name = "id")
    private String id;
    
    @Column(name = "submission_id", nullable = false)
    private String submissionId;
    
    @Column(name = "question_id", nullable = false)
    private String questionId;
    
    @Column(name = "question_number", nullable = false)
    private int questionNumber;
    
    @Column(name = "student_answer", length = 2000)
    private String studentAnswer;
    
    @Column(name = "max_marks", nullable = false)
    private int maxMarks;
    
    @Column(name = "obtained_marks")
    private int obtainedMarks;
    
    @Column(name = "is_correct")
    private boolean isCorrect;
    
    @Column(name = "teacher_comment", length = 1000)
    private String teacherComment;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "marking_status", nullable = false)
    private MarkingStatus markingStatus = MarkingStatus.PENDING;
    
    @Column(name = "answered_date")
    private Instant answeredDate;
    
    @Column(name = "marked_date")
    private Instant markedDate;
    
    @CreationTimestamp
    @Column(name = "created_date", nullable = false, updatable = false)
    private Instant createdDate;
    
    @UpdateTimestamp
    @Column(name = "updated_date", nullable = false)
    private Instant updatedDate;
    
    // Many-to-one relationship with submission
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "submission_id", insertable = false, updatable = false)
    private AssignmentSubmissionEntity submission;
    
    // Many-to-one relationship with question
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "question_id", insertable = false, updatable = false)
    private AssignmentQuestionEntity question;
}
