package com.marglabs.trackmyclass.assignment.config;

import com.marglabs.trackmyclass.assignment.model.LLMProvider;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class LLMProviderConfig {
    
    private LLMProvider provider;
    private boolean enabled;
    private String apiUrl;
    private String apiKey;
    private String model;
    private int maxTokens;
    private double temperature;
    private int timeoutSeconds;
    private int retryAttempts;
    private String description;
    
    // Provider-specific configurations
    private String organizationId; // For OpenAI
    private String projectId; // For Google
    private String version; // For Anthropic
    private boolean useStreaming; // For various providers
    
    public LLMProviderConfig() {
        this.enabled = false;
        this.maxTokens = 2000;
        this.temperature = 0.7;
        this.timeoutSeconds = 30;
        this.retryAttempts = 3;
        this.useStreaming = false;
    }
    
    public static LLMProviderConfig createOpenAIConfig() {
        return new LLMProviderConfig()
                .setProvider(LLMProvider.OPENAI)
                .setApiUrl("https://api.openai.com/v1/chat/completions")
                .setModel("gpt-3.5-turbo")
                .setDescription("OpenAI GPT models for question generation");
    }
    
    public static LLMProviderConfig createGroqConfig() {
        return new LLMProviderConfig()
                .setProvider(LLMProvider.GROQ)
                .setApiUrl("https://api.groq.com/openai/v1/chat/completions")
                .setModel("mixtral-8x7b-32768")
                .setDescription("Groq fast inference for question generation");
    }
    
    public static LLMProviderConfig createAnthropicConfig() {
        return new LLMProviderConfig()
                .setProvider(LLMProvider.ANTHROPIC)
                .setApiUrl("https://api.anthropic.com/v1/messages")
                .setModel("claude-3-sonnet-20240229")
                .setVersion("2023-06-01")
                .setDescription("Anthropic Claude for advanced question generation");
    }
    
    public static LLMProviderConfig createGoogleConfig() {
        return new LLMProviderConfig()
                .setProvider(LLMProvider.GOOGLE)
                .setApiUrl("https://generativelanguage.googleapis.com/v1beta/models")
                .setModel("gemini-2.0-flash") //gemini-2.0-flash, gemini-pro
                .setDescription("Google Gemini for question generation");
    }
    
    public static LLMProviderConfig createHuggingFaceConfig() {
        return new LLMProviderConfig()
                .setProvider(LLMProvider.HUGGINGFACE)
                .setApiUrl("https://api-inference.huggingface.co/models")
                .setModel("microsoft/DialoGPT-medium")
                .setDescription("Hugging Face models for question generation");
    }
    
    public static LLMProviderConfig createOllamaConfig() {
        return new LLMProviderConfig()
                .setProvider(LLMProvider.OLLAMA)
                .setApiUrl("http://localhost:11434/api/generate")
                .setModel("deepseek-r1:latest")
                .setMaxTokens(4000)
                .setTemperature(0.3)
                .setTimeoutSeconds(60)
                .setDescription("Local Ollama with DeepSeek models for question generation");
    }
}
