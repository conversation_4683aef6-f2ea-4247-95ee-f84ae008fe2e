package com.marglabs.trackmyclass.assignment.rest.controller;

import com.marglabs.trackmyclass.assignment.model.*;
import com.marglabs.trackmyclass.assignment.service.AssignmentService;
import com.marglabs.trackmyclass.user.model.RoleType;
import com.marglabs.trackmyclass.user.model.User;
import com.marglabs.trackmyclass.user.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/assignment/v1")
@Tag(name = "Assignment Management", description = "APIs for managing assignments with AI-powered question generation and marking")
public class AssignmentController {
    
    @Autowired
    private AssignmentService assignmentService;
    
    @Autowired
    private UserService userService;
    
    @Operation(summary = "Create assignment", 
               description = "Create a new assignment with manual questions or AI-generated questions")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Assignment created successfully",
                content = @Content(schema = @Schema(implementation = Assignment.class))),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Only teachers can create assignments")
    })
    @PostMapping(value = "/assignments", consumes = "application/json", produces = "application/json")
    @ResponseStatus(HttpStatus.CREATED)
    public Assignment createAssignment(
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal,
            @Parameter(description = "Assignment creation details") @Valid @RequestBody AssignmentCreationRequest request) {
        
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());
        verifyTeacherRole(teacher);
        
        return assignmentService.createAssignment(request, teacher.getId());
    }
    
    @Operation(summary = "Get assignment by ID", description = "Get assignment details including questions")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Assignment retrieved successfully",
                content = @Content(schema = @Schema(implementation = Assignment.class))),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "404", description = "Assignment not found")
    })
    @GetMapping(value = "/assignments/{assignmentId}", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public Assignment getAssignment(
            @Parameter(description = "Assignment ID") @PathVariable String assignmentId,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());
        return assignmentService.getAssignmentById(assignmentId, user.getId());
    }
    
    @Operation(summary = "Get teacher's assignments", description = "Get all assignments created by the authenticated teacher")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Assignments retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Only teachers can access this endpoint")
    })
    @GetMapping(value = "/teacher/assignments", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<Assignment> getTeacherAssignments(@Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());
        verifyTeacherRole(teacher);
        
        return assignmentService.getAssignmentsByTeacher(teacher.getId());
    }
    
    @Operation(summary = "Get classroom assignments", description = "Get all assignments for a specific classroom")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Assignments retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Only teachers can access this endpoint")
    })
    @GetMapping(value = "/teacher/classroom/{classroomId}/assignments", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<Assignment> getClassroomAssignments(
            @Parameter(description = "Classroom ID") @PathVariable String classroomId,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());
        verifyTeacherRole(teacher);
        
        return assignmentService.getAssignmentsByClassroom(classroomId, teacher.getId());
    }
    
    @Operation(summary = "Update assignment status", description = "Update assignment status (publish, complete, etc.)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Assignment status updated successfully",
                content = @Content(schema = @Schema(implementation = Assignment.class))),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Only teachers can update assignments"),
        @ApiResponse(responseCode = "404", description = "Assignment not found")
    })
    @PutMapping(value = "/assignments/{assignmentId}/status", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public Assignment updateAssignmentStatus(
            @Parameter(description = "Assignment ID") @PathVariable String assignmentId,
            @Parameter(description = "New status") @RequestParam AssignmentStatus status,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());
        verifyTeacherRole(teacher);
        
        return assignmentService.updateAssignmentStatus(assignmentId, status, teacher.getId());
    }
    
    @Operation(summary = "Delete assignment", description = "Delete an assignment and all its submissions")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "Assignment deleted successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Only teachers can delete assignments"),
        @ApiResponse(responseCode = "404", description = "Assignment not found")
    })
    @DeleteMapping(value = "/assignments/{assignmentId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void deleteAssignment(
            @Parameter(description = "Assignment ID") @PathVariable String assignmentId,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());
        verifyTeacherRole(teacher);
        
        assignmentService.deleteAssignment(assignmentId, teacher.getId());
    }
    
    @Operation(summary = "Get assignment submissions", description = "Get all submissions for an assignment")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Submissions retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Only teachers can view submissions")
    })
    @GetMapping(value = "/assignments/{assignmentId}/submissions", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<AssignmentSubmission> getAssignmentSubmissions(
            @Parameter(description = "Assignment ID") @PathVariable String assignmentId,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());
        verifyTeacherRole(teacher);
        
        return assignmentService.getAssignmentSubmissions(assignmentId, teacher.getId());
    }
    
    @Operation(summary = "Mark assignment submission", 
               description = "Mark student answers as correct/incorrect and provide feedback")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Submission marked successfully",
                content = @Content(schema = @Schema(implementation = AssignmentSubmission.class))),
        @ApiResponse(responseCode = "400", description = "Invalid marking data"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Only teachers can mark submissions")
    })
    @PostMapping(value = "/submissions/mark", consumes = "application/json", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public AssignmentSubmission markSubmission(
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal,
            @Parameter(description = "Marking details") @Valid @RequestBody MarkingRequest request) {
        
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());
        verifyTeacherRole(teacher);
        
        return assignmentService.markSubmission(request, teacher.getId());
    }
    
    @Operation(summary = "Get student assignments", description = "Get assignments available to the authenticated student")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Student assignments retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/student/assignments", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<Assignment> getStudentAssignments(@Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        User student = userService.getUserByCognitoId(userPrincipal.getSubject());
        return assignmentService.getStudentAssignments(student.getId());
    }
    
    @Operation(summary = "Get student submissions", description = "Get all submissions by the authenticated student")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Student submissions retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/student/submissions", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<AssignmentSubmission> getStudentSubmissions(@Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        User student = userService.getUserByCognitoId(userPrincipal.getSubject());
        return assignmentService.getStudentSubmissions(student.getId());
    }
    
    private void verifyTeacherRole(User user) {
        boolean isTeacher = user.getRoles().stream()
                .anyMatch(role -> role.getRole() == RoleType.TEACHER);
        
        if (!isTeacher) {
            throw new AccessDeniedException("Only teachers can perform this action");
        }
    }
}
