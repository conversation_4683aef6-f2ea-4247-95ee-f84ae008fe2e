package com.marglabs.trackmyclass.assignment.rest.controller;

import com.marglabs.trackmyclass.assignment.config.LLMProviderConfig;
import com.marglabs.trackmyclass.assignment.model.LLMProvider;
import com.marglabs.trackmyclass.assignment.service.LLMService;
import com.marglabs.trackmyclass.assignment.service.llm.OllamaProviderService;
import com.marglabs.trackmyclass.user.model.RoleType;
import com.marglabs.trackmyclass.user.model.User;
import com.marglabs.trackmyclass.user.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/assignment/v1/llm")
@Tag(name = "LLM Management", description = "APIs for managing LLM providers and configurations")
public class LLMManagementController {
    
    @Autowired
    private LLMService llmService;

    @Autowired
    private UserService userService;

    @Autowired
    private OllamaProviderService ollamaProviderService;
    
    @Operation(summary = "Get LLM provider status", 
               description = "Get status and configuration of all LLM providers")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Provider status retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Only teachers can access LLM management")
    })
    @GetMapping(value = "/providers/status", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public Map<String, Object> getProviderStatus(@Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());
        verifyTeacherRole(teacher);
        
        return llmService.getProviderStatus();
    }
    
    @Operation(summary = "Get available providers", 
               description = "Get list of currently available and enabled LLM providers")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Available providers retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Only teachers can access LLM management")
    })
    @GetMapping(value = "/providers/available", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<LLMProvider> getAvailableProviders(@Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());
        verifyTeacherRole(teacher);
        
        return llmService.getAvailableProviders();
    }
    
    @Operation(summary = "Get provider configuration", 
               description = "Get configuration details for a specific LLM provider")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Provider configuration retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Only teachers can access LLM management"),
        @ApiResponse(responseCode = "404", description = "Provider not found")
    })
    @GetMapping(value = "/providers/{provider}/config", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public LLMProviderConfig getProviderConfig(
            @Parameter(description = "LLM Provider") @PathVariable LLMProvider provider,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());
        verifyTeacherRole(teacher);
        
        LLMProviderConfig config = llmService.getProviderConfig(provider);
        if (config != null) {
            // Hide sensitive information like API keys
            config = new LLMProviderConfig()
                    .setProvider(config.getProvider())
                    .setEnabled(config.isEnabled())
                    .setApiUrl(config.getApiUrl())
                    .setModel(config.getModel())
                    .setMaxTokens(config.getMaxTokens())
                    .setTemperature(config.getTemperature())
                    .setDescription(config.getDescription());
        }
        
        return config;
    }
    
    @Operation(summary = "Enable/disable provider", 
               description = "Enable or disable a specific LLM provider")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Provider status updated successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Only teachers can manage LLM providers"),
        @ApiResponse(responseCode = "404", description = "Provider not found")
    })
    @PutMapping(value = "/providers/{provider}/enabled", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public Map<String, Object> setProviderEnabled(
            @Parameter(description = "LLM Provider") @PathVariable LLMProvider provider,
            @Parameter(description = "Enable/disable provider") @RequestParam boolean enabled,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());
        verifyTeacherRole(teacher);
        
        llmService.setProviderEnabled(provider, enabled);
        
        return Map.of(
            "provider", provider.getDisplayName(),
            "enabled", enabled,
            "message", (enabled ? "Enabled" : "Disabled") + " provider: " + provider.getDisplayName()
        );
    }
    
    @Operation(summary = "Set primary provider", 
               description = "Set the primary LLM provider for question generation")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Primary provider updated successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Only teachers can manage LLM providers"),
        @ApiResponse(responseCode = "400", description = "Provider not available")
    })
    @PutMapping(value = "/providers/{provider}/primary", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public Map<String, Object> setPrimaryProvider(
            @Parameter(description = "LLM Provider") @PathVariable LLMProvider provider,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());
        verifyTeacherRole(teacher);
        
        // Check if provider is available
        List<LLMProvider> availableProviders = llmService.getAvailableProviders();
        if (!availableProviders.contains(provider)) {
            throw new IllegalArgumentException("Provider " + provider.getDisplayName() + " is not available. Please enable and configure it first.");
        }
        
        llmService.setPrimaryProvider(provider);
        
        return Map.of(
            "primaryProvider", provider.getDisplayName(),
            "message", "Set primary provider to: " + provider.getDisplayName()
        );
    }
    
    @Operation(summary = "Test provider", 
               description = "Test a specific LLM provider with a sample question generation")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Provider test completed"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Only teachers can test LLM providers"),
        @ApiResponse(responseCode = "400", description = "Provider test failed")
    })
    @PostMapping(value = "/providers/{provider}/test", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public Map<String, Object> testProvider(
            @Parameter(description = "LLM Provider") @PathVariable LLMProvider provider,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());
        verifyTeacherRole(teacher);
        
        try {
            // Create a simple test request
            com.marglabs.trackmyclass.assignment.model.AIGenerationRequest testRequest = 
                new com.marglabs.trackmyclass.assignment.model.AIGenerationRequest()
                    .setTopic("Basic Mathematics")
                    .setDifficultyLevel("EASY")
                    .setNumberOfQuestions(1)
                    .setQuestionTypes(List.of(com.marglabs.trackmyclass.assignment.model.QuestionType.SHORT_ANSWER))
                    .setMarksPerQuestion(1);
            
            // Test the provider
            var questions = llmService.generateWithProvider(testRequest, "test-assignment", provider);
            
            return Map.of(
                "provider", provider.getDisplayName(),
                "status", "SUCCESS",
                "questionsGenerated", questions.size(),
                "message", "Provider test successful"
            );
            
        } catch (Exception e) {
            return Map.of(
                "provider", provider.getDisplayName(),
                "status", "FAILED",
                "error", e.getMessage(),
                "message", "Provider test failed"
            );
        }
    }
    
    private void verifyTeacherRole(User user) {
        boolean isTeacher = user.getRoles().stream()
                .anyMatch(role -> role.getRole() == RoleType.TEACHER);
        
        if (!isTeacher) {
            throw new AccessDeniedException("Only teachers can access LLM management features");
        }
    }

    @Operation(summary = "Get Ollama available models",
               description = "Get list of models available in the local Ollama instance")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Available models retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Only teachers can access LLM management"),
        @ApiResponse(responseCode = "503", description = "Ollama service not available")
    })
    @GetMapping(value = "/ollama/models", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<String> getOllamaAvailableModels(@Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());
        verifyTeacherRole(teacher);

        LLMProviderConfig ollamaConfig = llmService.getProviderConfig(LLMProvider.OLLAMA);
        if (ollamaConfig == null) {
            throw new RuntimeException("Ollama configuration not found");
        }

        return ollamaProviderService.getAvailableModels(ollamaConfig);
    }

    @Operation(summary = "Get Ollama recommended models",
               description = "Get list of recommended DeepSeek and other models for Ollama")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Recommended models retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Only teachers can access LLM management")
    })
    @GetMapping(value = "/ollama/recommended-models", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public Map<String, Object> getOllamaRecommendedModels(@Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());
        verifyTeacherRole(teacher);

        List<String> recommendedModels = ollamaProviderService.getRecommendedModels();

        return Map.of(
            "recommendedModels", recommendedModels,
            "deepSeekModels", List.of(
                "deepseek-coder:1.3b",
                "deepseek-coder:6.7b",
                "deepseek-coder:33b",
                "deepseek-llm:7b",
                "deepseek-llm:67b"
            ),
            "otherModels", List.of(
                "llama3:8b",
                "llama3:70b",
                "mistral:7b",
                "codellama:7b",
                "phi3:3.8b"
            ),
            "setupInstructions", Map.of(
                "install", "Visit https://ollama.ai/ to install Ollama",
                "pullModel", "Run: ollama pull deepseek-coder:6.7b",
                "startService", "Run: ollama serve",
                "testModel", "Run: ollama run deepseek-coder:6.7b \"Hello\""
            )
        );
    }
}
