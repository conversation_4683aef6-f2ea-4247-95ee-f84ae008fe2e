package com.marglabs.trackmyclass.assignment.repository;

import com.marglabs.trackmyclass.assignment.entity.AssignmentSubmissionEntity;
import com.marglabs.trackmyclass.assignment.model.SubmissionStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface AssignmentSubmissionRepository extends JpaRepository<AssignmentSubmissionEntity, String> {
    
    // Find submissions by assignment
    List<AssignmentSubmissionEntity> findByAssignmentIdOrderByCreatedDateDesc(String assignmentId);
    
    // Find submissions by student
    List<AssignmentSubmissionEntity> findByStudentIdOrderByCreatedDateDesc(String studentId);
    
    // Find submission by assignment and student
    Optional<AssignmentSubmissionEntity> findByAssignmentIdAndStudentId(String assignmentId, String studentId);
    
    // Find submissions by status
    List<AssignmentSubmissionEntity> findByStatusOrderByCreatedDateDesc(SubmissionStatus status);
    
    // Find submissions by classroom
    List<AssignmentSubmissionEntity> findByClassroomIdOrderByCreatedDateDesc(String classroomId);
    
    // Find submissions by assignment and status
    List<AssignmentSubmissionEntity> findByAssignmentIdAndStatusOrderByCreatedDateDesc(String assignmentId, SubmissionStatus status);
    
    // Count submissions by assignment
    long countByAssignmentId(String assignmentId);
    
    // Count submissions by student
    long countByStudentId(String studentId);
    
    // Count submissions by status
    long countByStatus(SubmissionStatus status);
    
    // Count graded submissions by assignment
    long countByAssignmentIdAndStatus(String assignmentId, SubmissionStatus status);
    
    // Get average score for assignment
    @Query("SELECT AVG(s.percentage) FROM AssignmentSubmissionEntity s WHERE s.assignmentId = :assignmentId AND s.status = 'GRADED'")
    Double getAverageScoreByAssignmentId(@Param("assignmentId") String assignmentId);
    
    // Get submission statistics by assignment
    @Query("SELECT s.assignmentId, COUNT(s), AVG(s.percentage), MAX(s.percentage), MIN(s.percentage) FROM AssignmentSubmissionEntity s WHERE s.status = 'GRADED' GROUP BY s.assignmentId")
    List<Object[]> getSubmissionStatsByAssignment();
    
    // Find top performers by assignment
    @Query("SELECT s FROM AssignmentSubmissionEntity s WHERE s.assignmentId = :assignmentId AND s.status = 'GRADED' ORDER BY s.percentage DESC")
    List<AssignmentSubmissionEntity> findTopPerformersByAssignment(@Param("assignmentId") String assignmentId);
    
    // Find submissions needing grading
    @Query("SELECT s FROM AssignmentSubmissionEntity s WHERE s.status = 'SUBMITTED' ORDER BY s.submittedDate ASC")
    List<AssignmentSubmissionEntity> findSubmissionsNeedingGrading();
    
    // Find overdue submissions
    @Query("SELECT s FROM AssignmentSubmissionEntity s JOIN s.assignment a WHERE s.status = 'DRAFT' AND a.dueDate < :currentDate")
    List<AssignmentSubmissionEntity> findOverdueSubmissions(@Param("currentDate") String currentDate);
}
