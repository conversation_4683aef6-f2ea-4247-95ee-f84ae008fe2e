package com.marglabs.trackmyclass.assignment.repository;

import com.marglabs.trackmyclass.assignment.entity.StudentAnswerEntity;
import com.marglabs.trackmyclass.assignment.model.MarkingStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface StudentAnswerRepository extends JpaRepository<StudentAnswerEntity, String> {
    
    // Find answers by submission
    List<StudentAnswerEntity> findBySubmissionIdOrderByQuestionNumber(String submissionId);
    
    // Find answers by question
    List<StudentAnswerEntity> findByQuestionIdOrderByCreatedDateDesc(String questionId);
    
    // Find answers by marking status
    List<StudentAnswerEntity> findByMarkingStatusOrderByCreatedDateDesc(MarkingStatus markingStatus);
    
    // Find answers needing marking
    List<StudentAnswerEntity> findByMarkingStatusOrderByAnsweredDateAsc(MarkingStatus markingStatus);
    
    // Count answers by submission
    long countBySubmissionId(String submissionId);
    
    // Count correct answers by submission
    long countBySubmissionIdAndIsCorrectTrue(String submissionId);
    
    // Get total marks by submission
    @Query("SELECT SUM(a.obtainedMarks) FROM StudentAnswerEntity a WHERE a.submissionId = :submissionId")
    Integer getTotalMarksBySubmissionId(@Param("submissionId") String submissionId);
    
    // Get max possible marks by submission
    @Query("SELECT SUM(a.maxMarks) FROM StudentAnswerEntity a WHERE a.submissionId = :submissionId")
    Integer getMaxMarksBySubmissionId(@Param("submissionId") String submissionId);
    
    // Find answers by question and correct status
    List<StudentAnswerEntity> findByQuestionIdAndIsCorrect(String questionId, boolean isCorrect);
    
    // Get answer statistics by question
    @Query("SELECT a.questionId, COUNT(a), SUM(CASE WHEN a.isCorrect = true THEN 1 ELSE 0 END), AVG(a.obtainedMarks) FROM StudentAnswerEntity a GROUP BY a.questionId")
    List<Object[]> getAnswerStatsByQuestion();
}
