package com.marglabs.trackmyclass.assignment.repository;

import com.marglabs.trackmyclass.assignment.entity.AssignmentEntity;
import com.marglabs.trackmyclass.assignment.model.AssignmentStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface AssignmentRepository extends JpaRepository<AssignmentEntity, String> {
    
    // Find assignments by teacher
    List<AssignmentEntity> findByTeacherIdAndIsActiveTrueOrderByCreatedDateDesc(String teacherId);
    
    // Find assignments by classroom
    List<AssignmentEntity> findByClassroomIdAndIsActiveTrueOrderByCreatedDateDesc(String classroomId);
    
    // Find assignments by teacher and classroom
    List<AssignmentEntity> findByTeacherIdAndClassroomIdAndIsActiveTrueOrderByCreatedDateDesc(String teacherId, String classroomId);
    
    // Find assignments by status
    List<AssignmentEntity> findByStatusAndIsActiveTrueOrderByCreatedDateDesc(AssignmentStatus status);
    
    // Find assignments by teacher and status
    List<AssignmentEntity> findByTeacherIdAndStatusAndIsActiveTrueOrderByCreatedDateDesc(String teacherId, AssignmentStatus status);
    
    // Find published assignments for students (via enrollment)
    @Query("SELECT a FROM AssignmentEntity a WHERE a.classroomId IN :classroomIds AND a.status = 'PUBLISHED' AND a.isActive = true ORDER BY a.createdDate DESC")
    List<AssignmentEntity> findPublishedAssignmentsByClassrooms(@Param("classroomIds") List<String> classroomIds);
    
    // Find assignment by ID and teacher (for authorization)
    Optional<AssignmentEntity> findByIdAndTeacherIdAndIsActiveTrue(String id, String teacherId);
    
    // Count assignments by teacher
    long countByTeacherIdAndIsActiveTrue(String teacherId);
    
    // Count assignments by classroom
    long countByClassroomIdAndIsActiveTrue(String classroomId);
    
    // Count assignments by status
    long countByStatusAndIsActiveTrue(AssignmentStatus status);
    
    // Find assignments due today or overdue
    @Query("SELECT a FROM AssignmentEntity a WHERE a.dueDate <= :today AND a.status = 'PUBLISHED' AND a.isActive = true")
    List<AssignmentEntity> findDueOrOverdueAssignments(@Param("today") String today);
    
    // Find assignments by subject
    List<AssignmentEntity> findBySubjectAndIsActiveTrueOrderByCreatedDateDesc(String subject);
    
    // Find assignments by topic
    List<AssignmentEntity> findByTopicContainingIgnoreCaseAndIsActiveTrueOrderByCreatedDateDesc(String topic);
    
    // Search assignments by title or description
    @Query("SELECT a FROM AssignmentEntity a WHERE (LOWER(a.title) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR LOWER(a.description) LIKE LOWER(CONCAT('%', :searchTerm, '%'))) AND a.isActive = true ORDER BY a.createdDate DESC")
    List<AssignmentEntity> searchAssignments(@Param("searchTerm") String searchTerm);
    
    // Get assignment statistics
    @Query("SELECT a.teacherId, COUNT(a), AVG(a.totalMarks) FROM AssignmentEntity a WHERE a.isActive = true GROUP BY a.teacherId")
    List<Object[]> getAssignmentStatsByTeacher();
}
