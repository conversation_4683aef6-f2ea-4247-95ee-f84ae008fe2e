package com.marglabs.trackmyclass.assignment.repository;

import com.marglabs.trackmyclass.assignment.entity.AssignmentQuestionEntity;
import com.marglabs.trackmyclass.assignment.model.QuestionType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AssignmentQuestionRepository extends JpaRepository<AssignmentQuestionEntity, String> {
    
    // Find questions by assignment
    List<AssignmentQuestionEntity> findByAssignmentIdOrderByQuestionNumber(String assignmentId);
    
    // Find questions by type
    List<AssignmentQuestionEntity> findByTypeOrderByCreatedDateDesc(QuestionType type);
    
    // Find AI-generated questions
    List<AssignmentQuestionEntity> findByIsAiGeneratedTrueOrderByCreatedDateDesc();
    
    // Find questions by AI model
    List<AssignmentQuestionEntity> findByAiModelOrderByCreatedDateDesc(String aiModel);
    
    // Count questions by assignment
    long countByAssignmentId(String assignmentId);
    
    // Get total marks for assignment
    @Query("SELECT SUM(q.marks) FROM AssignmentQuestionEntity q WHERE q.assignmentId = :assignmentId")
    Integer getTotalMarksByAssignmentId(@Param("assignmentId") String assignmentId);
    
    // Find questions by assignment and type
    List<AssignmentQuestionEntity> findByAssignmentIdAndTypeOrderByQuestionNumber(String assignmentId, QuestionType type);
    
    // Search questions by text
    @Query("SELECT q FROM AssignmentQuestionEntity q WHERE LOWER(q.questionText) LIKE LOWER(CONCAT('%', :searchTerm, '%')) ORDER BY q.createdDate DESC")
    List<AssignmentQuestionEntity> searchQuestionsByText(@Param("searchTerm") String searchTerm);
}
