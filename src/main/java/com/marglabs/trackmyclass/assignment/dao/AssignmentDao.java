package com.marglabs.trackmyclass.assignment.dao;

import com.marglabs.common.rest.error.GeneralException;
import com.marglabs.trackmyclass.assignment.entity.AssignmentEntity;
import com.marglabs.trackmyclass.assignment.model.AssignmentStatus;
import com.marglabs.trackmyclass.assignment.repository.AssignmentRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

@Component
public class AssignmentDao {
    
    @Autowired
    private AssignmentRepository repository;
    
    public AssignmentEntity save(AssignmentEntity entity) {
        return repository.save(entity);
    }
    
    public AssignmentEntity getById(String id) {
        return repository.findById(id)
                .orElseThrow(() -> new GeneralException(HttpStatus.NOT_FOUND, "ASSIGNMENT", "ASSIGNMENT_NOT_FOUND",
                        "assignment_not_found", id));
    }
    
    public Optional<AssignmentEntity> findById(String id) {
        return repository.findById(id);
    }
    
    public AssignmentEntity getByIdAndTeacher(String id, String teacherId) {
        return repository.findByIdAndTeacherIdAndIsActiveTrue(id, teacherId)
                .orElseThrow(() -> new GeneralException(HttpStatus.NOT_FOUND, "ASSIGNMENT", "ASSIGNMENT_NOT_FOUND",
                        "assignment_not_found_for_teacher", id, teacherId));
    }
    
    public List<AssignmentEntity> getByTeacherId(String teacherId) {
        return repository.findByTeacherIdAndIsActiveTrueOrderByCreatedDateDesc(teacherId);
    }
    
    public List<AssignmentEntity> getByClassroomId(String classroomId) {
        return repository.findByClassroomIdAndIsActiveTrueOrderByCreatedDateDesc(classroomId);
    }
    
    public List<AssignmentEntity> getByTeacherAndClassroom(String teacherId, String classroomId) {
        return repository.findByTeacherIdAndClassroomIdAndIsActiveTrueOrderByCreatedDateDesc(teacherId, classroomId);
    }
    
    public List<AssignmentEntity> getByStatus(AssignmentStatus status) {
        return repository.findByStatusAndIsActiveTrueOrderByCreatedDateDesc(status);
    }
    
    public List<AssignmentEntity> getByTeacherAndStatus(String teacherId, AssignmentStatus status) {
        return repository.findByTeacherIdAndStatusAndIsActiveTrueOrderByCreatedDateDesc(teacherId, status);
    }
    
    public List<AssignmentEntity> getPublishedAssignmentsByClassrooms(List<String> classroomIds) {
        return repository.findPublishedAssignmentsByClassrooms(classroomIds);
    }
    
    public long countByTeacherId(String teacherId) {
        return repository.countByTeacherIdAndIsActiveTrue(teacherId);
    }
    
    public long countByClassroomId(String classroomId) {
        return repository.countByClassroomIdAndIsActiveTrue(classroomId);
    }
    
    public long countByStatus(AssignmentStatus status) {
        return repository.countByStatusAndIsActiveTrue(status);
    }
    
    public List<AssignmentEntity> getDueOrOverdueAssignments(String today) {
        return repository.findDueOrOverdueAssignments(today);
    }
    
    public List<AssignmentEntity> getBySubject(String subject) {
        return repository.findBySubjectAndIsActiveTrueOrderByCreatedDateDesc(subject);
    }
    
    public List<AssignmentEntity> getByTopic(String topic) {
        return repository.findByTopicContainingIgnoreCaseAndIsActiveTrueOrderByCreatedDateDesc(topic);
    }
    
    public List<AssignmentEntity> searchAssignments(String searchTerm) {
        return repository.searchAssignments(searchTerm);
    }
    
    public List<Object[]> getAssignmentStatsByTeacher() {
        return repository.getAssignmentStatsByTeacher();
    }
    
    public AssignmentEntity update(AssignmentEntity entity) {
        return repository.save(entity);
    }
    
    public void deleteById(String id) {
        AssignmentEntity entity = getById(id);
        entity.setActive(false);
        repository.save(entity);
    }
    
    public void hardDeleteById(String id) {
        repository.deleteById(id);
    }
    
    public boolean existsById(String id) {
        return repository.existsById(id);
    }
}
