package com.marglabs.trackmyclass.assignment.model;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class AIGenerationRequest {
    
    @NotBlank(message = "Topic is required for AI generation")
    private String topic;
    
    @NotBlank(message = "Difficulty level is required")
    private String difficultyLevel; // EASY, MEDIUM, HARD
    
    @Min(value = 1, message = "Number of questions must be at least 1")
    @Max(value = 50, message = "Number of questions cannot exceed 50")
    private int numberOfQuestions;
    
    @NotNull(message = "Question types are required")
    private List<QuestionType> questionTypes;
    
    private String additionalInstructions;
    
    private String gradeLevel; // e.g., "Grade 10", "High School", "College"
    
    private int marksPerQuestion = 1; // Default marks per question
}
