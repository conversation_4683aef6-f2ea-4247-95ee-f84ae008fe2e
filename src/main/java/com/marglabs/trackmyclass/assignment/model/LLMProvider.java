package com.marglabs.trackmyclass.assignment.model;

public enum LLMProvider {
    OPENAI("OpenAI", "https://api.openai.com/v1/chat/completions"),
    GROQ("Groq", "https://api.groq.com/openai/v1/chat/completions"),
    ANTHROPIC("Anthropic Claude", "https://api.anthropic.com/v1/messages"),
    GOOGLE("Google Gemini", "https://generativelanguage.googleapis.com/v1beta/models"),
    HUGGINGFACE("Hugging Face", "https://api-inference.huggingface.co/models"),
    OLLAMA("Ollama", "http://localhost:11434/api/generate");
    
    private final String displayName;
    private final String defaultApiUrl;
    
    LLMProvider(String displayName, String defaultApiUrl) {
        this.displayName = displayName;
        this.defaultApiUrl = defaultApiUrl;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public String getDefaultApiUrl() {
        return defaultApiUrl;
    }
}
