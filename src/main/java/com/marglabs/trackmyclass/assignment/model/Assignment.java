package com.marglabs.trackmyclass.assignment.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class Assignment {
    
    private String id;
    private String teacherId;
    private String teacherName;
    private String classroomId;
    private String className;
    private String title;
    private String description;
    private String subject;
    private String topic;
    private AssignmentType type; // MANUAL, AI_GENERATED
    private AssignmentStatus status; // DRAFT, PUBLISHED, COMPLETED, GRADED
    private int totalQuestions;
    private int totalMarks;
    private String dueDate; // YYYY-MM-DD format
    private boolean isActive;
    private long createdDate;
    private long updatedDate;
    
    // Assignment questions
    private List<AssignmentQuestion> questions;
    
    // Assignment statistics
    private int totalSubmissions;
    private int gradedSubmissions;
    private double averageScore;
}
