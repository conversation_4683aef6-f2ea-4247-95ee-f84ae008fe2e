package com.marglabs.trackmyclass.assignment.model;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class MarkingRequest {
    
    @NotBlank(message = "Submission ID is required")
    private String submissionId;
    
    private List<AnswerMarking> answerMarkings;
    
    private String overallFeedback;
    
    @Data
    @Accessors(chain = true)
    public static class AnswerMarking {
        
        @NotBlank(message = "Answer ID is required")
        private String answerId;
        
        @Min(value = 0, message = "Obtained marks cannot be negative")
        private int obtainedMarks;
        
        private boolean isCorrect;
        
        private String teacherComment;
    }
}
