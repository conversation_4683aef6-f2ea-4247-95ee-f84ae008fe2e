package com.marglabs.trackmyclass.assignment.model;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class StudentAnswer {
    
    private String id;
    private String submissionId;
    private String questionId;
    private int questionNumber;
    private String questionText;
    private String studentAnswer;
    private String correctAnswer;
    private int maxMarks;
    private int obtainedMarks;
    private boolean isCorrect;
    private String teacherComment;
    private MarkingStatus markingStatus; // PENDING, MARKED, AUTO_MARKED
    private long answeredDate;
    private long markedDate;
}
