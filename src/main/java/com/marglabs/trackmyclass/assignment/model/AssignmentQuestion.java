package com.marglabs.trackmyclass.assignment.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class AssignmentQuestion {
    
    private String id;
    private String assignmentId;
    private int questionNumber;
    private String questionText;
    private QuestionType type; // MULTIPLE_CHOICE, SHORT_ANSWER, LONG_ANSWER, TRUE_FALSE
    private int marks;
    private String correctAnswer;
    private String explanation;
    private boolean isAiGenerated;
    private long createdDate;
    private long updatedDate;
    
    // For multiple choice questions
    private List<String> options;
    
    // For AI generation context
    private String aiPrompt;
    private String aiModel;
}
