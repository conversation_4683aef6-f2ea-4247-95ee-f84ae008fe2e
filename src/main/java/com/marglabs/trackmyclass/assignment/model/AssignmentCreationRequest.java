package com.marglabs.trackmyclass.assignment.model;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class AssignmentCreationRequest {
    
    @NotBlank(message = "Classroom ID is required")
    private String classroomId;
    
    @NotBlank(message = "Assignment title is required")
    @Size(min = 3, max = 200, message = "Title must be between 3 and 200 characters")
    private String title;
    
    @Size(max = 1000, message = "Description cannot exceed 1000 characters")
    private String description;
    
    @NotBlank(message = "Subject is required")
    @Size(max = 100, message = "Subject cannot exceed 100 characters")
    private String subject;
    
    @Size(max = 200, message = "Topic cannot exceed 200 characters")
    private String topic;
    
    @NotNull(message = "Assignment type is required")
    private AssignmentType type;
    
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "Due date must be in YYYY-MM-DD format")
    private String dueDate;
    
    // For manual assignments
    private List<QuestionCreationRequest> questions;
    
    // For AI-generated assignments
    private AIGenerationRequest aiRequest;
}
