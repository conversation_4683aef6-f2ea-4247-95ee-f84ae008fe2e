package com.marglabs.trackmyclass.assignment.model;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class QuestionCreationRequest {
    
    @NotBlank(message = "Question text is required")
    private String questionText;
    
    @NotNull(message = "Question type is required")
    private QuestionType type;
    
    @Min(value = 1, message = "Marks must be at least 1")
    private int marks;
    
    @NotBlank(message = "Correct answer is required")
    private String correctAnswer;
    
    private String explanation;
    
    // For multiple choice questions
    private List<String> options;
}
