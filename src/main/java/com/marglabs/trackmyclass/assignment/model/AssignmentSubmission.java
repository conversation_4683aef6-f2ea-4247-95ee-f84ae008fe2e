package com.marglabs.trackmyclass.assignment.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class AssignmentSubmission {
    
    private String id;
    private String assignmentId;
    private String assignmentTitle;
    private String studentId;
    private String studentName;
    private String classroomId;
    private SubmissionStatus status; // DRAFT, SUBMITTED, GRADED
    private int totalMarks;
    private int obtainedMarks;
    private double percentage;
    private String grade; // A, B, C, D, F
    private String teacherFeedback;
    private String submittedDate; // YYYY-MM-DD HH:mm:ss format
    private String gradedDate; // YYYY-MM-DD HH:mm:ss format
    private long createdDate;
    private long updatedDate;
    
    // Student answers
    private List<StudentAnswer> answers;
}
