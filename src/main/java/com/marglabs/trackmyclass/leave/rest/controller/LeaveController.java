package com.marglabs.trackmyclass.leave.rest.controller;

import com.marglabs.trackmyclass.leave.model.Leave;
import com.marglabs.trackmyclass.leave.model.LeaveApprovalRequest;
import com.marglabs.trackmyclass.leave.model.LeaveRequest;
import com.marglabs.trackmyclass.leave.service.LeaveService;
import com.marglabs.trackmyclass.user.model.User;
import com.marglabs.trackmyclass.user.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/leave/v1")
@Tag(name = "Leave Management", description = "APIs for managing student leave applications")
public class LeaveController {
    
    @Autowired
    private LeaveService leaveService;
    
    @Autowired
    private UserService userService;
    
    @Operation(summary = "Apply for leave", description = "Student applies for leave from class")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Leave application submitted successfully",
                content = @Content(schema = @Schema(implementation = Leave.class))),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Not enrolled in class")
    })
    @PostMapping(value = "/leaves", consumes = "application/json", produces = "application/json")
    @ResponseStatus(HttpStatus.CREATED)
    public Leave applyLeave(
            @Parameter(description = "Leave application details") @Valid @RequestBody LeaveRequest request,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        
        User student = userService.getUserByCognitoId(userPrincipal.getSubject());
        return leaveService.applyLeave(request, student.getId());
    }
    
    @Operation(summary = "Process leave application", description = "Teacher approves or denies leave application")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Leave processed successfully",
                content = @Content(schema = @Schema(implementation = Leave.class))),
        @ApiResponse(responseCode = "400", description = "Invalid input or leave already processed"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Not authorized to process this leave"),
        @ApiResponse(responseCode = "404", description = "Leave not found")
    })
    @PutMapping(value = "/leaves/{leaveId}/process", consumes = "application/json", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public Leave processLeave(
            @Parameter(description = "Leave ID") @PathVariable String leaveId,
            @Parameter(description = "Approval decision") @Valid @RequestBody LeaveApprovalRequest request,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());
        return leaveService.processLeave(leaveId, request, teacher.getId());
    }
    
    @Operation(summary = "Get student leaves", description = "Get all leave applications by student")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Student leaves retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/leaves/my-leaves", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<Leave> getMyLeaves(@Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        User student = userService.getUserByCognitoId(userPrincipal.getSubject());
        return leaveService.getStudentLeaves(student.getId());
    }
    
    @Operation(summary = "Get teacher leaves", description = "Get all leave applications for teacher's classes")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Teacher leaves retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/leaves/teacher-leaves", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<Leave> getTeacherLeaves(@Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());
        return leaveService.getTeacherLeaves(teacher.getId());
    }
    
    @Operation(summary = "Get pending leaves", description = "Get pending leave applications for teacher")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Pending leaves retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/leaves/pending", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<Leave> getPendingLeaves(@Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());
        return leaveService.getPendingLeaves(teacher.getId());
    }
    
    @Operation(summary = "Get leaves by class", description = "Get all leave applications for a specific class")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Class leaves retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Not authorized for this class")
    })
    @GetMapping(value = "/leaves/class/{classId}", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<Leave> getLeavesByClass(
            @Parameter(description = "Class ID") @PathVariable String classId,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());
        return leaveService.getLeavesByClassId(classId, teacher.getId());
    }
    
    @Operation(summary = "Get my leaves by class", description = "Get student's own leave applications for a specific class")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Student class leaves retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Not enrolled in this class")
    })
    @GetMapping(value = "/leaves/my-leaves/class/{classId}", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<Leave> getMyLeavesByClass(
            @Parameter(description = "Class ID") @PathVariable String classId,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        User student = userService.getUserByCognitoId(userPrincipal.getSubject());
        return leaveService.getStudentLeavesByClassId(classId, student.getId());
    }
}