package com.marglabs.trackmyclass.leave.dao;

import com.marglabs.common.rest.error.EntityNotFoundException;
import com.marglabs.trackmyclass.leave.entity.LeaveEntity;
import com.marglabs.trackmyclass.leave.model.LeaveStatus;
import com.marglabs.trackmyclass.leave.repository.LeaveRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class LeaveDao {
    
    @Autowired
    private LeaveRepository repository;
    
    public LeaveEntity save(LeaveEntity entity) {
        return repository.save(entity);
    }
    
    public LeaveEntity getById(String id) {
        return repository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException(HttpStatus.NOT_FOUND, "LEAVE", "LEAVE_NOT_FOUND",
                        "leave_not_found", id));
    }
    
    public List<LeaveEntity> getByStudentId(String studentId) {
        return repository.findByStudentIdOrderByCreatedDateDesc(studentId);
    }
    
    public List<LeaveEntity> getByTeacherId(String teacherId) {
        return repository.findByTeacherIdOrderByCreatedDateDesc(teacherId);
    }
    
    public List<LeaveEntity> getPendingLeavesByTeacher(String teacherId) {
        return repository.findByTeacherIdAndStatusOrderByCreatedDateDesc(teacherId, LeaveStatus.PENDING);
    }
    
    public List<LeaveEntity> getByClassId(String classId) {
        return repository.findByClassIdOrderByCreatedDateDesc(classId);
    }
    
    public List<LeaveEntity> getByClassIdAndStudentId(String classId, String studentId) {
        return repository.findByClassIdAndStudentIdOrderByCreatedDateDesc(classId, studentId);
    }
}