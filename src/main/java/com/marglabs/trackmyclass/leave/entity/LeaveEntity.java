package com.marglabs.trackmyclass.leave.entity;

import com.marglabs.trackmyclass.leave.model.LeaveStatus;
import jakarta.persistence.*;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.Instant;

@Entity
@Table(name = "leaves")
@Data
@Accessors(chain = true)
public class LeaveEntity {
    
    @Id
    @Column(name = "id")
    private String id;
    
    @Column(name = "student_id", nullable = false)
    private String studentId;
    
    @Column(name = "class_id", nullable = false)
    private String classId;
    
    @Column(name = "teacher_id", nullable = false)
    private String teacherId;
    
    @Column(name = "start_date", nullable = false, length = 10)
    private String startDate;
    
    @Column(name = "end_date", nullable = false, length = 10)
    private String endDate;
    
    @Column(name = "reason", nullable = false, length = 500)
    private String reason;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private LeaveStatus status;
    
    @Column(name = "teacher_comments", length = 500)
    private String teacherComments;
    
    @CreationTimestamp
    @Column(name = "created_date", nullable = false, updatable = false)
    private Instant createdDate;
    
    @UpdateTimestamp
    @Column(name = "updated_date", nullable = false)
    private Instant updatedDate;
}