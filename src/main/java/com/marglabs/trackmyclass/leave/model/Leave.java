package com.marglabs.trackmyclass.leave.model;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class Leave {
    
    private String id;
    private String studentId;
    private String studentName;
    private String classId;
    private String className;
    private String teacherId;
    private String teacherName;
    private String startDate;
    private String endDate;
    private String reason;
    private LeaveStatus status;
    private String teacherComments;
    private long createdDate;
    private long updatedDate;
}