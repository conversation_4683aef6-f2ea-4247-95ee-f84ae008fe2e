package com.marglabs.trackmyclass.leave.model;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class LeaveApprovalRequest {
    
    @NotNull(message = "Status is required")
    private LeaveStatus status;
    
    @Size(max = 500, message = "Comments cannot exceed 500 characters")
    private String teacherComments;
}