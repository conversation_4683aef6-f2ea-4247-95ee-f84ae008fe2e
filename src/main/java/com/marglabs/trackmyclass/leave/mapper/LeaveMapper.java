package com.marglabs.trackmyclass.leave.mapper;

import com.marglabs.trackmyclass.leave.entity.LeaveEntity;
import com.marglabs.trackmyclass.leave.model.Leave;
import com.marglabs.trackmyclass.leave.model.LeaveRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(componentModel = "spring")
public interface LeaveMapper {
    
    @Mapping(target = "studentName", ignore = true)
    @Mapping(target = "className", ignore = true)
    @Mapping(target = "teacherName", ignore = true)
    @Mapping(target = "createdDate", expression = "java(entity.getCreatedDate() != null ? entity.getCreatedDate().toEpochMilli() : 0)")
    @Mapping(target = "updatedDate", expression = "java(entity.getUpdatedDate() != null ? entity.getUpdatedDate().toEpochMilli() : 0)")
    Leave toDto(LeaveEntity entity);
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "studentId", ignore = true)
    @Mapping(target = "teacherId", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "teacherComments", ignore = true)
    @Mapping(target = "createdDate", ignore = true)
    @Mapping(target = "updatedDate", ignore = true)
    LeaveEntity toEntity(LeaveRequest request);
}