package com.marglabs.trackmyclass.leave.service;

import com.marglabs.common.rest.error.GeneralException;
import com.marglabs.common.utils.IdGenerator;
import com.marglabs.trackmyclass.classroom.dao.ClassroomDao;
import com.marglabs.trackmyclass.enrollment.dao.EnrollmentDao;
import com.marglabs.trackmyclass.leave.dao.LeaveDao;
import com.marglabs.trackmyclass.leave.entity.LeaveEntity;
import com.marglabs.trackmyclass.leave.mapper.LeaveMapper;
import com.marglabs.trackmyclass.leave.model.Leave;
import com.marglabs.trackmyclass.leave.model.LeaveApprovalRequest;
import com.marglabs.trackmyclass.leave.model.LeaveRequest;
import com.marglabs.trackmyclass.leave.model.LeaveStatus;
import com.marglabs.trackmyclass.user.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class LeaveService {
    
    private static final Logger logger = LoggerFactory.getLogger(LeaveService.class);
    
    @Autowired
    private LeaveDao leaveDao;
    
    @Autowired
    private ClassroomDao classroomDao;
    
    @Autowired
    private EnrollmentDao enrollmentDao;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private LeaveMapper mapper;
    
    @Autowired
    private IdGenerator idGenerator;
    
    @Transactional
    public Leave applyLeave(LeaveRequest request, String studentId) {
        logger.info("Student {} applying for leave in class {}", studentId, request.getClassId());
        
        validateLeaveRequest(request);
        validateStudentEnrollment(studentId, request.getClassId());
        
        var classroom = classroomDao.getById(request.getClassId());
        
        LeaveEntity entity = mapper.toEntity(request);
        entity.setId(idGenerator.generateId());
        entity.setStudentId(studentId);
        entity.setTeacherId(classroom.getTeacherId());
        entity.setStatus(LeaveStatus.PENDING);
        
        LeaveEntity savedEntity = leaveDao.save(entity);
        return enrichLeave(mapper.toDto(savedEntity));
    }
    
    @Transactional
    public Leave processLeave(String leaveId, LeaveApprovalRequest request, String teacherId) {
        logger.info("Teacher {} processing leave {}", teacherId, leaveId);
        
        LeaveEntity entity = leaveDao.getById(leaveId);
        
        if (!entity.getTeacherId().equals(teacherId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "LEAVE", "UNAUTHORIZED_ACCESS",
                    "teacher_not_authorized_for_leave", teacherId, leaveId);
        }
        
        if (entity.getStatus() != LeaveStatus.PENDING) {
            throw new GeneralException(HttpStatus.BAD_REQUEST, "LEAVE", "LEAVE_ALREADY_PROCESSED",
                    "leave_already_processed", leaveId);
        }
        
        entity.setStatus(request.getStatus());
        entity.setTeacherComments(request.getTeacherComments());
        
        LeaveEntity updatedEntity = leaveDao.save(entity);
        return enrichLeave(mapper.toDto(updatedEntity));
    }
    
    public List<Leave> getStudentLeaves(String studentId) {
        List<LeaveEntity> entities = leaveDao.getByStudentId(studentId);
        return entities.stream()
                .map(entity -> enrichLeave(mapper.toDto(entity)))
                .collect(Collectors.toList());
    }
    
    public List<Leave> getTeacherLeaves(String teacherId) {
        List<LeaveEntity> entities = leaveDao.getByTeacherId(teacherId);
        return entities.stream()
                .map(entity -> enrichLeave(mapper.toDto(entity)))
                .collect(Collectors.toList());
    }
    
    public List<Leave> getPendingLeaves(String teacherId) {
        List<LeaveEntity> entities = leaveDao.getPendingLeavesByTeacher(teacherId);
        return entities.stream()
                .map(entity -> enrichLeave(mapper.toDto(entity)))
                .collect(Collectors.toList());
    }
    
    public List<Leave> getLeavesByClassId(String classId, String teacherId) {
        var classroom = classroomDao.getById(classId);
        
        if (!classroom.getTeacherId().equals(teacherId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "LEAVE", "UNAUTHORIZED_ACCESS",
                    "teacher_not_authorized_for_class", teacherId, classId);
        }
        
        List<LeaveEntity> entities = leaveDao.getByClassId(classId);
        return entities.stream()
                .map(entity -> enrichLeave(mapper.toDto(entity)))
                .collect(Collectors.toList());
    }
    
    public List<Leave> getStudentLeavesByClassId(String classId, String studentId) {
        validateStudentEnrollment(studentId, classId);
        
        List<LeaveEntity> entities = leaveDao.getByClassIdAndStudentId(classId, studentId);
        return entities.stream()
                .map(entity -> enrichLeave(mapper.toDto(entity)))
                .collect(Collectors.toList());
    }
    
    private void validateLeaveRequest(LeaveRequest request) {
        LocalDate startDate = LocalDate.parse(request.getStartDate());
        LocalDate endDate = LocalDate.parse(request.getEndDate());
        
        if (startDate.isAfter(endDate)) {
            throw new GeneralException(HttpStatus.BAD_REQUEST, "LEAVE", "INVALID_DATE_RANGE",
                    "start_date_must_be_before_end_date");
        }
        
        if (startDate.isBefore(LocalDate.now())) {
            throw new GeneralException(HttpStatus.BAD_REQUEST, "LEAVE", "INVALID_START_DATE",
                    "start_date_cannot_be_in_past");
        }
    }
    
    private void validateStudentEnrollment(String studentId, String classId) {
        if (!enrollmentDao.existsByClassroomIdAndUserIdAndRole(classId, studentId, "STUDENT")) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "LEAVE", "NOT_ENROLLED",
                    "student_not_enrolled_in_class", studentId, classId);
        }
    }
    
    private Leave enrichLeave(Leave leave) {
        try {
            var student = userService.getUserById(leave.getStudentId());
            leave.setStudentName(student.getName());
            
            var classroom = classroomDao.getById(leave.getClassId());
            leave.setClassName(classroom.getClassName());
            
            var teacher = userService.getUserById(leave.getTeacherId());
            leave.setTeacherName(teacher.getName());
        } catch (Exception e) {
            logger.warn("Failed to enrich leave details for leave {}: {}", leave.getId(), e.getMessage());
        }
        return leave;
    }
}