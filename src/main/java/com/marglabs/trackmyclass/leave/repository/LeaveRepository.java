package com.marglabs.trackmyclass.leave.repository;

import com.marglabs.trackmyclass.leave.entity.LeaveEntity;
import com.marglabs.trackmyclass.leave.model.LeaveStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface LeaveRepository extends JpaRepository<LeaveEntity, String> {
    
    List<LeaveEntity> findByStudentIdOrderByCreatedDateDesc(String studentId);
    
    List<LeaveEntity> findByTeacherIdOrderByCreatedDateDesc(String teacherId);
    
    List<LeaveEntity> findByTeacherIdAndStatusOrderByCreatedDateDesc(String teacherId, LeaveStatus status);
    
    List<LeaveEntity> findByClassIdOrderByCreatedDateDesc(String classId);
    
    List<LeaveEntity> findByClassIdAndStudentIdOrderByCreatedDateDesc(String classId, String studentId);
}