package com.marglabs.trackmyclass.file.config;

import com.marglabs.trackmyclass.file.provider.*;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class FileStorageConfiguration {
    
    @Bean
    @ConditionalOnProperty(name = "file.storage.type", havingValue = "DATABASE", matchIfMissing = true)
    public DatabaseStorageProvider databaseStorageProvider() {
        return new DatabaseStorageProvider();
    }
    

    @Bean
    @ConditionalOnProperty(name = "file.storage.type", havingValue = "GCP_CLOUD_STORAGE")
    public GcpCloudStorageProvider gcpCloudStorageProvider() {
        return new GcpCloudStorageProvider();
    }
    
    @Bean
    @ConditionalOnProperty(name = "file.storage.type", havingValue = "LOCAL_FILESYSTEM")
    public LocalFilesystemStorageProvider localFilesystemStorageProvider() {
        return new LocalFilesystemStorageProvider();
    }
    
    /**
     * Fallback bean to ensure at least one storage provider is always available
     */
    @Bean
    @ConditionalOnMissingBean(FileStorageProvider.class)
    public DatabaseStorageProvider fallbackDatabaseStorageProvider() {
        return new DatabaseStorageProvider();
    }
}
