package com.marglabs.trackmyclass.file.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.annotations.CreationTimestamp;

import java.time.Instant;

@Entity
@Table(name = "file_data")
@Data
@Accessors(chain = true)
public class FileDataEntity {
    
    @Id
    @Column(name = "file_id")
    private String fileId;
    
    @Lob
    @Column(name = "file_data", nullable = false)
    private byte[] fileData;
    
    @CreationTimestamp
    @Column(name = "created_date", nullable = false, updatable = false)
    private Instant createdDate;
}
