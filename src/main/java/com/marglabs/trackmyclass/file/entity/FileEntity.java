package com.marglabs.trackmyclass.file.entity;

import com.marglabs.trackmyclass.file.model.FileStorageType;
import jakarta.persistence.*;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.Instant;

@Entity
@Table(name = "files")
@Data
@Accessors(chain = true)
public class FileEntity {
    
    @Id
    @Column(name = "id")
    private String id;
    
    @Column(name = "original_filename", nullable = false, length = 500)
    private String originalFilename;
    
    @Column(name = "stored_filename", nullable = false, length = 500)
    private String storedFilename;
    
    @Column(name = "content_type", length = 200)
    private String contentType;
    
    @Column(name = "file_size", nullable = false)
    private long fileSize;
    
    @Column(name = "file_hash", length = 64)
    private String fileHash; // SHA-256 hash for integrity
    
    @Enumerated(EnumType.STRING)
    @Column(name = "storage_type", nullable = false)
    private FileStorageType storageType;
    
    @Column(name = "storage_path", length = 1000)
    private String storagePath; // Path/key in storage system
    
    @Column(name = "bucket_name", length = 200)
    private String bucketName; // For cloud storage
    
    @Column(name = "uploaded_by", nullable = false)
    private String uploadedBy; // User ID who uploaded the file
    
    @Column(name = "description", length = 1000)
    private String description;
    
    @Column(name = "tags", length = 500)
    private String tags; // Comma-separated tags for categorization

    @Column(name = "is_active", nullable = false)
    private boolean isActive = true;
    
    @CreationTimestamp
    @Column(name = "created_date", nullable = false, updatable = false)
    private Instant createdDate;
    
    @UpdateTimestamp
    @Column(name = "updated_date", nullable = false)
    private Instant updatedDate;
    
    // Metadata fields for different use cases
    @Column(name = "related_entity_type", length = 100)
    private String relatedEntityType; // e.g., "ASSIGNMENT", "PROFILE", "CLASSROOM"

    @Column(name = "file_category", length = 100)
    private String fileCategory; // e.g., "DOCUMENT", "IMAGE", "VIDEO", "AUDIO"
}
