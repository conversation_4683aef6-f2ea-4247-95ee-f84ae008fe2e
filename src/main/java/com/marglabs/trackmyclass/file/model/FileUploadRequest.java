package com.marglabs.trackmyclass.file.model;

import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.constraints.NotNull;
import java.time.Instant;

@Data
@Accessors(chain = true)
public class FileUploadRequest {
    
    @NotNull(message = "File is required")
    private MultipartFile file;
    
    private String fileId;
    private String description;
    private String tags;
    private String relatedEntityType;
    private String fileCategory;
}
