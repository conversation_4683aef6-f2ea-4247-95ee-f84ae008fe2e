package com.marglabs.trackmyclass.file.model;

public enum FileStorageType {
    DATABASE("Database Storage", "Files stored as BLOB in database"),
    AWS_S3("Amazon S3", "Files stored in AWS S3 bucket"),
    GCP_CLOUD_STORAGE("Google Cloud Storage", "Files stored in GCP Cloud Storage bucket"),
    LOCAL_FILESYSTEM("Local File System", "Files stored on local server filesystem");
    
    private final String displayName;
    private final String description;
    
    FileStorageType(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public String getDescription() {
        return description;
    }
}
