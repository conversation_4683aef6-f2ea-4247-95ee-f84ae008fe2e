package com.marglabs.trackmyclass.file.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.Instant;

@Data
@Accessors(chain = true)
public class FileInfo {
    
    private String id;
    private String originalFilename;
    private String contentType;
    private long fileSize;
    private String fileHash;
    private FileStorageType storageType;
    private String uploadedBy;
    private String description;
    private String tags;
    private boolean isActive;
    private Instant createdDate;
    private Instant updatedDate;
    private String relatedEntityType;
    private String fileCategory;
    
    // Additional fields for API responses
    private String downloadUrl;
    private String previewUrl;
    private String fileSizeFormatted; // e.g., "1.5 MB"
}
