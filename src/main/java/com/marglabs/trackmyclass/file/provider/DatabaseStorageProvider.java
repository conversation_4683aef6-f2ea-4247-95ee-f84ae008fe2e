package com.marglabs.trackmyclass.file.provider;

import com.marglabs.trackmyclass.file.entity.FileDataEntity;
import com.marglabs.trackmyclass.file.model.FileStorageType;
import com.marglabs.trackmyclass.file.repository.FileDataRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Optional;

public class DatabaseStorageProvider implements FileStorageProvider {
    
    private static final Logger logger = LoggerFactory.getLogger(DatabaseStorageProvider.class);
    
    @Autowired
    private FileDataRepository fileDataRepository;
    
    @Override
    public FileStorageType getStorageType() {
        return FileStorageType.DATABASE;
    }
    
    @Override
    public boolean isAvailable() {
        try {
            // Test database connectivity
            fileDataRepository.count();
            return true;
        } catch (Exception e) {
            logger.error("Database storage provider not available: {}", e.getMessage());
            return false;
        }
    }
    
    @Override
    public String storeFile(String fileId, String filename, InputStream inputStream, String contentType, long fileSize) throws Exception {
        logger.info("Storing file {} in database", fileId);
        
        try {
            // Read all bytes from input stream
            byte[] fileData = inputStream.readAllBytes();
            
            // Create file data entity
            FileDataEntity fileDataEntity = new FileDataEntity()
                    .setFileId(fileId)
                    .setFileData(fileData);
            
            // Save to database
            fileDataRepository.save(fileDataEntity);
            
            logger.info("Successfully stored file {} in database, size: {} bytes", fileId, fileData.length);
            return fileId; // Use fileId as storage path for database storage
            
        } catch (IOException e) {
            logger.error("Failed to read file data for {}: {}", fileId, e.getMessage());
            throw new Exception("Failed to read file data", e);
        } catch (Exception e) {
            logger.error("Failed to store file {} in database: {}", fileId, e.getMessage());
            throw new Exception("Failed to store file in database", e);
        }
    }
    
    @Override
    public InputStream retrieveFile(String storagePath) throws Exception {
        logger.debug("Retrieving file {} from database", storagePath);
        
        try {
            Optional<FileDataEntity> fileDataOpt = fileDataRepository.findByFileId(storagePath);
            
            if (fileDataOpt.isEmpty()) {
                throw new Exception("File not found in database: " + storagePath);
            }
            
            FileDataEntity fileData = fileDataOpt.get();
            return new ByteArrayInputStream(fileData.getFileData());
            
        } catch (Exception e) {
            logger.error("Failed to retrieve file {} from database: {}", storagePath, e.getMessage());
            throw new Exception("Failed to retrieve file from database", e);
        }
    }
    
    @Override
    public boolean deleteFile(String storagePath) throws Exception {
        logger.info("Deleting file {} from database", storagePath);
        
        try {
            Optional<FileDataEntity> fileDataOpt = fileDataRepository.findByFileId(storagePath);
            
            if (fileDataOpt.isEmpty()) {
                logger.warn("File {} not found in database for deletion", storagePath);
                return false;
            }
            
            fileDataRepository.delete(fileDataOpt.get());
            logger.info("Successfully deleted file {} from database", storagePath);
            return true;
            
        } catch (Exception e) {
            logger.error("Failed to delete file {} from database: {}", storagePath, e.getMessage());
            throw new Exception("Failed to delete file from database", e);
        }
    }
    
    @Override
    public boolean fileExists(String storagePath) throws Exception {
        try {
            return fileDataRepository.existsByFileId(storagePath);
        } catch (Exception e) {
            logger.error("Failed to check file existence {} in database: {}", storagePath, e.getMessage());
            throw new Exception("Failed to check file existence in database", e);
        }
    }
    
    @Override
    public long getFileSize(String storagePath) throws Exception {
        try {
            Optional<FileDataEntity> fileDataOpt = fileDataRepository.findByFileId(storagePath);
            
            if (fileDataOpt.isEmpty()) {
                throw new Exception("File not found in database: " + storagePath);
            }
            
            return fileDataOpt.get().getFileData().length;
            
        } catch (Exception e) {
            logger.error("Failed to get file size {} from database: {}", storagePath, e.getMessage());
            throw new Exception("Failed to get file size from database", e);
        }
    }
    
    @Override
    public String generatePresignedUrl(String storagePath, int expirationMinutes) throws Exception {
        // Database storage doesn't support pre-signed URLs
        return null;
    }
    
    @Override
    public String getStorageMetadata(String storagePath) throws Exception {
        try {
            Optional<FileDataEntity> fileDataOpt = fileDataRepository.findByFileId(storagePath);
            
            if (fileDataOpt.isEmpty()) {
                return "File not found";
            }
            
            FileDataEntity fileData = fileDataOpt.get();
            return String.format("Database storage - File ID: %s, Size: %d bytes, Created: %s", 
                                fileData.getFileId(), 
                                fileData.getFileData().length,
                                fileData.getCreatedDate());
            
        } catch (Exception e) {
            logger.error("Failed to get metadata for file {} from database: {}", storagePath, e.getMessage());
            throw new Exception("Failed to get file metadata from database", e);
        }
    }
}
