package com.marglabs.trackmyclass.file.provider;

import com.marglabs.trackmyclass.file.model.FileStorageType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;

import jakarta.annotation.PostConstruct;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;

public class LocalFilesystemStorageProvider implements FileStorageProvider {
    
    private static final Logger logger = LoggerFactory.getLogger(LocalFilesystemStorageProvider.class);
    
    @Value("${file.storage.local.base-path:/var/app/files}")
    private String basePath;
    
    @Value("${file.storage.local.create-directories:true}")
    private boolean createDirectories;
    
    @PostConstruct
    public void initializeLocalStorage() {
        try {
            Path baseDir = Paths.get(basePath);
            
            if (!Files.exists(baseDir)) {
                if (createDirectories) {
                    Files.createDirectories(baseDir);
                    logger.info("Created local storage directory: {}", basePath);
                } else {
                    logger.error("Local storage directory does not exist: {}", basePath);
                }
            }
            
            // Test write permissions
            Path testFile = baseDir.resolve(".write-test");
            Files.write(testFile, "test".getBytes());
            Files.delete(testFile);
            
            logger.info("Local filesystem storage initialized at: {}", basePath);
            
        } catch (Exception e) {
            logger.error("Failed to initialize local filesystem storage: {}", e.getMessage());
        }
    }
    
    @Override
    public FileStorageType getStorageType() {
        return FileStorageType.LOCAL_FILESYSTEM;
    }
    
    @Override
    public boolean isAvailable() {
        try {
            Path baseDir = Paths.get(basePath);
            return Files.exists(baseDir) && Files.isWritable(baseDir);
        } catch (Exception e) {
            logger.error("Local filesystem storage not available: {}", e.getMessage());
            return false;
        }
    }
    
    @Override
    public String storeFile(String fileId, String filename, InputStream inputStream, String contentType, long fileSize) throws Exception {
        logger.info("Storing file {} in local filesystem", fileId);
        
        try {
            // Create directory structure: basePath/files/first2chars/fileId/
            String dirPath = String.format("files/%s/%s", 
                fileId.substring(0, Math.min(2, fileId.length())), fileId);
            Path fileDir = Paths.get(basePath, dirPath);
            
            if (!Files.exists(fileDir)) {
                Files.createDirectories(fileDir);
            }
            
            // Store file with original filename
            Path filePath = fileDir.resolve(sanitizeFilename(filename));
            Files.copy(inputStream, filePath, StandardCopyOption.REPLACE_EXISTING);
            
            String relativePath = Paths.get(basePath).relativize(filePath).toString();
            logger.info("Successfully stored file {} at: {}", fileId, relativePath);
            
            return relativePath;
            
        } catch (Exception e) {
            logger.error("Failed to store file {} in local filesystem: {}", fileId, e.getMessage());
            throw new Exception("Failed to store file in local filesystem", e);
        }
    }
    
    @Override
    public InputStream retrieveFile(String storagePath) throws Exception {
        logger.debug("Retrieving file {} from local filesystem", storagePath);
        
        try {
            Path filePath = Paths.get(basePath, storagePath);
            
            if (!Files.exists(filePath)) {
                throw new Exception("File not found in local filesystem: " + storagePath);
            }
            
            return Files.newInputStream(filePath);
            
        } catch (Exception e) {
            logger.error("Failed to retrieve file {} from local filesystem: {}", storagePath, e.getMessage());
            throw new Exception("Failed to retrieve file from local filesystem", e);
        }
    }
    
    @Override
    public boolean deleteFile(String storagePath) throws Exception {
        logger.info("Deleting file {} from local filesystem", storagePath);
        
        try {
            Path filePath = Paths.get(basePath, storagePath);
            
            if (!Files.exists(filePath)) {
                logger.warn("File {} not found in local filesystem for deletion", storagePath);
                return false;
            }
            
            Files.delete(filePath);
            
            // Try to delete empty parent directories
            cleanupEmptyDirectories(filePath.getParent());
            
            logger.info("Successfully deleted file {} from local filesystem", storagePath);
            return true;
            
        } catch (Exception e) {
            logger.error("Failed to delete file {} from local filesystem: {}", storagePath, e.getMessage());
            throw new Exception("Failed to delete file from local filesystem", e);
        }
    }
    
    @Override
    public boolean fileExists(String storagePath) throws Exception {
        try {
            Path filePath = Paths.get(basePath, storagePath);
            return Files.exists(filePath);
        } catch (Exception e) {
            logger.error("Failed to check file existence {} in local filesystem: {}", storagePath, e.getMessage());
            throw new Exception("Failed to check file existence in local filesystem", e);
        }
    }
    
    @Override
    public long getFileSize(String storagePath) throws Exception {
        try {
            Path filePath = Paths.get(basePath, storagePath);
            
            if (!Files.exists(filePath)) {
                throw new Exception("File not found in local filesystem: " + storagePath);
            }
            
            return Files.size(filePath);
            
        } catch (Exception e) {
            logger.error("Failed to get file size {} from local filesystem: {}", storagePath, e.getMessage());
            throw new Exception("Failed to get file size from local filesystem", e);
        }
    }
    
    @Override
    public String generatePresignedUrl(String storagePath, int expirationMinutes) throws Exception {
        // Local filesystem doesn't support pre-signed URLs
        return null;
    }
    
    @Override
    public String getStorageMetadata(String storagePath) throws Exception {
        try {
            Path filePath = Paths.get(basePath, storagePath);
            
            if (!Files.exists(filePath)) {
                return "File not found";
            }
            
            return String.format("Local filesystem storage - Path: %s, Size: %d bytes, Modified: %s", 
                                filePath.toString(), 
                                Files.size(filePath),
                                Files.getLastModifiedTime(filePath));
            
        } catch (Exception e) {
            logger.error("Failed to get metadata for file {} from local filesystem: {}", storagePath, e.getMessage());
            throw new Exception("Failed to get file metadata from local filesystem", e);
        }
    }
    
    /**
     * Sanitize filename to prevent directory traversal and invalid characters
     */
    private String sanitizeFilename(String filename) {
        if (filename == null) {
            return "unnamed";
        }
        
        // Remove path separators and other dangerous characters
        String sanitized = filename.replaceAll("[/\\\\:*?\"<>|]", "_");
        
        // Limit length
        if (sanitized.length() > 255) {
            String extension = "";
            int lastDot = sanitized.lastIndexOf('.');
            if (lastDot > 0) {
                extension = sanitized.substring(lastDot);
                sanitized = sanitized.substring(0, Math.min(255 - extension.length(), lastDot)) + extension;
            } else {
                sanitized = sanitized.substring(0, 255);
            }
        }
        
        return sanitized;
    }
    
    /**
     * Clean up empty parent directories
     */
    private void cleanupEmptyDirectories(Path directory) {
        try {
            Path baseDir = Paths.get(basePath);
            
            while (directory != null && !directory.equals(baseDir)) {
                if (Files.exists(directory) && isDirectoryEmpty(directory)) {
                    Files.delete(directory);
                    logger.debug("Deleted empty directory: {}", directory);
                    directory = directory.getParent();
                } else {
                    break;
                }
            }
        } catch (Exception e) {
            logger.debug("Failed to cleanup empty directories: {}", e.getMessage());
        }
    }
    
    /**
     * Check if directory is empty
     */
    private boolean isDirectoryEmpty(Path directory) throws IOException {
        try (var stream = Files.list(directory)) {
            return !stream.findFirst().isPresent();
        }
    }
}
