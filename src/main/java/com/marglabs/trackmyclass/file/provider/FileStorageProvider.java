package com.marglabs.trackmyclass.file.provider;

import com.marglabs.trackmyclass.file.model.FileStorageType;

import java.io.InputStream;

public interface FileStorageProvider {
    
    /**
     * Get the storage type this provider handles
     */
    FileStorageType getStorageType();
    
    /**
     * Check if this provider is available and properly configured
     */
    boolean isAvailable();
    
    /**
     * Store a file and return the storage path/key
     */
    String storeFile(String fileId, String filename, InputStream inputStream, String contentType, long fileSize) throws Exception;
    
    /**
     * Retrieve a file as InputStream
     */
    InputStream retrieveFile(String storagePath) throws Exception;
    
    /**
     * Delete a file from storage
     */
    boolean deleteFile(String storagePath) throws Exception;
    
    /**
     * Check if a file exists in storage
     */
    boolean fileExists(String storagePath) throws Exception;
    
    /**
     * Get file size from storage
     */
    long getFileSize(String storagePath) throws Exception;
    
    /**
     * Generate a pre-signed URL for direct access (for cloud providers)
     * Returns null if not supported
     */
    String generatePresignedUrl(String storagePath, int expirationMinutes) throws Exception;
    
    /**
     * Get storage-specific metadata
     */
    String getStorageMetadata(String storagePath) throws Exception;
}
