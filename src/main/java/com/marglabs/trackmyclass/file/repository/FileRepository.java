package com.marglabs.trackmyclass.file.repository;

import com.marglabs.trackmyclass.file.entity.FileEntity;
import com.marglabs.trackmyclass.file.model.FileStorageType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

@Repository
public interface FileRepository extends JpaRepository<FileEntity, String> {
    
    // Find files by uploader
    List<FileEntity> findByUploadedByAndIsActiveTrueOrderByCreatedDateDesc(String uploadedBy);
    
    // Related entity ID functionality removed
    
    // Find files by category
    List<FileEntity> findByFileCategoryAndIsActiveTrueOrderByCreatedDateDesc(String fileCategory);
    
    // Public files functionality removed
    
    // Find files by storage type
    List<FileEntity> findByStorageTypeAndIsActiveTrueOrderByCreatedDateDesc(FileStorageType storageType);
    
    // Expired files functionality removed
    
    // Find files by content type
    List<FileEntity> findByContentTypeContainingIgnoreCaseAndIsActiveTrueOrderByCreatedDateDesc(String contentType);
    
    // Search files by filename
    @Query("SELECT f FROM FileEntity f WHERE LOWER(f.originalFilename) LIKE LOWER(CONCAT('%', :filename, '%')) AND f.isActive = true ORDER BY f.createdDate DESC")
    List<FileEntity> searchByFilename(@Param("filename") String filename);
    
    // Find files by tags
    @Query("SELECT f FROM FileEntity f WHERE LOWER(f.tags) LIKE LOWER(CONCAT('%', :tag, '%')) AND f.isActive = true ORDER BY f.createdDate DESC")
    List<FileEntity> findByTagsContaining(@Param("tag") String tag);
    
    // Get file statistics
    @Query("SELECT f.storageType, COUNT(f), SUM(f.fileSize) FROM FileEntity f WHERE f.isActive = true GROUP BY f.storageType")
    List<Object[]> getFileStatsByStorageType();
    
    // Find large files
    @Query("SELECT f FROM FileEntity f WHERE f.fileSize > :sizeThreshold AND f.isActive = true ORDER BY f.fileSize DESC")
    List<FileEntity> findLargeFiles(@Param("sizeThreshold") long sizeThreshold);
    
    // Count files by uploader
    long countByUploadedByAndIsActiveTrue(String uploadedBy);
    
    // Get total storage used by uploader
    @Query("SELECT COALESCE(SUM(f.fileSize), 0) FROM FileEntity f WHERE f.uploadedBy = :uploadedBy AND f.isActive = true")
    Long getTotalStorageUsedByUploader(@Param("uploadedBy") String uploadedBy);

    // Count active files
    long countByIsActiveTrue();
}
