package com.marglabs.trackmyclass.file.repository;

import com.marglabs.trackmyclass.file.entity.FileDataEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface FileDataRepository extends JpaRepository<FileDataEntity, String> {
    
    Optional<FileDataEntity> findByFileId(String fileId);
    
    boolean existsByFileId(String fileId);
    
    void deleteByFileId(String fileId);
}
