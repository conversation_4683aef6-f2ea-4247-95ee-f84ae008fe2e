package com.marglabs.trackmyclass.file.mapper;

import com.marglabs.trackmyclass.file.entity.FileEntity;
import com.marglabs.trackmyclass.file.model.FileInfo;
import org.springframework.stereotype.Component;

@Component
public class FileMapper {
    
    public FileInfo toDto(FileEntity entity) {
        if (entity == null) {
            return null;
        }
        
        return new FileInfo()
                .setId(entity.getId())
                .setOriginalFilename(entity.getOriginalFilename())
                .setContentType(entity.getContentType())
                .setFileSize(entity.getFileSize())
                .setFileHash(entity.getFileHash())
                .setStorageType(entity.getStorageType())
                .setUploadedBy(entity.getUploadedBy())
                .setDescription(entity.getDescription())
                .setTags(entity.getTags())
                .setActive(entity.isActive())
                .setCreatedDate(entity.getCreatedDate())
                .setUpdatedDate(entity.getUpdatedDate())
                .setRelatedEntityType(entity.getRelatedEntityType())
                .setFileCategory(entity.getFileCategory());
    }
    
    public FileEntity toEntity(FileInfo dto) {
        if (dto == null) {
            return null;
        }
        
        return new FileEntity()
                .setId(dto.getId())
                .setOriginalFilename(dto.getOriginalFilename())
                .setContentType(dto.getContentType())
                .setFileSize(dto.getFileSize())
                .setFileHash(dto.getFileHash())
                .setStorageType(dto.getStorageType())
                .setUploadedBy(dto.getUploadedBy())
                .setDescription(dto.getDescription())
                .setTags(dto.getTags())
                .setActive(dto.isActive())
                .setRelatedEntityType(dto.getRelatedEntityType())
                .setFileCategory(dto.getFileCategory());
    }
}
