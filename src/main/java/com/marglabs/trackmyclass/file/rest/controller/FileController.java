package com.marglabs.trackmyclass.file.rest.controller;

import com.marglabs.trackmyclass.file.model.FileInfo;
import com.marglabs.trackmyclass.file.model.FileUploadRequest;
import com.marglabs.trackmyclass.file.service.FileService;
import com.marglabs.trackmyclass.user.model.User;
import com.marglabs.trackmyclass.user.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.time.Instant;
import java.util.List;

@RestController
@RequestMapping("/api/file/v1")
@Tag(name = "File Management", description = "APIs for file upload, download, and management")
public class FileController {
    
    @Autowired
    private FileService fileService;
    
    @Autowired
    private UserService userService;
    
    @Operation(summary = "Upload or update file", 
               description = "Upload a new file or update existing file content")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "File uploaded successfully",
                content = @Content(schema = @Schema(implementation = FileInfo.class))),
        @ApiResponse(responseCode = "200", description = "File updated successfully",
                content = @Content(schema = @Schema(implementation = FileInfo.class))),
        @ApiResponse(responseCode = "400", description = "Invalid file or request data"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Not authorized to update this file"),
        @ApiResponse(responseCode = "404", description = "File to update not found"),
        @ApiResponse(responseCode = "413", description = "File too large"),
        @ApiResponse(responseCode = "415", description = "Unsupported file type")
    })
    @PostMapping(value = "/files", consumes = MediaType.MULTIPART_FORM_DATA_VALUE, produces = "application/json")
    public ResponseEntity<FileInfo> uploadFile(
            @Valid @ModelAttribute FileUploadRequest request,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());
        
        FileInfo result;
        if (request.getFileId() != null && !request.getFileId().trim().isEmpty()) {
            // Update existing file
            result = fileService.updateFile(request.getFileId(), request, user.getId());
            return ResponseEntity.ok(result);
        } else {
            // Upload new file
            result = fileService.uploadFile(request, user.getId());
            return ResponseEntity.status(HttpStatus.CREATED).body(result);
        }
    }
    
    @Operation(summary = "Download file", 
               description = "Download a file by its ID")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "File downloaded successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "404", description = "File not found"),
        @ApiResponse(responseCode = "410", description = "File expired")
    })
    @GetMapping(value = "/files/{fileId}/download")
    public ResponseEntity<InputStreamResource> downloadFile(
            @Parameter(description = "File ID") @PathVariable String fileId,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());
        
        // Get file info for headers
        FileInfo fileInfo = fileService.getFileInfo(fileId);
        
        // Get file stream
        InputStream fileStream = fileService.downloadFile(fileId);
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.parseMediaType(fileInfo.getContentType()));
        headers.setContentDispositionFormData("attachment", fileInfo.getOriginalFilename());
        headers.setContentLength(fileInfo.getFileSize());
        
        return ResponseEntity.ok()
                .headers(headers)
                .body(new InputStreamResource(fileStream));
    }
    
    @Operation(summary = "Get file information", 
               description = "Get metadata information about a file")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "File information retrieved successfully",
                content = @Content(schema = @Schema(implementation = FileInfo.class))),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "404", description = "File not found")
    })
    @GetMapping(value = "/files/{fileId}", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public FileInfo getFileInfo(
            @Parameter(description = "File ID") @PathVariable String fileId,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());
        return fileService.getFileInfo(fileId);
    }
    
    @Operation(summary = "Delete file", 
               description = "Delete a file (only by the uploader)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "File deleted successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Forbidden - not the file owner"),
        @ApiResponse(responseCode = "404", description = "File not found")
    })
    @DeleteMapping(value = "/files/{fileId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void deleteFile(
            @Parameter(description = "File ID") @PathVariable String fileId,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());
        fileService.deleteFile(fileId, user.getId());
    }
    
    @Operation(summary = "Get user files", 
               description = "Get all files uploaded by the authenticated user")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "User files retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/files/my-files", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<FileInfo> getUserFiles(@Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());
        return fileService.getFilesByUser(user.getId());
    }
    
    // Related entity functionality removed
    
    @Operation(summary = "Generate presigned URL", 
               description = "Generate a presigned URL for direct file access (cloud storage only)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Presigned URL generated successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "404", description = "File not found"),
        @ApiResponse(responseCode = "501", description = "Not supported by current storage provider")
    })
    @GetMapping(value = "/files/{fileId}/presigned-url", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public ResponseEntity<String> generatePresignedUrl(
            @Parameter(description = "File ID") @PathVariable String fileId,
            @Parameter(description = "URL expiration in minutes") @RequestParam(defaultValue = "60") int expirationMinutes,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());
        
        String presignedUrl = fileService.generatePresignedUrl(fileId, expirationMinutes);
        
        if (presignedUrl == null) {
            return ResponseEntity.status(HttpStatus.NOT_IMPLEMENTED)
                    .body("Presigned URLs not supported by current storage provider");
        }
        
        return ResponseEntity.ok(presignedUrl);
    }
}
