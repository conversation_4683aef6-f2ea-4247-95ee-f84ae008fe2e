package com.marglabs.trackmyclass.file.rest.controller;

import com.marglabs.trackmyclass.file.model.FileStorageType;
import com.marglabs.trackmyclass.file.service.FileManagementService;
import com.marglabs.trackmyclass.user.model.RoleType;
import com.marglabs.trackmyclass.user.model.User;
import com.marglabs.trackmyclass.user.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/api/file/v1/admin")
@Tag(name = "File Administration", description = "Administrative APIs for file management")
public class FileAdminController {
    
    @Autowired
    private FileManagementService fileManagementService;
    
    @Autowired
    private UserService userService;
    
    @Operation(summary = "Get storage statistics", 
               description = "Get comprehensive statistics about file storage usage")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Storage statistics retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Admin access required")
    })
    @GetMapping(value = "/statistics", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public Map<String, Object> getStorageStatistics(@Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());
        verifyAdminAccess(user);
        
        return fileManagementService.getStorageStatistics();
    }
    
    @Operation(summary = "Validate file integrity", 
               description = "Validate the integrity of a specific file")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "File integrity validation completed"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Admin access required"),
        @ApiResponse(responseCode = "404", description = "File not found")
    })
    @GetMapping(value = "/files/{fileId}/validate", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public Map<String, Object> validateFileIntegrity(
            @Parameter(description = "File ID") @PathVariable String fileId,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());
        verifyAdminAccess(user);
        
        return fileManagementService.validateFileIntegrity(fileId);
    }
    
    @Operation(summary = "Migrate file storage", 
               description = "Migrate a file from one storage provider to another")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "File migration completed successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid storage type or migration failed"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Admin access required"),
        @ApiResponse(responseCode = "404", description = "File not found")
    })
    @PostMapping(value = "/files/{fileId}/migrate", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public Map<String, Object> migrateFile(
            @Parameter(description = "File ID") @PathVariable String fileId,
            @Parameter(description = "Target storage type") @RequestParam FileStorageType targetStorageType,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());
        verifyAdminAccess(user);
        
        boolean success = fileManagementService.migrateFile(fileId, targetStorageType);
        
        return Map.of(
            "fileId", fileId,
            "targetStorageType", targetStorageType.name(),
            "migrationSuccess", success,
            "message", success ? "File migrated successfully" : "File migration failed"
        );
    }
    
    // Expired files cleanup removed since expiration functionality was removed
    
    @Operation(summary = "Trigger orphaned files cleanup", 
               description = "Manually trigger cleanup of orphaned files")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Orphaned files cleanup triggered successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Admin access required")
    })
    @PostMapping(value = "/cleanup/orphaned", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public Map<String, Object> triggerOrphanedFilesCleanup(@Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());
        verifyAdminAccess(user);
        
        // Trigger orphaned files cleanup manually
        fileManagementService.cleanupOrphanedFiles();
        
        return Map.of(
            "message", "Orphaned files cleanup triggered successfully",
            "timestamp", System.currentTimeMillis()
        );
    }
    
    @Operation(summary = "Get storage configuration", 
               description = "Get current file storage configuration")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Storage configuration retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Admin access required")
    })
    @GetMapping(value = "/configuration", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public Map<String, Object> getStorageConfiguration(@Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        User user = userService.getUserByCognitoId(userPrincipal.getSubject());
        verifyAdminAccess(user);
        
        return Map.of(
            "availableStorageTypes", FileStorageType.values(),
            "storageTypeDescriptions", Map.of(
                "DATABASE", "Files stored as BLOB in database",
                "AWS_S3", "Files stored in Amazon S3 bucket",
                "GCP_CLOUD_STORAGE", "Files stored in Google Cloud Storage",
                "LOCAL_FILESYSTEM", "Files stored on local server filesystem"
            ),
            "features", Map.of(
                "presignedUrls", "Supported by AWS_S3 and GCP_CLOUD_STORAGE",
                "encryption", "Available for all storage types",
                "virusScanning", "Available for all storage types",
                "thumbnailGeneration", "Available for image files"
            )
        );
    }
    
    /**
     * Verify admin access
     */
    private void verifyAdminAccess(User user) {
        boolean isAdmin = user.getRoles().stream()
                .anyMatch(role -> role.getRole() == RoleType.TEACHER); // Assuming teachers have admin access
        
        if (!isAdmin) {
            throw new AccessDeniedException("Admin access required for file management operations");
        }
    }
}
