package com.marglabs.trackmyclass.file.service;

import com.marglabs.trackmyclass.file.entity.FileEntity;
import com.marglabs.trackmyclass.file.model.FileInfo;
import com.marglabs.trackmyclass.file.model.FileStorageType;
import com.marglabs.trackmyclass.file.provider.FileStorageProvider;
import com.marglabs.trackmyclass.file.repository.FileRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class FileManagementService {
    
    private static final Logger logger = LoggerFactory.getLogger(FileManagementService.class);
    
    @Autowired
    private FileRepository fileRepository;
    
    @Autowired
    private List<FileStorageProvider> storageProviders;
    
    @Value("${file.cleanup.enabled:true}")
    private boolean cleanupEnabled;
    
    @Value("${file.cleanup.orphaned.enabled:true}")
    private boolean orphanedCleanupEnabled;
    
    @Value("${file.cleanup.orphaned.age-days:7}")
    private int orphanedFileAgeDays;
    
    /**
     * Get file storage statistics
     */
    public Map<String, Object> getStorageStatistics() {
        logger.info("Generating file storage statistics");
        
        Map<String, Object> stats = new HashMap<>();
        
        // Overall statistics
        long totalFiles = fileRepository.count();
        long activeFiles = fileRepository.countByIsActiveTrue();
        
        stats.put("totalFiles", totalFiles);
        stats.put("activeFiles", activeFiles);
        stats.put("inactiveFiles", totalFiles - activeFiles);
        
        // Storage type breakdown
        List<Object[]> storageStats = fileRepository.getFileStatsByStorageType();
        Map<String, Map<String, Object>> storageBreakdown = new HashMap<>();
        
        for (Object[] stat : storageStats) {
            FileStorageType storageType = (FileStorageType) stat[0];
            Long fileCount = (Long) stat[1];
            Long totalSize = (Long) stat[2];
            
            Map<String, Object> typeStats = new HashMap<>();
            typeStats.put("fileCount", fileCount);
            typeStats.put("totalSize", totalSize);
            typeStats.put("averageSize", fileCount > 0 ? totalSize / fileCount : 0);
            typeStats.put("totalSizeFormatted", formatFileSize(totalSize));
            
            storageBreakdown.put(storageType.name(), typeStats);
        }
        
        stats.put("storageBreakdown", storageBreakdown);
        
        // Storage provider availability
        Map<String, Boolean> providerAvailability = new HashMap<>();
        for (FileStorageProvider provider : storageProviders) {
            providerAvailability.put(provider.getStorageType().name(), provider.isAvailable());
        }
        stats.put("providerAvailability", providerAvailability);
        
        // No expired files since we removed expiration functionality
        
        // Large files (> 10MB)
        List<FileEntity> largeFiles = fileRepository.findLargeFiles(10 * 1024 * 1024);
        stats.put("largeFiles", largeFiles.size());
        
        return stats;
    }
    
    // Expired files cleanup removed since expiration functionality was removed
    
    /**
     * Clean up orphaned files (files in storage but not in database)
     */
    @Scheduled(cron = "${file.cleanup.orphaned.schedule:0 0 3 * * ?}")
    public void cleanupOrphanedFiles() {
        if (!orphanedCleanupEnabled) {
            return;
        }
        
        logger.info("Starting cleanup of orphaned files");
        
        // This is a placeholder implementation
        // In a real implementation, you would:
        // 1. List all files in storage
        // 2. Check if each file exists in database
        // 3. Delete files that don't exist in database and are older than threshold
        
        logger.info("Orphaned file cleanup completed");
    }
    
    /**
     * Validate file integrity
     */
    public Map<String, Object> validateFileIntegrity(String fileId) {
        logger.info("Validating integrity of file: {}", fileId);
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            FileEntity fileEntity = fileRepository.findById(fileId)
                    .orElseThrow(() -> new RuntimeException("File not found: " + fileId));
            
            FileStorageProvider provider = getStorageProviderByType(fileEntity.getStorageType());
            
            // Check if file exists in storage
            boolean existsInStorage = provider.fileExists(fileEntity.getStoragePath());
            result.put("existsInStorage", existsInStorage);
            
            if (existsInStorage) {
                // Check file size
                long storageSize = provider.getFileSize(fileEntity.getStoragePath());
                boolean sizeMatches = storageSize == fileEntity.getFileSize();
                
                result.put("storageSizeBytes", storageSize);
                result.put("databaseSizeBytes", fileEntity.getFileSize());
                result.put("sizeMatches", sizeMatches);
                
                // TODO: Verify file hash if needed
                result.put("hashVerified", false);
                result.put("hashNote", "Hash verification not implemented");
            }
            
            result.put("valid", existsInStorage);
            result.put("fileId", fileId);
            result.put("storageType", fileEntity.getStorageType().name());
            
        } catch (Exception e) {
            logger.error("Failed to validate file integrity for {}: {}", fileId, e.getMessage());
            result.put("valid", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }
    
    /**
     * Migrate file to different storage provider
     */
    public boolean migrateFile(String fileId, FileStorageType targetStorageType) {
        logger.info("Migrating file {} to storage type: {}", fileId, targetStorageType);
        
        try {
            FileEntity fileEntity = fileRepository.findById(fileId)
                    .orElseThrow(() -> new RuntimeException("File not found: " + fileId));
            
            if (fileEntity.getStorageType() == targetStorageType) {
                logger.info("File {} is already in target storage type", fileId);
                return true;
            }
            
            // Get source and target providers
            FileStorageProvider sourceProvider = getStorageProviderByType(fileEntity.getStorageType());
            FileStorageProvider targetProvider = getStorageProviderByType(targetStorageType);
            
            // Retrieve file from source
            try (var inputStream = sourceProvider.retrieveFile(fileEntity.getStoragePath())) {
                
                // Store in target
                String newStoragePath = targetProvider.storeFile(
                    fileEntity.getId(),
                    fileEntity.getOriginalFilename(),
                    inputStream,
                    fileEntity.getContentType(),
                    fileEntity.getFileSize()
                );
                
                // Update database
                String oldStoragePath = fileEntity.getStoragePath();
                FileStorageType oldStorageType = fileEntity.getStorageType();
                
                fileEntity.setStorageType(targetStorageType);
                fileEntity.setStoragePath(newStoragePath);
                fileRepository.save(fileEntity);
                
                // Delete from old storage
                try {
                    sourceProvider.deleteFile(oldStoragePath);
                    logger.info("Successfully migrated file {} from {} to {}", 
                               fileId, oldStorageType, targetStorageType);
                    return true;
                } catch (Exception e) {
                    logger.warn("Failed to delete file from old storage after migration: {}", e.getMessage());
                    return true; // Migration successful, cleanup failed
                }
            }
            
        } catch (Exception e) {
            logger.error("Failed to migrate file {}: {}", fileId, e.getMessage());
            return false;
        }
    }
    
    /**
     * Get storage provider by type
     */
    private FileStorageProvider getStorageProviderByType(FileStorageType storageType) {
        return storageProviders.stream()
                .filter(provider -> provider.getStorageType() == storageType)
                .findFirst()
                .orElseThrow(() -> new RuntimeException("Storage provider not found: " + storageType));
    }
    
    /**
     * Format file size in human readable format
     */
    private String formatFileSize(long size) {
        if (size < 1024) return size + " B";
        if (size < 1024 * 1024) return String.format("%.1f KB", size / 1024.0);
        if (size < 1024 * 1024 * 1024) return String.format("%.1f MB", size / (1024.0 * 1024.0));
        return String.format("%.1f GB", size / (1024.0 * 1024.0 * 1024.0));
    }
}
