package com.marglabs.trackmyclass.file.service;

import com.marglabs.common.rest.error.GeneralException;
import com.marglabs.common.utils.IdGenerator;
import com.marglabs.trackmyclass.file.entity.FileEntity;
import com.marglabs.trackmyclass.file.mapper.FileMapper;
import com.marglabs.trackmyclass.file.model.FileInfo;
import com.marglabs.trackmyclass.file.model.FileStorageType;
import com.marglabs.trackmyclass.file.model.FileUploadRequest;
import com.marglabs.trackmyclass.file.provider.FileStorageProvider;
import com.marglabs.trackmyclass.file.repository.FileRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.security.MessageDigest;
import java.time.Instant;
import java.util.List;
import java.util.Optional;

@Service
public class FileService {
    
    private static final Logger logger = LoggerFactory.getLogger(FileService.class);
    
    @Autowired
    private FileRepository fileRepository;
    
    @Autowired
    private List<FileStorageProvider> storageProviders;
    
    @Autowired
    private IdGenerator idGenerator;
    
    @Autowired
    private FileMapper fileMapper;
    
    @Value("${file.storage.type:DATABASE}")
    private String configuredStorageType;
    
    @Value("${file.max-size:10485760}") // 10MB default
    private long maxFileSize;
    
    @Value("${file.allowed-types:image/jpeg,image/png,image/gif,application/pdf,text/plain}")
    private String allowedContentTypes;
    
    /**
     * Upload a file
     */
    public FileInfo uploadFile(FileUploadRequest request, String uploadedBy) {
        logger.info("Uploading file: {} by user: {}", request.getFile().getOriginalFilename(), uploadedBy);
        
        MultipartFile file = request.getFile();
        
        // Validate file
        validateFile(file);
        
        // Get active storage provider
        FileStorageProvider storageProvider = getActiveStorageProvider();
        
        try {
            // Generate file ID
            String fileId = idGenerator.generateId();
            
            // Calculate file hash
            String fileHash = calculateFileHash(file.getInputStream());
            
            // Store file using the active provider
            String storagePath = storageProvider.storeFile(
                fileId, 
                file.getOriginalFilename(), 
                file.getInputStream(), 
                file.getContentType(), 
                file.getSize()
            );
            
            // Create file entity
            FileEntity fileEntity = new FileEntity()
                    .setId(fileId)
                    .setOriginalFilename(file.getOriginalFilename())
                    .setStoredFilename(generateStoredFilename(fileId, file.getOriginalFilename()))
                    .setContentType(file.getContentType())
                    .setFileSize(file.getSize())
                    .setFileHash(fileHash)
                    .setStorageType(storageProvider.getStorageType())
                    .setStoragePath(storagePath)
                    .setUploadedBy(uploadedBy)
                    .setDescription(request.getDescription())
                    .setTags(request.getTags())
                    .setRelatedEntityType(request.getRelatedEntityType())
                    .setFileCategory(request.getFileCategory())
                    .setActive(true);
            
            // Save to database
            FileEntity savedEntity = fileRepository.save(fileEntity);
            
            logger.info("Successfully uploaded file: {} with ID: {}", file.getOriginalFilename(), fileId);
            return enrichFileInfo(fileMapper.toDto(savedEntity));
            
        } catch (Exception e) {
            logger.error("Failed to upload file: {} - Error: {}", file.getOriginalFilename(), e.getMessage());
            throw new GeneralException(HttpStatus.INTERNAL_SERVER_ERROR, "FILE", "UPLOAD_FAILED",
                    "file_upload_failed", file.getOriginalFilename(), e.getMessage());
        }
    }
    
    /**
     * Download a file
     */
    public InputStream downloadFile(String fileId) {
        logger.info("Downloading file: {}", fileId);
        
        FileEntity fileEntity = getFileEntityById(fileId);

        // Get storage provider for this file
        FileStorageProvider storageProvider = getStorageProviderByType(fileEntity.getStorageType());
        
        try {
            return storageProvider.retrieveFile(fileEntity.getStoragePath());
        } catch (Exception e) {
            logger.error("Failed to download file: {} - Error: {}", fileId, e.getMessage());
            throw new GeneralException(HttpStatus.INTERNAL_SERVER_ERROR, "FILE", "DOWNLOAD_FAILED",
                    "file_download_failed", fileId, e.getMessage());
        }
    }
    
    /**
     * Get file information
     */
    public FileInfo getFileInfo(String fileId) {
        FileEntity fileEntity = getFileEntityById(fileId);
        return enrichFileInfo(fileMapper.toDto(fileEntity));
    }
    
    /**
     * Update an existing file
     */
    public FileInfo updateFile(String fileId, FileUploadRequest request, String userId) {
        logger.info("Updating file: {} by user: {}", fileId, userId);
        
        FileEntity existingEntity = getFileEntityById(fileId);
        
        // Check if user has permission to update
        if (!existingEntity.getUploadedBy().equals(userId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "FILE", "UPDATE_FORBIDDEN",
                    "file_update_forbidden", fileId);
        }
        
        MultipartFile file = request.getFile();
        validateFile(file);
        
        FileStorageProvider storageProvider = getStorageProviderByType(existingEntity.getStorageType());
        
        try {
            // Calculate new file hash
            String fileHash = calculateFileHash(file.getInputStream());
            
            // Delete old file from storage
            storageProvider.deleteFile(existingEntity.getStoragePath());
            
            // Store new file
            String storagePath = storageProvider.storeFile(
                fileId, 
                file.getOriginalFilename(), 
                file.getInputStream(), 
                file.getContentType(), 
                file.getSize()
            );
            
            // Update entity
            existingEntity.setOriginalFilename(file.getOriginalFilename())
                    .setStoredFilename(generateStoredFilename(fileId, file.getOriginalFilename()))
                    .setContentType(file.getContentType())
                    .setFileSize(file.getSize())
                    .setFileHash(fileHash)
                    .setStoragePath(storagePath)
                    .setDescription(request.getDescription())
                    .setTags(request.getTags())
                    .setRelatedEntityType(request.getRelatedEntityType())
                    .setFileCategory(request.getFileCategory());
            
            FileEntity updatedEntity = fileRepository.save(existingEntity);
            
            logger.info("Successfully updated file: {}", fileId);
            return enrichFileInfo(fileMapper.toDto(updatedEntity));
            
        } catch (Exception e) {
            logger.error("Failed to update file: {} - Error: {}", fileId, e.getMessage());
            throw new GeneralException(HttpStatus.INTERNAL_SERVER_ERROR, "FILE", "UPDATE_FAILED",
                    "file_update_failed", fileId, e.getMessage());
        }
    }
    
    /**
     * Delete a file
     */
    public void deleteFile(String fileId, String userId) {
        logger.info("Deleting file: {} by user: {}", fileId, userId);
        
        FileEntity fileEntity = getFileEntityById(fileId);
        
        // Check if user has permission to delete
        if (!fileEntity.getUploadedBy().equals(userId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "FILE", "DELETE_FORBIDDEN",
                    "file_delete_forbidden", fileId);
        }
        
        // Get storage provider for this file
        FileStorageProvider storageProvider = getStorageProviderByType(fileEntity.getStorageType());
        
        try {
            // Delete from storage
            storageProvider.deleteFile(fileEntity.getStoragePath());
            
            // Mark as inactive in database
            fileEntity.setActive(false);
            fileRepository.save(fileEntity);
            
            logger.info("Successfully deleted file: {}", fileId);
            
        } catch (Exception e) {
            logger.error("Failed to delete file: {} - Error: {}", fileId, e.getMessage());
            throw new GeneralException(HttpStatus.INTERNAL_SERVER_ERROR, "FILE", "DELETE_FAILED",
                    "file_delete_failed", fileId, e.getMessage());
        }
    }
    
    /**
     * Get files by user
     */
    public List<FileInfo> getFilesByUser(String userId) {
        List<FileEntity> fileEntities = fileRepository.findByUploadedByAndIsActiveTrueOrderByCreatedDateDesc(userId);
        return fileEntities.stream()
                .map(entity -> enrichFileInfo(fileMapper.toDto(entity)))
                .toList();
    }
    
    // Related entity functionality removed
    
    /**
     * Generate presigned URL for direct access (cloud providers only)
     */
    public String generatePresignedUrl(String fileId, int expirationMinutes) {
        FileEntity fileEntity = getFileEntityById(fileId);
        FileStorageProvider storageProvider = getStorageProviderByType(fileEntity.getStorageType());
        
        try {
            return storageProvider.generatePresignedUrl(fileEntity.getStoragePath(), expirationMinutes);
        } catch (Exception e) {
            logger.error("Failed to generate presigned URL for file: {} - Error: {}", fileId, e.getMessage());
            return null;
        }
    }
    
    /**
     * Get active storage provider based on configuration
     */
    private FileStorageProvider getActiveStorageProvider() {
        FileStorageType configuredType = FileStorageType.valueOf(configuredStorageType.toUpperCase());
        
        return storageProviders.stream()
                .filter(provider -> provider.getStorageType() == configuredType)
                .filter(FileStorageProvider::isAvailable)
                .findFirst()
                .orElseThrow(() -> new GeneralException(HttpStatus.SERVICE_UNAVAILABLE, "FILE", "STORAGE_UNAVAILABLE",
                        "storage_provider_unavailable", configuredType.getDisplayName()));
    }
    
    /**
     * Get storage provider by type
     */
    private FileStorageProvider getStorageProviderByType(FileStorageType storageType) {
        return storageProviders.stream()
                .filter(provider -> provider.getStorageType() == storageType)
                .findFirst()
                .orElseThrow(() -> new GeneralException(HttpStatus.SERVICE_UNAVAILABLE, "FILE", "STORAGE_UNAVAILABLE",
                        "storage_provider_not_found", storageType.getDisplayName()));
    }
    
    /**
     * Get file entity by ID
     */
    private FileEntity getFileEntityById(String fileId) {
        return fileRepository.findById(fileId)
                .filter(FileEntity::isActive)
                .orElseThrow(() -> new GeneralException(HttpStatus.NOT_FOUND, "FILE", "FILE_NOT_FOUND",
                        "file_not_found", fileId));
    }
    
    /**
     * Validate uploaded file
     */
    private void validateFile(MultipartFile file) {
        if (file.isEmpty()) {
            throw new GeneralException(HttpStatus.BAD_REQUEST, "FILE", "EMPTY_FILE",
                    "empty_file_not_allowed");
        }
        
        if (file.getSize() > maxFileSize) {
            throw new GeneralException(HttpStatus.BAD_REQUEST, "FILE", "FILE_TOO_LARGE",
                    "file_too_large", String.valueOf(maxFileSize));
        }
        
        String contentType = file.getContentType();
        if (contentType == null || !allowedContentTypes.contains(contentType)) {
            throw new GeneralException(HttpStatus.BAD_REQUEST, "FILE", "INVALID_FILE_TYPE",
                    "invalid_file_type", contentType, allowedContentTypes);
        }
    }
    
    /**
     * Calculate SHA-256 hash of file
     */
    private String calculateFileHash(InputStream inputStream) throws Exception {
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        byte[] buffer = new byte[8192];
        int bytesRead;
        
        while ((bytesRead = inputStream.read(buffer)) != -1) {
            digest.update(buffer, 0, bytesRead);
        }
        
        byte[] hashBytes = digest.digest();
        StringBuilder hexString = new StringBuilder();
        
        for (byte b : hashBytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        
        return hexString.toString();
    }
    
    /**
     * Generate stored filename
     */
    private String generateStoredFilename(String fileId, String originalFilename) {
        String extension = "";
        int lastDotIndex = originalFilename.lastIndexOf('.');
        if (lastDotIndex > 0) {
            extension = originalFilename.substring(lastDotIndex);
        }
        return fileId + extension;
    }
    
    /**
     * Enrich file info with additional data
     */
    private FileInfo enrichFileInfo(FileInfo fileInfo) {
        // Set download URL
        fileInfo.setDownloadUrl("/api/file/v1/files/" + fileInfo.getId() + "/download");

        // Format file size
        fileInfo.setFileSizeFormatted(formatFileSize(fileInfo.getFileSize()));
        
        return fileInfo;
    }
    
    /**
     * Format file size in human readable format
     */
    private String formatFileSize(long size) {
        if (size < 1024) return size + " B";
        if (size < 1024 * 1024) return String.format("%.1f KB", size / 1024.0);
        if (size < 1024 * 1024 * 1024) return String.format("%.1f MB", size / (1024.0 * 1024.0));
        return String.format("%.1f GB", size / (1024.0 * 1024.0 * 1024.0));
    }
}
