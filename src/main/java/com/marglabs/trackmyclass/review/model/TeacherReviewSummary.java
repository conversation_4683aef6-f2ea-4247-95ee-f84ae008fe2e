package com.marglabs.trackmyclass.review.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class TeacherReviewSummary {
    
    private String teacherId;
    private String teacherName;
    private double averageRating;
    private long totalReviews;
    private long totalApprovedReviews;
    
    // Rating distribution
    private RatingDistribution ratingDistribution;
    
    // Category-wise ratings
    private CategoryRatings categoryRatings;
    
    // Recent reviews
    private List<TeacherReview> recentReviews;
    
    // Review trends
    private ReviewTrends trends;
    
    @Data
    @Accessors(chain = true)
    public static class RatingDistribution {
        private long fiveStars;
        private long fourStars;
        private long threeStars;
        private long twoStars;
        private long oneStar;
        
        public double getFiveStarPercentage() {
            long total = fiveStars + fourStars + threeStars + twoStars + oneStar;
            return total > 0 ? (double) fiveStars / total * 100 : 0.0;
        }
        
        public double getFourStarPercentage() {
            long total = fiveStars + fourStars + threeStars + twoStars + oneStar;
            return total > 0 ? (double) fourStars / total * 100 : 0.0;
        }
        
        public double getThreeStarPercentage() {
            long total = fiveStars + fourStars + threeStars + twoStars + oneStar;
            return total > 0 ? (double) threeStars / total * 100 : 0.0;
        }
        
        public double getTwoStarPercentage() {
            long total = fiveStars + fourStars + threeStars + twoStars + oneStar;
            return total > 0 ? (double) twoStars / total * 100 : 0.0;
        }
        
        public double getOneStarPercentage() {
            long total = fiveStars + fourStars + threeStars + twoStars + oneStar;
            return total > 0 ? (double) oneStar / total * 100 : 0.0;
        }
    }
    
    @Data
    @Accessors(chain = true)
    public static class CategoryRatings {
        private double teachingQualityRating;
        private double communicationRating;
        private double helpfulnessRating;
        private double overallRating;
    }
    
    @Data
    @Accessors(chain = true)
    public static class ReviewTrends {
        private String trendDirection; // IMPROVING, STABLE, DECLINING
        private double trendPercentage;
        private String mostCommonCategory;
        private String bestRatedCategory;
        private String lowestRatedCategory;
        private long reviewsThisMonth;
        private long reviewsLastMonth;
    }
}
