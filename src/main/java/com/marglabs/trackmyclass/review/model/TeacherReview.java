package com.marglabs.trackmyclass.review.model;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class TeacherReview {
    
    private String id;
    private String studentId;
    private String studentName;
    private String teacherId;
    private String teacherName;
    private String classroomId;
    private String className;
    private Integer rating; // 1-5 stars
    private String reviewTitle;
    private String reviewText;
    private String reviewCategory; // TEACHING_QUALITY, COMMUNICATION, HELPFULNESS, OVERALL
    private boolean isAnonymous;
    private String status; // PENDING, APPROVED, REJECTED
    private long createdDate;
    private long updatedDate;
    private long approvedDate;
    private String approvedBy;
}
