package com.marglabs.trackmyclass.review.model;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class TeacherReviewRequest {
    
    @NotBlank(message = "Teacher ID is required")
    private String teacherId;
    
    @NotBlank(message = "Classroom ID is required")
    private String classroomId;
    
    @NotNull(message = "Rating is required")
    @Min(value = 1, message = "Rating must be between 1 and 5")
    @Max(value = 5, message = "Rating must be between 1 and 5")
    private Integer rating;
    
    @Size(max = 200, message = "Review title cannot exceed 200 characters")
    private String reviewTitle;
    
    @NotBlank(message = "Review text is required")
    @Size(min = 10, message = "Review text must be at least 10 characters")
    @Size(max = 2000, message = "Review text cannot exceed 2000 characters")
    private String reviewText;
    
    @Pattern(regexp = "^(TEACHING_QUALITY|COMMUNICATION|HELPFULNESS|OVERALL)$", 
             message = "Review category must be one of: TEACHING_QUALITY, COMMUNICATION, HELPFULNESS, OVERALL")
    private String reviewCategory;
    
    private boolean isAnonymous;
}
