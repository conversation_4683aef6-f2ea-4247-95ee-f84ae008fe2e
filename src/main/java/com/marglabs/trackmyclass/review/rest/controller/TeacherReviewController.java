package com.marglabs.trackmyclass.review.rest.controller;

import com.marglabs.trackmyclass.review.model.TeacherReview;
import com.marglabs.trackmyclass.review.model.TeacherReviewRequest;
import com.marglabs.trackmyclass.review.model.TeacherReviewSummary;
import com.marglabs.trackmyclass.review.service.TeacherReviewService;
import com.marglabs.trackmyclass.user.model.User;
import com.marglabs.trackmyclass.user.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/teacherReview/v1")
@Tag(name = "Teacher Review Management", description = "APIs for students to review teachers and view review summaries")
public class TeacherReviewController {
    
    @Autowired
    private TeacherReviewService teacherReviewService;
    
    @Autowired
    private UserService userService;
    
    @Operation(summary = "Submit teacher review", description = "Submit a review for a teacher by a student")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Review submitted successfully",
                content = @Content(schema = @Schema(implementation = TeacherReview.class))),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Student not enrolled with teacher"),
        @ApiResponse(responseCode = "409", description = "Review already exists")
    })
    @PostMapping(value = "/reviews", consumes = "application/json", produces = "application/json")
    @ResponseStatus(HttpStatus.CREATED)
    public TeacherReview submitReview(
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal,
            @Parameter(description = "Teacher review details") @Valid @RequestBody TeacherReviewRequest request) {

        User student = userService.getUserByCognitoId(userPrincipal.getSubject());
        return teacherReviewService.submitReview(request, student.getId());
    }
    
    @Operation(summary = "Update teacher review", description = "Update an existing review (only pending reviews can be updated)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Review updated successfully",
                content = @Content(schema = @Schema(implementation = TeacherReview.class))),
        @ApiResponse(responseCode = "400", description = "Invalid input or review cannot be updated"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Not authorized to update this review"),
        @ApiResponse(responseCode = "404", description = "Review not found")
    })
    @PutMapping(value = "/reviews/{reviewId}", consumes = "application/json", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public TeacherReview updateReview(
            @Parameter(description = "Review ID") @PathVariable String reviewId,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal,
            @Parameter(description = "Updated review details") @Valid @RequestBody TeacherReviewRequest request) {

        User student = userService.getUserByCognitoId(userPrincipal.getSubject());
        return teacherReviewService.updateReview(reviewId, request, student.getId());
    }
    
    @Operation(summary = "Get teacher reviews", description = "Get all approved reviews for the authenticated teacher")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Reviews retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/teacher/reviews", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<TeacherReview> getTeacherReviews(@Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {

        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());
        return teacherReviewService.getReviewsByTeacher(teacher.getId());
    }
    
    @Operation(summary = "Get student reviews", description = "Get all reviews submitted by the authenticated student")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Student reviews retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/student/reviews", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<TeacherReview> getStudentReviews(@Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {
        User student = userService.getUserByCognitoId(userPrincipal.getSubject());
        return teacherReviewService.getReviewsByStudent(student.getId());
    }
    
    @Operation(summary = "Get review by ID", description = "Get a specific review by ID (only accessible by the student who created it)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Review retrieved successfully",
                content = @Content(schema = @Schema(implementation = TeacherReview.class))),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Not authorized to view this review"),
        @ApiResponse(responseCode = "404", description = "Review not found")
    })
    @GetMapping(value = "/reviews/{reviewId}", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public TeacherReview getReview(
            @Parameter(description = "Review ID") @PathVariable String reviewId,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {

        User student = userService.getUserByCognitoId(userPrincipal.getSubject());
        return teacherReviewService.getReviewById(reviewId, student.getId());
    }
    
    @Operation(summary = "Delete review", description = "Delete a review (only pending reviews can be deleted)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "Review deleted successfully"),
        @ApiResponse(responseCode = "400", description = "Review cannot be deleted"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Not authorized to delete this review"),
        @ApiResponse(responseCode = "404", description = "Review not found")
    })
    @DeleteMapping(value = "/reviews/{reviewId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void deleteReview(
            @Parameter(description = "Review ID") @PathVariable String reviewId,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {

        User student = userService.getUserByCognitoId(userPrincipal.getSubject());
        teacherReviewService.deleteReview(reviewId, student.getId());
    }
    
    @Operation(summary = "Get teacher review summary", description = "Get comprehensive review summary and statistics for the authenticated teacher")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Review summary retrieved successfully",
                content = @Content(schema = @Schema(implementation = TeacherReviewSummary.class))),
        @ApiResponse(responseCode = "404", description = "Teacher not found"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = "/teacher/summary", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public TeacherReviewSummary getTeacherReviewSummary(@Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {

        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());
        return teacherReviewService.getTeacherReviewSummary(teacher.getId());
    }

    @Operation(summary = "Get reviews by classroom",
               description = "Get all reviews for the authenticated teacher in a specific classroom, regardless of status")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Reviews retrieved successfully"),
        @ApiResponse(responseCode = "404", description = "Classroom not found"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Teacher not authorized for this classroom")
    })
    @GetMapping(value = "/teacher/classrooms/{classroomId}/reviews", produces = "application/json")
    @ResponseStatus(HttpStatus.OK)
    public List<TeacherReview> getReviewsByTeacherAndClassroom(
            @Parameter(description = "Classroom ID") @PathVariable String classroomId,
            @Parameter(hidden = true) @AuthenticationPrincipal Jwt userPrincipal) {

        User teacher = userService.getUserByCognitoId(userPrincipal.getSubject());
        return teacherReviewService.getReviewsByTeacherAndClassroom(teacher.getId(), classroomId);
    }
}
