package com.marglabs.trackmyclass.review.mapper;

import com.marglabs.trackmyclass.review.entity.TeacherReviewEntity;
import com.marglabs.trackmyclass.review.model.TeacherReview;
import com.marglabs.trackmyclass.review.model.TeacherReviewRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(componentModel = "spring")
public interface TeacherReviewMapper {
    
    @Mapping(target = "studentName", ignore = true) // Will be set by service
    @Mapping(target = "teacherName", ignore = true) // Will be set by service
    @Mapping(target = "className", ignore = true) // Will be set by service
    @Mapping(target = "createdDate", expression = "java(entity.getCreatedDate() != null ? entity.getCreatedDate().toEpochMilli() : 0)")
    @Mapping(target = "updatedDate", expression = "java(entity.getUpdatedDate() != null ? entity.getUpdatedDate().toEpochMilli() : 0)")
    @Mapping(target = "approvedDate", expression = "java(entity.getApprovedDate() != null ? entity.getApprovedDate().toEpochMilli() : 0)")
    TeacherReview toDto(TeacherReviewEntity entity);
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "studentId", ignore = true) // Will be set by service
    @Mapping(target = "status", ignore = true) // Will be set by service
    @Mapping(target = "createdDate", ignore = true)
    @Mapping(target = "updatedDate", ignore = true)
    @Mapping(target = "approvedDate", ignore = true)
    @Mapping(target = "approvedBy", ignore = true)
    TeacherReviewEntity toEntity(TeacherReviewRequest request);
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "studentId", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "createdDate", ignore = true)
    @Mapping(target = "updatedDate", ignore = true)
    @Mapping(target = "approvedDate", ignore = true)
    @Mapping(target = "approvedBy", ignore = true)
    void updateEntityFromRequest(TeacherReviewRequest request, @MappingTarget TeacherReviewEntity entity);
}
