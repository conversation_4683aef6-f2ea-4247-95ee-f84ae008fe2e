package com.marglabs.trackmyclass.review.service;

import com.marglabs.common.rest.error.GeneralException;
import com.marglabs.trackmyclass.classroom.service.ClassroomService;
import com.marglabs.trackmyclass.review.dao.TeacherReviewDao;
import com.marglabs.trackmyclass.review.entity.TeacherReviewEntity;
import com.marglabs.trackmyclass.review.mapper.TeacherReviewMapper;
import com.marglabs.trackmyclass.review.model.TeacherReview;
import com.marglabs.trackmyclass.review.model.TeacherReviewRequest;
import com.marglabs.trackmyclass.review.model.TeacherReviewSummary;
import com.marglabs.trackmyclass.user.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
public class TeacherReviewService {
    
    private static final Logger logger = LoggerFactory.getLogger(TeacherReviewService.class);
    
    @Autowired
    private TeacherReviewDao teacherReviewDao;
    
    @Autowired
    private TeacherReviewMapper mapper;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private ClassroomService classroomService;
    
    /**
     * Submit a review for a teacher
     */
    @Transactional
    public TeacherReview submitReview(TeacherReviewRequest request, String studentId) {
        logger.info("Student {} submitting review for teacher {}", studentId, request.getTeacherId());
        
        // Validate the request
        validateReviewRequest(request, studentId);
        
        // Check if student already reviewed this teacher for this classroom
        Optional<TeacherReviewEntity> existingReview = teacherReviewDao.findByStudentTeacherAndClassroom(
                studentId, request.getTeacherId(), request.getClassroomId());
        
        if (existingReview.isPresent()) {
            throw new GeneralException(HttpStatus.CONFLICT, "TEACHER_REVIEW", "REVIEW_ALREADY_EXISTS",
                    "student_already_reviewed_teacher", studentId, request.getTeacherId());
        }
        
        // Create entity
        TeacherReviewEntity entity = mapper.toEntity(request);
        entity.setId(UUID.randomUUID().toString());
        entity.setStudentId(studentId);
        entity.setStatus("PENDING"); // Reviews need approval
        
        // Save entity
        TeacherReviewEntity savedEntity = teacherReviewDao.save(entity);
        
        logger.info("Successfully submitted review with ID: {} for teacher: {}", savedEntity.getId(), request.getTeacherId());
        return enrichReviewWithDetails(mapper.toDto(savedEntity));
    }
    
    /**
     * Update a review (only by the student who created it and only if pending)
     */
    @Transactional
    public TeacherReview updateReview(String reviewId, TeacherReviewRequest request, String studentId) {
        logger.info("Student {} updating review {}", studentId, reviewId);
        
        // Get existing review and verify ownership
        TeacherReviewEntity entity = teacherReviewDao.getById(reviewId);
        
        if (!entity.getStudentId().equals(studentId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "TEACHER_REVIEW", "UNAUTHORIZED_REVIEW_ACCESS",
                    "student_not_authorized_for_review", studentId, reviewId);
        }
        
        if (!"PENDING".equals(entity.getStatus())) {
            throw new GeneralException(HttpStatus.BAD_REQUEST, "TEACHER_REVIEW", "REVIEW_CANNOT_BE_UPDATED",
                    "review_not_pending", reviewId);
        }
        
        // Validate the request
        validateReviewRequest(request, studentId);
        
        // Update entity
        mapper.updateEntityFromRequest(request, entity);
        
        // Save updated entity
        TeacherReviewEntity updatedEntity = teacherReviewDao.save(entity);
        
        logger.info("Successfully updated review with ID: {}", reviewId);
        return enrichReviewWithDetails(mapper.toDto(updatedEntity));
    }
    
    /**
     * Get reviews by teacher ID (approved reviews only for public view)
     */
    public List<TeacherReview> getReviewsByTeacher(String teacherId) {
        logger.info("Getting approved reviews for teacher: {}", teacherId);
        
        List<TeacherReviewEntity> entities = teacherReviewDao.getByTeacherId(teacherId);
        
        return entities.stream()
                .map(entity -> enrichReviewWithDetails(mapper.toDto(entity)))
                .collect(Collectors.toList());
    }
    
    /**
     * Get reviews by student ID (all reviews by this student)
     */
    public List<TeacherReview> getReviewsByStudent(String studentId) {
        logger.info("Getting reviews by student: {}", studentId);
        
        List<TeacherReviewEntity> entities = teacherReviewDao.getByStudentId(studentId);
        
        return entities.stream()
                .map(entity -> enrichReviewWithDetails(mapper.toDto(entity)))
                .collect(Collectors.toList());
    }
    
    /**
     * Get review by ID
     */
    public TeacherReview getReviewById(String reviewId, String studentId) {
        logger.info("Getting review {} for student: {}", reviewId, studentId);
        
        TeacherReviewEntity entity = teacherReviewDao.getById(reviewId);
        
        // Verify ownership
        if (!entity.getStudentId().equals(studentId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "TEACHER_REVIEW", "UNAUTHORIZED_REVIEW_ACCESS",
                    "student_not_authorized_for_review", studentId, reviewId);
        }
        
        return enrichReviewWithDetails(mapper.toDto(entity));
    }
    
    /**
     * Delete a review (only by the student who created it and only if pending)
     */
    @Transactional
    public void deleteReview(String reviewId, String studentId) {
        logger.info("Student {} deleting review {}", studentId, reviewId);
        
        TeacherReviewEntity entity = teacherReviewDao.getById(reviewId);
        
        if (!entity.getStudentId().equals(studentId)) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "TEACHER_REVIEW", "UNAUTHORIZED_REVIEW_ACCESS",
                    "student_not_authorized_for_review", studentId, reviewId);
        }
        
        if (!"PENDING".equals(entity.getStatus())) {
            throw new GeneralException(HttpStatus.BAD_REQUEST, "TEACHER_REVIEW", "REVIEW_CANNOT_BE_DELETED",
                    "review_not_pending", reviewId);
        }
        
        teacherReviewDao.deleteById(reviewId);
        
        logger.info("Successfully deleted review with ID: {}", reviewId);
    }
    
    /**
     * Get teacher review summary with statistics
     */
    public TeacherReviewSummary getTeacherReviewSummary(String teacherId) {
        logger.info("Generating review summary for teacher: {}", teacherId);
        
        // Get teacher information
        var teacher = userService.getUserById(teacherId);
        
        // Calculate basic statistics
        Double averageRating = teacherReviewDao.calculateAverageRating(teacherId);
        long totalReviews = teacherReviewDao.countByTeacher(teacherId);
        long totalApprovedReviews = teacherReviewDao.countByTeacherAndStatus(teacherId, "APPROVED");
        
        // Create summary
        TeacherReviewSummary summary = new TeacherReviewSummary()
                .setTeacherId(teacherId)
                .setTeacherName(teacher.getName())
                .setAverageRating(averageRating != null ? averageRating : 0.0)
                .setTotalReviews(totalReviews)
                .setTotalApprovedReviews(totalApprovedReviews);
        
        // Calculate rating distribution
        summary.setRatingDistribution(calculateRatingDistribution(teacherId));
        
        // Calculate category ratings
        summary.setCategoryRatings(calculateCategoryRatings(teacherId));
        
        // Get recent reviews
        List<TeacherReviewEntity> recentEntities = teacherReviewDao.getRecentApprovedReviews(teacherId);
        List<TeacherReview> recentReviews = recentEntities.stream()
                .limit(5) // Last 5 reviews
                .map(entity -> enrichReviewWithDetails(mapper.toDto(entity)))
                .collect(Collectors.toList());
        summary.setRecentReviews(recentReviews);
        
        // Calculate trends
        summary.setTrends(calculateReviewTrends(teacherId));
        
        logger.info("Successfully generated review summary for teacher: {}", teacherId);
        return summary;
    }
    
    private void validateReviewRequest(TeacherReviewRequest request, String studentId) {
        // Check if student can review this teacher (is enrolled in teacher's class)
        if (!teacherReviewDao.canStudentReviewTeacher(studentId, request.getTeacherId())) {
            throw new GeneralException(HttpStatus.FORBIDDEN, "TEACHER_REVIEW", "STUDENT_CANNOT_REVIEW_TEACHER",
                    "student_not_enrolled_with_teacher", studentId, request.getTeacherId());
        }
        
        // Verify teacher exists
        try {
            userService.getUserById(request.getTeacherId());
        } catch (Exception e) {
            throw new GeneralException(HttpStatus.NOT_FOUND, "TEACHER_REVIEW", "TEACHER_NOT_FOUND",
                    "teacher_not_found_details", request.getTeacherId());
        }
        
        // Verify classroom exists
        try {
            classroomService.getClassroomById(request.getClassroomId());
        } catch (Exception e) {
            throw new GeneralException(HttpStatus.NOT_FOUND, "TEACHER_REVIEW", "CLASSROOM_NOT_FOUND",
                    "classroom_not_found_details", request.getClassroomId());
        }
    }
    
    private TeacherReview enrichReviewWithDetails(TeacherReview review) {
        try {
            // Get student name (if not anonymous)
            if (!review.isAnonymous()) {
                var student = userService.getUserById(review.getStudentId());
                review.setStudentName(student.getName());
            } else {
                review.setStudentName("Anonymous");
            }

            // Get teacher name
            var teacher = userService.getUserById(review.getTeacherId());
            review.setTeacherName(teacher.getName());
            
            // Get classroom name
            var classroom = classroomService.getClassroomById(review.getClassroomId());
            review.setClassName(classroom.getClassName());
            
        } catch (Exception e) {
            logger.warn("Failed to enrich review details for review {}: {}", review.getId(), e.getMessage());
        }

        return review;
    }

    private TeacherReviewSummary.RatingDistribution calculateRatingDistribution(String teacherId) {
        return new TeacherReviewSummary.RatingDistribution()
                .setFiveStars(teacherReviewDao.countByTeacherAndRating(teacherId, 5))
                .setFourStars(teacherReviewDao.countByTeacherAndRating(teacherId, 4))
                .setThreeStars(teacherReviewDao.countByTeacherAndRating(teacherId, 3))
                .setTwoStars(teacherReviewDao.countByTeacherAndRating(teacherId, 2))
                .setOneStar(teacherReviewDao.countByTeacherAndRating(teacherId, 1));
    }

    private TeacherReviewSummary.CategoryRatings calculateCategoryRatings(String teacherId) {
        Double teachingQuality = teacherReviewDao.calculateAverageRatingByCategory(teacherId, "TEACHING_QUALITY");
        Double communication = teacherReviewDao.calculateAverageRatingByCategory(teacherId, "COMMUNICATION");
        Double helpfulness = teacherReviewDao.calculateAverageRatingByCategory(teacherId, "HELPFULNESS");
        Double overall = teacherReviewDao.calculateAverageRatingByCategory(teacherId, "OVERALL");

        return new TeacherReviewSummary.CategoryRatings()
                .setTeachingQualityRating(teachingQuality != null ? teachingQuality : 0.0)
                .setCommunicationRating(communication != null ? communication : 0.0)
                .setHelpfulnessRating(helpfulness != null ? helpfulness : 0.0)
                .setOverallRating(overall != null ? overall : 0.0);
    }

    private TeacherReviewSummary.ReviewTrends calculateReviewTrends(String teacherId) {
        // Calculate reviews for this month and last month
        LocalDate now = LocalDate.now();
        LocalDate startOfThisMonth = now.withDayOfMonth(1);
        LocalDate startOfLastMonth = startOfThisMonth.minusMonths(1);

        Instant thisMonthStart = startOfThisMonth.atStartOfDay().toInstant(ZoneOffset.UTC);
        Instant thisMonthEnd = now.atTime(23, 59, 59).toInstant(ZoneOffset.UTC);
        Instant lastMonthStart = startOfLastMonth.atStartOfDay().toInstant(ZoneOffset.UTC);
        Instant lastMonthEnd = startOfThisMonth.atStartOfDay().minusSeconds(1).toInstant(ZoneOffset.UTC);

        List<TeacherReviewEntity> thisMonthReviews = teacherReviewDao.getByTeacherAndDateRange(teacherId, thisMonthStart, thisMonthEnd);
        List<TeacherReviewEntity> lastMonthReviews = teacherReviewDao.getByTeacherAndDateRange(teacherId, lastMonthStart, lastMonthEnd);

        long reviewsThisMonth = thisMonthReviews.size();
        long reviewsLastMonth = lastMonthReviews.size();

        // Calculate trend
        String trendDirection = "STABLE";
        double trendPercentage = 0.0;

        if (reviewsLastMonth > 0) {
            trendPercentage = ((double) (reviewsThisMonth - reviewsLastMonth) / reviewsLastMonth) * 100;
            if (trendPercentage > 20) {
                trendDirection = "IMPROVING";
            } else if (trendPercentage < -20) {
                trendDirection = "DECLINING";
            }
        } else if (reviewsThisMonth > 0) {
            trendDirection = "IMPROVING";
            trendPercentage = 100.0;
        }

        // Find most common category
        List<TeacherReviewEntity> allReviews = teacherReviewDao.getApprovedReviewsByTeacher(teacherId);
        String mostCommonCategory = allReviews.stream()
                .filter(review -> review.getReviewCategory() != null)
                .collect(Collectors.groupingBy(TeacherReviewEntity::getReviewCategory, Collectors.counting()))
                .entrySet().stream()
                .max(java.util.Map.Entry.comparingByValue())
                .map(java.util.Map.Entry::getKey)
                .orElse("OVERALL");

        // Find best and worst rated categories
        TeacherReviewSummary.CategoryRatings categoryRatings = calculateCategoryRatings(teacherId);
        String bestRatedCategory = getBestRatedCategory(categoryRatings);
        String lowestRatedCategory = getLowestRatedCategory(categoryRatings);

        return new TeacherReviewSummary.ReviewTrends()
                .setTrendDirection(trendDirection)
                .setTrendPercentage(trendPercentage)
                .setMostCommonCategory(mostCommonCategory)
                .setBestRatedCategory(bestRatedCategory)
                .setLowestRatedCategory(lowestRatedCategory)
                .setReviewsThisMonth(reviewsThisMonth)
                .setReviewsLastMonth(reviewsLastMonth);
    }

    private String getBestRatedCategory(TeacherReviewSummary.CategoryRatings ratings) {
        double maxRating = Math.max(Math.max(ratings.getTeachingQualityRating(), ratings.getCommunicationRating()),
                                   Math.max(ratings.getHelpfulnessRating(), ratings.getOverallRating()));

        if (maxRating == ratings.getTeachingQualityRating()) return "TEACHING_QUALITY";
        if (maxRating == ratings.getCommunicationRating()) return "COMMUNICATION";
        if (maxRating == ratings.getHelpfulnessRating()) return "HELPFULNESS";
        return "OVERALL";
    }

    private String getLowestRatedCategory(TeacherReviewSummary.CategoryRatings ratings) {
        double minRating = Math.min(Math.min(ratings.getTeachingQualityRating(), ratings.getCommunicationRating()),
                                   Math.min(ratings.getHelpfulnessRating(), ratings.getOverallRating()));

        if (minRating == ratings.getTeachingQualityRating()) return "TEACHING_QUALITY";
        if (minRating == ratings.getCommunicationRating()) return "COMMUNICATION";
        if (minRating == ratings.getHelpfulnessRating()) return "HELPFULNESS";
        return "OVERALL";
    }

    /**
     * Get all reviews for a specific teacher in a specific classroom (all statuses)
     */
    public List<TeacherReview> getReviewsByTeacherAndClassroom(String teacherId, String classroomId) {
        logger.info("Getting all reviews for teacher {} in classroom {}", teacherId, classroomId);

        // Verify teacher exists
        try {
            userService.getUserById(teacherId);
        } catch (Exception e) {
            throw new GeneralException(HttpStatus.NOT_FOUND, "TEACHER_REVIEW", "TEACHER_NOT_FOUND",
                    "teacher_not_found_details", teacherId);
        }

        // Verify classroom exists and teacher owns it
        try {
            var classroom = classroomService.getClassroomById(classroomId);
            if (!teacherId.equals(classroom.getTeacherId())) {
                throw new GeneralException(HttpStatus.FORBIDDEN, "TEACHER_REVIEW", "TEACHER_NOT_AUTHORIZED_FOR_CLASSROOM",
                        "teacher_not_authorized_for_classroom", teacherId, classroomId);
            }
        } catch (GeneralException e) {
            throw e; // Re-throw authorization errors
        } catch (Exception e) {
            throw new GeneralException(HttpStatus.NOT_FOUND, "TEACHER_REVIEW", "CLASSROOM_NOT_FOUND",
                    "classroom_not_found_details", classroomId);
        }

        // Get all reviews for this teacher and classroom (regardless of status)
        List<TeacherReviewEntity> entities = teacherReviewDao.getByTeacherAndClassroom(teacherId, classroomId);

        return entities.stream()
                .map(entity -> enrichReviewWithDetails(mapper.toDto(entity)))
                .sorted((r1, r2) -> Long.compare(r2.getCreatedDate(), r1.getCreatedDate())) // Most recent first
                .collect(Collectors.toList());
    }
}
