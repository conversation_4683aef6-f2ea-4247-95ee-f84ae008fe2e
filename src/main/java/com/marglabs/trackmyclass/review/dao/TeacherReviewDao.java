package com.marglabs.trackmyclass.review.dao;

import com.marglabs.common.rest.error.GeneralException;
import com.marglabs.trackmyclass.review.entity.TeacherReviewEntity;
import com.marglabs.trackmyclass.review.repository.TeacherReviewRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

@Component
public class TeacherReviewDao {
    
    @Autowired
    private TeacherReviewRepository repository;
    
    public TeacherReviewEntity save(TeacherReviewEntity entity) {
        return repository.save(entity);
    }
    
    public TeacherReviewEntity getById(String id) {
        return repository.findById(id)
                .orElseThrow(() -> new GeneralException(HttpStatus.NOT_FOUND, "TEACHER_REVIEW", "TEACHER_REVIEW_NOT_FOUND",
                        "teacher_review_not_found_details", id));
    }
    
    public Optional<TeacherReviewEntity> findById(String id) {
        return repository.findById(id);
    }
    
    public List<TeacherReviewEntity> getByTeacherId(String teacherId) {
        return repository.findByTeacherId(teacherId);
    }
    
    public List<TeacherReviewEntity> getApprovedReviewsByTeacher(String teacherId) {
        return repository.findByTeacherIdAndStatus(teacherId, "APPROVED");
    }
    
    public List<TeacherReviewEntity> getByStudentId(String studentId) {
        return repository.findByStudentId(studentId);
    }
    
    public List<TeacherReviewEntity> getByClassroomId(String classroomId) {
        return repository.findByClassroomId(classroomId);
    }
    
    public List<TeacherReviewEntity> getByTeacherAndClassroom(String teacherId, String classroomId) {
        return repository.findByTeacherIdAndClassroomId(teacherId, classroomId);
    }
    
    public Optional<TeacherReviewEntity> findByStudentAndTeacher(String studentId, String teacherId) {
        return repository.findByStudentIdAndTeacherId(studentId, teacherId);
    }
    
    public Optional<TeacherReviewEntity> findByStudentTeacherAndClassroom(String studentId, String teacherId, String classroomId) {
        return repository.findByStudentIdAndTeacherIdAndClassroomId(studentId, teacherId, classroomId);
    }
    
    public List<TeacherReviewEntity> getByTeacherAndRating(String teacherId, Integer rating) {
        return repository.findByTeacherIdAndRating(teacherId, rating);
    }
    
    public List<TeacherReviewEntity> getByTeacherAndCategory(String teacherId, String category) {
        return repository.findByTeacherIdAndReviewCategory(teacherId, category);
    }
    
    public List<TeacherReviewEntity> getByStatus(String status) {
        return repository.findByStatus(status);
    }
    
    public List<TeacherReviewEntity> getRecentApprovedReviews(String teacherId) {
        return repository.findRecentApprovedReviewsByTeacher(teacherId);
    }
    
    public Double calculateAverageRating(String teacherId) {
        return repository.calculateAverageRatingByTeacher(teacherId);
    }
    
    public long countByTeacherAndStatus(String teacherId, String status) {
        return repository.countByTeacherIdAndStatus(teacherId, status);
    }
    
    public long countByTeacher(String teacherId) {
        return repository.countByTeacherId(teacherId);
    }
    
    public long countByTeacherAndRating(String teacherId, Integer rating) {
        return repository.countByTeacherIdAndRating(teacherId, rating);
    }
    
    public List<TeacherReviewEntity> getByTeacherAndDateRange(String teacherId, Instant startDate, Instant endDate) {
        return repository.findByTeacherIdAndDateRange(teacherId, startDate, endDate);
    }
    
    public Double calculateAverageRatingByCategory(String teacherId, String category) {
        return repository.calculateAverageRatingByTeacherAndCategory(teacherId, category);
    }
    
    public boolean canStudentReviewTeacher(String studentId, String teacherId) {
        return repository.canStudentReviewTeacher(studentId, teacherId);
    }
    
    public void deleteById(String id) {
        repository.deleteById(id);
    }
    
    public boolean existsById(String id) {
        return repository.existsById(id);
    }
}
