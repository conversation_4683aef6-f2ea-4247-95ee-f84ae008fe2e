package com.marglabs.trackmyclass.review.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.Instant;

@Entity
@Table(name = "teacher_reviews")
@Data
@Accessors(chain = true)
public class TeacherReviewEntity {
    
    @Id
    @Column(name = "id")
    private String id;
    
    @Column(name = "student_id", nullable = false)
    private String studentId;
    
    @Column(name = "teacher_id", nullable = false)
    private String teacherId;
    
    @Column(name = "classroom_id", nullable = false)
    private String classroomId;
    
    @Column(name = "rating", nullable = false)
    private Integer rating;
    
    @Column(name = "review_title", length = 200)
    private String reviewTitle;
    
    @Column(name = "review_text", nullable = false, length = 2000)
    private String reviewText;
    
    @Column(name = "review_category", length = 20)
    private String reviewCategory;
    
    @Column(name = "is_anonymous")
    private boolean isAnonymous;
    
    @Column(name = "status", length = 20, nullable = false)
    private String status; // PENDING, APPROVED, REJECTED
    
    @CreationTimestamp
    @Column(name = "created_date", nullable = false, updatable = false)
    private Instant createdDate;
    
    @UpdateTimestamp
    @Column(name = "updated_date", nullable = false)
    private Instant updatedDate;
    
    @Column(name = "approved_date")
    private Instant approvedDate;
    
    @Column(name = "approved_by")
    private String approvedBy;
}
