package com.marglabs.trackmyclass.review.repository;

import com.marglabs.trackmyclass.review.entity.TeacherReviewEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

@Repository
public interface TeacherReviewRepository extends JpaRepository<TeacherReviewEntity, String> {
    
    // Find reviews by teacher ID
    List<TeacherReviewEntity> findByTeacherId(String teacherId);
    
    // Find approved reviews by teacher ID
    List<TeacherReviewEntity> findByTeacherIdAndStatus(String teacherId, String status);
    
    // Find reviews by student ID
    List<TeacherReviewEntity> findByStudentId(String studentId);
    
    // Find reviews by classroom ID
    List<TeacherReviewEntity> findByClassroomId(String classroomId);
    
    // Find reviews by teacher and classroom
    List<TeacherReviewEntity> findByTeacherIdAndClassroomId(String teacherId, String classroomId);
    
    // Find reviews by student and teacher (to check if student already reviewed)
    Optional<TeacherReviewEntity> findByStudentIdAndTeacherId(String studentId, String teacherId);
    
    // Find reviews by student, teacher and classroom
    Optional<TeacherReviewEntity> findByStudentIdAndTeacherIdAndClassroomId(String studentId, String teacherId, String classroomId);
    
    // Find reviews by rating
    List<TeacherReviewEntity> findByTeacherIdAndRating(String teacherId, Integer rating);
    
    // Find reviews by category
    List<TeacherReviewEntity> findByTeacherIdAndReviewCategory(String teacherId, String reviewCategory);
    
    // Find reviews by status
    List<TeacherReviewEntity> findByStatus(String status);
    
    // Find recent reviews (ordered by creation date)
    @Query("SELECT r FROM TeacherReviewEntity r WHERE r.teacherId = :teacherId AND r.status = 'APPROVED' ORDER BY r.createdDate DESC")
    List<TeacherReviewEntity> findRecentApprovedReviewsByTeacher(@Param("teacherId") String teacherId);
    
    // Calculate average rating for teacher
    @Query("SELECT AVG(r.rating) FROM TeacherReviewEntity r WHERE r.teacherId = :teacherId AND r.status = 'APPROVED'")
    Double calculateAverageRatingByTeacher(@Param("teacherId") String teacherId);
    
    // Count reviews by teacher and status
    long countByTeacherIdAndStatus(String teacherId, String status);
    
    // Count reviews by teacher
    long countByTeacherId(String teacherId);
    
    // Count reviews by rating for a teacher
    @Query("SELECT COUNT(r) FROM TeacherReviewEntity r WHERE r.teacherId = :teacherId AND r.rating = :rating AND r.status = 'APPROVED'")
    long countByTeacherIdAndRating(@Param("teacherId") String teacherId, @Param("rating") Integer rating);
    
    // Find reviews within date range
    @Query("SELECT r FROM TeacherReviewEntity r WHERE r.teacherId = :teacherId AND r.createdDate BETWEEN :startDate AND :endDate")
    List<TeacherReviewEntity> findByTeacherIdAndDateRange(@Param("teacherId") String teacherId, 
                                                          @Param("startDate") Instant startDate, 
                                                          @Param("endDate") Instant endDate);
    
    // Calculate average rating by category
    @Query("SELECT AVG(r.rating) FROM TeacherReviewEntity r WHERE r.teacherId = :teacherId AND r.reviewCategory = :category AND r.status = 'APPROVED'")
    Double calculateAverageRatingByTeacherAndCategory(@Param("teacherId") String teacherId, @Param("category") String category);
    
    // Check if student can review teacher (is enrolled in teacher's class)
    @Query("SELECT COUNT(e) > 0 FROM com.marglabs.trackmyclass.enrollment.entity.EnrollmentEntity e " +
           "JOIN com.marglabs.trackmyclass.classroom.entity.ClassroomEntity c ON e.classroomId = c.id " +
           "WHERE e.userId = :studentId AND c.teacherId = :teacherId AND e.status = 'ACTIVE'")
    boolean canStudentReviewTeacher(@Param("studentId") String studentId, @Param("teacherId") String teacherId);
}
