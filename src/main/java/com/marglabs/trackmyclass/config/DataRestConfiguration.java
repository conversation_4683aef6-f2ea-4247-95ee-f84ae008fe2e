package com.marglabs.trackmyclass.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.rest.core.config.RepositoryRestConfiguration;
import org.springframework.data.rest.webmvc.config.RepositoryRestConfigurer;
import org.springframework.web.servlet.config.annotation.CorsRegistry;

@Configuration
@ConditionalOnClass(name = "org.springframework.data.rest.webmvc.config.RepositoryRestConfigurer")
public class DataRestConfiguration implements RepositoryRestConfigurer {

    @Override
    public void configureRepositoryRestConfiguration(RepositoryRestConfiguration config, CorsRegistry cors) {
        // Completely disable Spring Data REST auto-exposure
        config.disableDefaultExposure();
        
        // Set a different base path to avoid conflicts
        config.setBasePath("/api/data-rest");
        
        // Disable HATEOAS
        config.setReturnBodyOnCreate(false);
        config.setReturnBodyOnUpdate(false);
    }
}
