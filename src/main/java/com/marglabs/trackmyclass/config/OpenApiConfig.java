package com.marglabs.trackmyclass.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

import java.util.Arrays;

@Configuration
public class OpenApiConfig {

    @Value("${app.security.enabled:true}")
    private boolean securityEnabled;

    private final Environment environment;

    public OpenApiConfig(Environment environment) {
        this.environment = environment;
    }

    @Bean
    public OpenAPI customOpenAPI() {
        boolean isLocalProfile = Arrays.asList(environment.getActiveProfiles()).contains("local");
        String securityMessage = isLocalProfile && !securityEnabled ?
                "DEVELOPMENT MODE: Security is disabled. Authentication is not required." :
                "API documentation for Track My Class application";

        OpenAPI openAPI = new OpenAPI()
                .info(new Info()
                        .title("Track My Class API")
                        .description(securityMessage)
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("MargLabs")
                                .url("https://marglabs.com")
                                .email("<EMAIL>"))
                        .license(new License()
                                .name("Copyright MargLabs")
                                .url("https://marglabs.com/license")))
                .servers(Arrays.asList(
                        new Server().url("/").description("Default Server URL")));

        // Only add security requirements if security is enabled
        if (securityEnabled) {
            openAPI.addSecurityItem(new SecurityRequirement().addList("bearerAuth"))
                  .components(new Components()
                        .addSecuritySchemes("bearerAuth",
                                new SecurityScheme()
                                        .name("bearerAuth")
                                        .type(SecurityScheme.Type.HTTP)
                                        .scheme("bearer")
                                        .bearerFormat("JWT")
                                        .description("JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"")));
        }

        return openAPI;
    }
}
