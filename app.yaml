runtime: java21

env_variables:
  SPRING_PROFILES_ACTIVE: "prod"
  # Configure these environment variables in GCP App Engine
  # DB_HOST: "your-cloud-sql-instance-connection-name"
  # DB_PORT: "3306"
  # DB_NAME: "trackmyclass"
  # DB_USERNAME: "your-db-username"
  # DB_PASSWORD: "your-db-password"
  # OAUTH_ISSUER_URI: "your-oauth-issuer-uri"
  # OAUTH_CLIENT_ID: "your-oauth-client-id"
  # OAUTH_CLIENT_SECRET: "your-oauth-client-secret"

automatic_scaling:
  min_instances: 1
  max_instances: 1
  target_cpu_utilization: 0.6